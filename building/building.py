import math
import pandas as pd
import numpy as np
from stl import mesh

# Building variables
Building_Name = "Standard_Office"
tai = 20.0
tao = -4.0
t_delta = tai - tao

# location currently only used for solar PV generation
lat = 51.507
lon = -0.128
# solar PV orientation and angle
angle = 35
aspect = 0 # 0=south, 90=west, -90=east
#SolarPV_size = 4.0      # size of solar PV array in kWp

floor_height = 2.32

building_width = 35 # metres
building_length = 60 # metres
building_floor_area = building_width * building_length # metres squared
building_height = 14
building_floors = 4
building_perimeter = round((building_length + building_width) * 2,2)
building_volume = building_floor_area * building_height

glazing_ratio = 0.3
building_wall_area = building_perimeter * building_height * (1 - glazing_ratio)
building_glazing_area = building_perimeter * building_height * (glazing_ratio)

U_value_walls = 0.89
U_value_floor = 0.4
U_value_roof = 0.35
U_value_glazing = 2.0

THM_kWhpoK = 530 #530 # (KJ/m2.K) (Can't find where this number came from)
sum_UA = ((building_wall_area * U_value_walls) + (building_glazing_area * U_value_glazing) + (U_value_roof * building_floor_area) + (U_value_floor * building_floor_area)) / 1000
sum_infiltration = 0.33 * 1 * building_volume / 1000 # 1/3 * ACH * volume / 1000
BuildingHeatLoss = sum_UA + sum_infiltration
HeatingOutput = 0
CoolingOutput = 0

# Setpoint temperatures
heating_setpoint_times = [0, 6.5, 7, 17, 17.5, 24] # This is IES VE style, so set temperatures at each end of the time
heating_setpoint_values = [16, 16, 21, 21, 16, 16]
cooling_setpoint_times = [0, 6.5, 7, 17, 17.5, 24] # This is IES VE style, so set temperatures at each end of the time
cooling_setpoint_values = [25, 25, 23, 23, 25, 25]

# Thermal Systems
HeatMax_kW = 200
CoolMax_kW = 20
Fan_SFP = 2.2 # W/l/s
AHU_ACH = 6 # air changes per hour
AHU_SupplyVolume = (AHU_ACH * building_volume) / 3600
AHU_HR = 0.75 # Heat Recovery efficiency
AHU_SupPercent = 1 - AHU_HR # supply percentage

''' --------------
    Internal Gains 
    -------------- '''
''' Equipment Gains '''
Equipment_Energy_Ratio = 0.15 # The assumed amount of electricity energy which is converted to heat, double check this
#Equipment_Gains_Area = 10 # W/m2 during occupied hours
#Lighting_Gains_Area = 5 # W/m2 during occupied hours
# Try the following formula: <above_gains> * occupancy_profile * building_floor_area

''' Solar Gains '''

''' Occupancy Profile '''
occupancy = 0.0 # number of occupants in that hour segment
occupancy_gains = 0.0 # kW
occupant_gains_kW = 0.1 # 100 Watts per person heat gain
occupants_max = 10 # The maximum number of occupants, then adjusted by hour by the ratio below to create the profile
occupancy_profile = {
    0.5: 0.0, 1.0: 0.0, 1.5: 0.0, 2.0: 0.0, 2.5: 0.0, 3.0: 0.0, 3.5: 0.0,
    4.0: 0.01, 4.5: 0.01, 5.0: 0.05, 5.5: 0.05, 6.0: 0.1, 6.5: 0.1, 7.0: 0.5, 7.5: 0.5,
    8.0: 0.9, 8.5: 0.9, 9.0: 1.0, 9.5: 1.0, 10.0: 1.0, 10.5: 1.0, 11.0: 0.9, 11.5: 0.9,
    12.0: 0.9, 12.5: 0.9, 13.0: 0.9, 13.5: 0.9, 14.0: 1.0, 14.5: 1.0, 15.0: 0.9, 15.5: 0.9,
    16.0: 0.5, 16.5: 0.5, 17.0: 0.1, 17.5: 0.1, 18.0: 0.05, 18.5: 0.05, 19.0: 0.02, 19.5: 0.02,
    20.0: 0.0, 20.5: 0.0, 21.0: 0.0, 21.5: 0.0, 22.0: 0.0, 22.5: 0.0, 23.0: 0.0, 23.5: 0.0, 24.0: 0.0
}
occupancy_series = pd.Series(occupancy_profile)

''' Domestic Hot Water '''
cv_water = 4190 # volumetric specific heat of water kJ/m3.K
cp_water = 4.19 # specific heat capacity kJ/kg.K
cold_water_T = 10
DHW_T_stored = 65
DHW_T_recovery = 1.0 # typically 1-2 hours to refill hot water
DHW_cylinder_radius = 0.22
DHW_cylinder_height = 1.97
DHW_cylinder_area = (2 * math.pi * DHW_cylinder_radius * DHW_cylinder_height) + (2 * math.pi * pow(DHW_cylinder_radius,2)) # m2
DHW_cylinder_volume_m3 = math.pi * pow(DHW_cylinder_radius,2) * DHW_cylinder_height # m3
DHW_cylinder_volume_l = DHW_cylinder_volume_m3 * 1000
DHW_cylinder_max_stored_heat = (cv_water * DHW_cylinder_volume_m3 * DHW_T_stored) / 3600 # kWh
DHW_cylinder_ins_thick = 0.1
DHW_cylinder_ther_cond = 0.04
DHW_cylinder_uvalue = DHW_cylinder_ther_cond / DHW_cylinder_ins_thick
DHW_cylinder_ins_radius = 0.25
DHW_cylinder_ins_height = 2.05
DHW_cylinder_ins_area = (2 * math.pi * DHW_cylinder_ins_radius * DHW_cylinder_ins_height) + (2 * math.pi * pow(DHW_cylinder_ins_radius,2)) # m2
DHW_cylinder_heat_loss = DHW_cylinder_uvalue * DHW_cylinder_ins_area * (DHW_T_stored - 20) # 20 here is assumed ambient room temperature
DHW_cylinder_heat_loss_1k = cv_water * DHW_cylinder_volume_m3 * 1 # kJ required to reduce water in cylinder by 1K
DHW_cylinder_heat_loss_T = ((DHW_cylinder_heat_loss_1k * 1000) / DHW_cylinder_heat_loss) / 3600 # hours it takes to drop to 0
DHW_kW_recovery = (DHW_cylinder_volume_l * cp_water * (DHW_T_stored - cold_water_T)) / (DHW_T_recovery * 3600) # heat input to recover water temp

''' --------------
    Building Model (STL) 
    -------------- '''

# Define vertices (corners) of the base
base_vertices = np.array([
    [0, 0, 0],
    [building_length, 0, 0],
    [building_length, building_width, 0],
    [0, building_width, 0]
])

# Create top vertices by adding height
top_vertices = base_vertices.copy()
top_vertices[:, 2] = building_height

# Combine all vertices
vertices = np.vstack([base_vertices, top_vertices])

# Define faces
faces = np.array([
    [0, 1, 2],
    [0, 2, 3],
    [4, 5, 6],
    [4, 6, 7],
    [0, 1, 5],
    [0, 5, 4],
    [1, 2, 6],
    [1, 6, 5],
    [2, 3, 7],
    [2, 7, 6],
    [3, 0, 4],
    [3, 4, 7]
])

# Create the mesh object
building = mesh.Mesh(np.zeros(faces.shape[0], dtype=mesh.Mesh.dtype))
for i, f in enumerate(faces):
    for j in range(3):
        building.vectors[i][j] = vertices[f[j], :]
