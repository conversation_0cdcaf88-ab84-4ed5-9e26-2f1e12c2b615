''' VERSION CONTROL
v0.15 - to add NPV as an option, and a dotted vertical line as the carbon reduction value



'''
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import base64
from PIL import Image
import textwrap
import dash
from dash import dcc, html, Input, Output, State
import json
from io import StringIO

''' IW colours
171/10/56 - brand red
23/156/154 - brand teal
139/217/0 - sustainability
200/0/0 - architecture
65/30/169 - building surveying
32/143/183 - building services
'''

# CARBON TARGET CONFIGURATION
# Set your carbon emissions reduction target here (tCO2 per year)
CARBON_TARGET = 20.0  # Change this value to your desired target

# NPV CONFIGURATION
# Discount rate for NPV calculations (typical for energy efficiency projects: 3-7%)
DISCOUNT_RATE = 0.05  # 5% discount rate
USE_NPV = True  # Set to True to use NPV-based MAC, False to use simple Net Cost

# Define mapping from CSV "Colour" names to RGB strings (for Plotly)
colour_mapping = {
    "Building Surveying": "rgb(65, 30, 169)",
    "Sustainability": "rgb(139, 217, 0)",
    "Building Services": "rgb(32, 143, 183)",
    "Architecture": "rgb(200, 0, 0)"
}

LOGO_ON = False

# NPV calculation function
def calculate_npv(initial_cost, annual_savings, lifetime, discount_rate):
    """
    Calculate Net Present Value of an investment
    NPV = -Initial_Cost + Sum(Annual_Savings / (1 + discount_rate)^year) for each year
    """
    if discount_rate == 0:
        return annual_savings * lifetime - initial_cost

    # Calculate present value of annual savings over the lifetime
    pv_savings = sum(annual_savings / (1 + discount_rate) ** year for year in range(1, int(lifetime) + 1))
    return pv_savings - initial_cost

# Function to create MACC chart
def create_macc_chart(df_filtered):
    """
    Create MACC chart from filtered dataframe
    """
    if df_filtered.empty:
        # Return empty chart if no measures selected
        fig = go.Figure()
        fig.add_annotation(
            text="No measures selected. Please enable some measures and click Update.",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False, font=dict(size=16)
        )
        fig.update_layout(
            title="MACC Chart - No Data",
            xaxis=dict(title="Abatement Potential (tCO2 per year)"),
            yaxis=dict(title="MAC (£/tCO2)"),
            width=1200, height=600
        )
        return fig

    # Recalculate cumulative values for filtered data
    df_work = df_filtered.copy().sort_values(by="MAC")
    df_work["CumulativeCarbonSaved"] = df_work["LifetimeCarbonSaved"].cumsum()
    df_work["LeftEdge"] = df_work["CumulativeCarbonSaved"] - df_work["LifetimeCarbonSaved"]

    # Create chart
    fig = go.Figure()

    # Add each measure as a separate trace
    for idx, row in df_work.iterrows():
        # Create hover text with detailed information including building
        hover_text = (
            f"<b>{row['Building']}: {row['Measure']}</b><br>"
            f"MAC: £{row['MAC']:.2f}/tCO2<br>"
            f"Carbon Reduction: {row['LifetimeCarbonSaved']:.1f} tCO2/year<br>"
            f"Capital Cost: £{row['Cost']:,.0f}<br>"
            f"Annual Savings: £{row['Annual Savings']:,.0f}<br>"
            f"NPV: £{row['NPV']:,.0f}<br>"
            f"Net Cost: £{row['Net Cost']:,.0f}<br>"
            f"Payback: {row['Payback Period']:.1f} years<br>"
            f"Lifetime: {row['Lifetime']} years"
        )

        # Define rectangle coordinates for precise bar positioning
        left_edge = row['LeftEdge']
        right_edge = row['LeftEdge'] + row['LifetimeCarbonSaved']
        mac_value = row['MAC']

        # Create rectangle using scatter plot with fill
        x_coords = [left_edge, right_edge, right_edge, left_edge, left_edge]
        y_coords = [0, 0, mac_value, mac_value, 0]

        # Create the filled rectangle (no hover on this trace)
        fig.add_trace(go.Scatter(
            x=x_coords,
            y=y_coords,
            fill='toself',
            fillcolor=row['Color'],
            line=dict(color='black', width=1),
            mode='lines',
            name=f"{row['Building']}: {row['Measure']}",
            showlegend=True,
            hoverinfo='skip'  # Skip hover on the outline
        ))

        # Create a grid of invisible hover points across the entire rectangle
        width = row['LifetimeCarbonSaved']
        height = abs(mac_value)

        # Determine number of hover points based on rectangle size
        num_x_points = max(3, int(width * 2))  # At least 3 points horizontally
        num_y_points = max(3, int(height / 50))  # Scale with height

        hover_x = []
        hover_y = []

        for i in range(num_x_points):
            for j in range(num_y_points):
                x_pos = left_edge + (width * i / (num_x_points - 1))
                if mac_value >= 0:
                    y_pos = height * j / (num_y_points - 1)
                else:
                    y_pos = -height * j / (num_y_points - 1)
                hover_x.append(x_pos)
                hover_y.append(y_pos)

        # Add invisible hover points covering the entire rectangle
        fig.add_trace(go.Scatter(
            x=hover_x,
            y=hover_y,
            mode='markers',
            marker=dict(size=8, opacity=0, color=row['Color']),  # Invisible markers with color
            name=f"{row['Building']}: {row['Measure']}",
            showlegend=False,  # Don't show in legend (duplicate)
            hovertemplate=hover_text + "<extra></extra>",
            hoverinfo='text',
            hoverlabel=dict(bgcolor=row['Color'], bordercolor='white', font_color='white')
        ))

    # Add carbon target line (vertical dotted line)
    if CARBON_TARGET > 0:
        # Get approximate y-range for the target line
        y_min = df_work['MAC'].min() * 1.1 if df_work['MAC'].min() < 0 else df_work['MAC'].min() * 0.9
        y_max = df_work['MAC'].max() * 1.1

        fig.add_shape(
            type="line",
            x0=CARBON_TARGET, y0=y_min,
            x1=CARBON_TARGET, y1=y_max,
            line=dict(color="red", width=2, dash="dash"),
        )

        # Add annotation for target line
        fig.add_annotation(
            x=CARBON_TARGET,
            y=y_max * 0.9,
            text=f"Target<br>{CARBON_TARGET:.1f} tCO2/year",
            showarrow=False,
            bgcolor="white",
            bordercolor="red",
            borderwidth=1,
            font=dict(color="red", size=10)
        )

    # Add text annotations for measure names and carbon savings
    for idx, row in df_work.iterrows():
        x_center = row["LeftEdge"] + row["LifetimeCarbonSaved"] / 2

        # Add measure name above/below bar
        wrapped_label = textwrap.fill(row["Measure"], width=15)
        y_pos = row["MAC"]

        fig.add_annotation(
            x=x_center,
            y=y_pos,
            text=wrapped_label,
            showarrow=False,
            yshift=15 if y_pos >= 0 else -15,
            font=dict(size=8, color="black"),
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="rgba(0,0,0,0.2)",
            borderwidth=1
        )

        # Add carbon savings annotation near x-axis
        if USE_NPV:
            is_roi = row["NPV"] >= 0
        else:
            is_roi = row["Net Cost"] <= 0

        y_offset = 15 if is_roi else -15

        fig.add_annotation(
            x=x_center,
            y=0,
            text=f"{row['LifetimeCarbonSaved']:.1f}",
            showarrow=False,
            yshift=y_offset,
            font=dict(size=8, color="black"),
            bgcolor="rgba(255,255,255,0.8)"
        )

    # Configure chart layout and styling
    buildings = df_work['Building'].unique()
    building_list = ', '.join(buildings)
    title_suffix = f" - {cost_basis} Based" if USE_NPV else ""
    multi_building_title = f"Multi-Building MACC ({building_list})" if len(buildings) > 1 else f"MACC - {buildings[0]}"

    # Add logo as base64 encoded image
    if LOGO_ON:
        try:
            # Load and encode logo
            logo_path = "E:\\Python\\Energy Model\\BEMMS\\assets\\J&H Logo (circular).png"
            with open(logo_path, "rb") as f:
                logo_data = base64.b64encode(f.read()).decode()

            # Add logo to layout
            fig.add_layout_image(
                dict(
                    source=f"data:image/png;base64,{logo_data}",
                    xref="paper", yref="paper",
                    x=0.02, y=0.98,
                    sizex=0.1, sizey=0.1,
                    xanchor="left", yanchor="top"
                )
            )
        except:
            pass  # Continue without logo if not found

    # Update layout
    fig.update_layout(
        title=dict(
            text=f"{multi_building_title}{title_suffix}",
            x=0.5,
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Abatement Potential (tCO2 per year)",
            showgrid=False,
            zeroline=True,
            zerolinecolor='black',
            zerolinewidth=1,
            range=[-0.1, df_work['CumulativeCarbonSaved'].max() + 0.1] if not df_work.empty else [0, 1]
        ),
        yaxis=dict(
            title="MAC (£/tCO2)",
            showgrid=True,
            gridcolor='lightgray',
            gridwidth=1,
            zeroline=True,
            zerolinecolor='black',
            zerolinewidth=2
        ),
        width=1200,
        height=600,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02,
            bgcolor="rgba(255,255,255,0.8)",
            bordercolor="black",
            borderwidth=1
        ),
        hovermode='closest',
        plot_bgcolor='white',
        hoverlabel=dict(
            bgcolor="rgba(255,255,255,0.9)",
            bordercolor="black",
            font_size=12,
            font_family="Arial"
        )
    )

    return fig

# Load and prepare data
def load_and_prepare_data():
    # Load input data from CSV file
    # CSV headers: Building, Measure, Capital Cost, Carbon Reduction, Annual Savings, Lifetime, Colour
    df = pd.read_csv("MACC_Input.csv")

    # Rename 'Capital Cost' to 'Cost' for consistency
    df.rename(columns={'Capital Cost': 'Cost'}, inplace=True)

    # Map the textual "Colour" column to an RGB tuple and store in a new "Color" column
    df["Color"] = df["Colour"].map(colour_mapping)

    # Calculate payback period (years)
    df["Payback Period"] = df["Cost"] / df["Annual Savings"]

    # Calculate Net Cost, NPV & Lifetime Cost per Tonne CO2 Saved (MAC)
    df["Net Cost"] = df["Cost"] - (df["Annual Savings"] * df["Lifetime"])

    # NPV = Present Value of future savings - Initial Cost
    df["NPV"] = df.apply(lambda row: calculate_npv(row["Cost"], row["Annual Savings"],
                                                   row["Lifetime"], DISCOUNT_RATE), axis=1)

    # Total carbon saved over lifetime for each measure
    df["LifetimeCarbonSaved"] = df["Carbon Reduction"]

    # Choose between NPV-based or Net Cost-based MAC calculation
    global cost_basis
    if USE_NPV:
        df["MAC"] = (-df["NPV"] / df["Carbon Reduction"]) / df["Lifetime"]
        cost_basis = "NPV"
    else:
        df["MAC"] = (df["Net Cost"] / df["Carbon Reduction"]) / df["Lifetime"]
        cost_basis = "Net Cost"

    return df

# Load the data
df_original = load_and_prepare_data()

# Create Dash app
app = dash.Dash(__name__)

# Define app layout
app.layout = html.Div([
    html.H1("Interactive MACC Analysis", style={'textAlign': 'center'}),

    html.Div([
        html.Button('Update Chart', id='update-button', n_clicks=0,
                   style={'fontSize': '16px', 'padding': '10px 20px', 'margin': '10px',
                          'backgroundColor': '#007bff', 'color': 'white', 'border': 'none',
                          'borderRadius': '5px', 'cursor': 'pointer'}),
        html.Button('Reset All Measures', id='reset-button', n_clicks=0,
                   style={'fontSize': '16px', 'padding': '10px 20px', 'margin': '10px',
                          'backgroundColor': '#28a745', 'color': 'white', 'border': 'none',
                          'borderRadius': '5px', 'cursor': 'pointer'}),
        html.Div(id='summary-stats', style={'margin': '10px', 'fontSize': '14px'})
    ], style={'textAlign': 'center'}),

    # Export section
    html.Div([
        html.H3("Export Current View", style={'margin': '20px 0 10px 0'}),
        html.Div([
            dcc.Input(
                id='filename-input',
                type='text',
                placeholder='Enter filename (without .html)',
                value='MACC_Scenario',
                style={'fontSize': '14px', 'padding': '8px', 'margin': '5px', 'width': '300px',
                       'border': '1px solid #ccc', 'borderRadius': '4px'}
            ),
            html.Button('Export HTML', id='export-button', n_clicks=0,
                       style={'fontSize': '14px', 'padding': '8px 16px', 'margin': '5px',
                              'backgroundColor': '#ffc107', 'color': 'black', 'border': 'none',
                              'borderRadius': '4px', 'cursor': 'pointer'}),
        ], style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center'}),
        html.Div(id='export-status', style={'margin': '10px', 'fontSize': '14px', 'color': 'green'})
    ], style={'textAlign': 'center', 'backgroundColor': '#f8f9fa', 'padding': '15px', 'margin': '20px 0',
              'border': '1px solid #dee2e6', 'borderRadius': '5px'}),

    dcc.Graph(id='macc-chart', figure=create_macc_chart(df_original)),

    # Store the original data
    dcc.Store(id='original-data', data=df_original.to_json(date_format='iso', orient='split'))
])

# Callback for updating the chart
@app.callback(
    [Output('macc-chart', 'figure'),
     Output('summary-stats', 'children')],
    [Input('update-button', 'n_clicks'),
     Input('reset-button', 'n_clicks')],
    [State('macc-chart', 'figure'),
     State('original-data', 'data')]
)
def update_chart(update_clicks, reset_clicks, current_figure, original_data_json):
    # Determine which button was clicked using dash.callback_context
    ctx = dash.callback_context
    if not ctx.triggered:
        # Initial load
        df_filtered = df_original.copy()
        updated_fig = create_macc_chart(df_filtered)

        # Create summary statistics for all measures
        total_carbon = df_filtered['LifetimeCarbonSaved'].sum()
        total_cost = df_filtered['Cost'].sum()
        total_npv = df_filtered['NPV'].sum()
        num_measures = len(df_filtered)

        summary = html.Div([
            html.Span(f"Selected Measures: {num_measures}", style={'margin': '0 20px', 'fontWeight': 'bold'}),
            html.Span(f"Total Carbon Reduction: {total_carbon:.1f} tCO2/year", style={'margin': '0 20px'}),
            html.Span(f"Total Capital Cost: £{total_cost:,.0f}", style={'margin': '0 20px'}),
            html.Span(f"Total NPV: £{total_npv:,.0f}", style={'margin': '0 20px'})
        ], style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center', 'flexWrap': 'wrap'})

        return updated_fig, summary
    else:
        button_id = ctx.triggered[0]['prop_id'].split('.')[0]

        if button_id == 'reset-button':
            # Reset button clicked - show all measures
            df_filtered = df_original.copy()
            # Create updated chart with all measures visible
            updated_fig = create_macc_chart(df_filtered)

            # Create summary statistics for all measures
            total_carbon = df_filtered['LifetimeCarbonSaved'].sum()
            total_cost = df_filtered['Cost'].sum()
            total_npv = df_filtered['NPV'].sum()
            num_measures = len(df_filtered)

            summary = html.Div([
                html.Span(f"Selected Measures: {num_measures} (All measures reset)", style={'margin': '0 20px', 'fontWeight': 'bold'}),
                html.Span(f"Total Carbon Reduction: {total_carbon:.1f} tCO2/year", style={'margin': '0 20px'}),
                html.Span(f"Total Capital Cost: £{total_cost:,.0f}", style={'margin': '0 20px'}),
                html.Span(f"Total NPV: £{total_npv:,.0f}", style={'margin': '0 20px'})
            ], style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center', 'flexWrap': 'wrap'})

            return updated_fig, summary

        elif button_id == 'update-button':
            # Fix FutureWarning by using StringIO
            df_original_reload = pd.read_json(StringIO(original_data_json), orient='split')

            # Extract visibility state from current figure
            visibility_state = {}

            # Get visibility state from legend traces
            for trace in current_figure['data']:
                # Convert trace to dict if it's a Plotly object
                if hasattr(trace, 'to_plotly_json'):
                    trace_dict = trace.to_plotly_json()
                else:
                    trace_dict = trace

                if trace_dict.get('showlegend', False):  # Only check legend traces
                    trace_name = trace_dict.get('name', '')
                    # Check if trace is visible (visible can be True, False, or 'legendonly')
                    is_visible = trace_dict.get('visible', True)
                    if is_visible == 'legendonly' or is_visible == False:
                        visibility_state[trace_name] = False
                    else:
                        visibility_state[trace_name] = True

            # Filter original data based on visibility state
            visible_measures = []
            for measure_name, is_visible in visibility_state.items():
                if is_visible and ':' in measure_name:  # Format is "Building: Measure"
                    building, measure = measure_name.split(': ', 1)
                    visible_measures.append((building, measure))

            # Filter original data to only include visible measures
            if visible_measures:
                mask = df_original_reload.apply(
                    lambda row: (row['Building'], row['Measure']) in visible_measures, axis=1
                )
                df_filtered = df_original_reload[mask].copy()
            else:
                df_filtered = pd.DataFrame()  # Empty if nothing selected

            # Create updated chart
            updated_fig = create_macc_chart(df_filtered)

            # Apply visibility state to the new figure if we have one
            if visibility_state:
                for trace in updated_fig['data']:
                    # Convert trace to dict if it's a Plotly object
                    if hasattr(trace, 'to_plotly_json'):
                        trace_dict = trace.to_plotly_json()
                        # Update the actual trace object
                        if trace_dict.get('showlegend', False):
                            trace_name = trace_dict.get('name', '')
                            if trace_name in visibility_state:
                                if not visibility_state[trace_name]:
                                    trace.visible = 'legendonly'
                    else:
                        # It's already a dict
                        if trace.get('showlegend', False):
                            trace_name = trace.get('name', '')
                            if trace_name in visibility_state:
                                if not visibility_state[trace_name]:
                                    trace['visible'] = 'legendonly'

            # Create summary statistics
            if not df_filtered.empty:
                total_carbon = df_filtered['LifetimeCarbonSaved'].sum()
                total_cost = df_filtered['Cost'].sum()
                total_npv = df_filtered['NPV'].sum()
                num_measures = len(df_filtered)

                summary = html.Div([
                    html.Span(f"Selected Measures: {num_measures}", style={'margin': '0 20px', 'fontWeight': 'bold'}),
                    html.Span(f"Total Carbon Reduction: {total_carbon:.1f} tCO2/year", style={'margin': '0 20px'}),
                    html.Span(f"Total Capital Cost: £{total_cost:,.0f}", style={'margin': '0 20px'}),
                    html.Span(f"Total NPV: £{total_npv:,.0f}", style={'margin': '0 20px'})
                ], style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center', 'flexWrap': 'wrap'})
            else:
                summary = html.P("No measures selected")

            return updated_fig, summary

        else:
            # Fallback case - shouldn't happen but ensures we always return something
            df_filtered = df_original.copy()
            updated_fig = create_macc_chart(df_filtered)
            summary = html.P("Unexpected button click - showing all measures")
            return updated_fig, summary

# Callback for exporting current view
@app.callback(
    Output('export-status', 'children'),
    [Input('export-button', 'n_clicks')],
    [State('filename-input', 'value'),
     State('macc-chart', 'figure'),
     State('original-data', 'data')]
)
def export_current_view(n_clicks, filename, current_figure, original_data_json):
    if n_clicks == 0:
        return ""

    try:
        # Fix FutureWarning by using StringIO
        df_original_reload = pd.read_json(StringIO(original_data_json), orient='split')

        # Extract visibility state from current figure
        visible_measures = []

        # Get visibility state from legend traces
        for trace in current_figure['data']:
            # Convert trace to dict if it's a Plotly object
            if hasattr(trace, 'to_plotly_json'):
                trace_dict = trace.to_plotly_json()
            else:
                trace_dict = trace

            if trace_dict.get('showlegend', False):  # Only check legend traces
                trace_name = trace_dict.get('name', '')
                # Check if trace is visible (visible can be True, False, or 'legendonly')
                is_visible = trace_dict.get('visible', True)
                if is_visible != 'legendonly' and is_visible != False:
                    if ':' in trace_name:  # Format is "Building: Measure"
                        building, measure = trace_name.split(': ', 1)
                        visible_measures.append((building, measure))

        # Filter original data to only include visible measures
        if visible_measures:
            mask = df_original_reload.apply(
                lambda row: (row['Building'], row['Measure']) in visible_measures, axis=1
            )
            df_filtered = df_original_reload[mask].copy()
        else:
            df_filtered = pd.DataFrame()  # Empty if nothing selected

        # Create chart for export (without interactive buttons)
        export_fig = create_macc_chart(df_filtered)

        # Clean filename
        clean_filename = filename.strip()
        if not clean_filename:
            clean_filename = "MACC_Export"

        # Remove any invalid characters for filename
        import re
        clean_filename = re.sub(r'[<>:"/\\|?*]', '_', clean_filename)

        # Add .html extension if not present
        if not clean_filename.endswith('.html'):
            clean_filename += '.html'

        # Export the chart
        export_fig.write_html(clean_filename)

        # Create success message with statistics
        if not df_filtered.empty:
            num_measures = len(df_filtered)
            total_carbon = df_filtered['LifetimeCarbonSaved'].sum()
            total_cost = df_filtered['Cost'].sum()

            return html.Div([
                html.P(f"✅ Successfully exported: {clean_filename}", style={'fontWeight': 'bold'}),
                html.P(f"Exported {num_measures} measures with {total_carbon:.1f} tCO2/year reduction"),
                html.P(f"Total cost: £{total_cost:,.0f}")
            ])
        else:
            return html.P("⚠️ No measures selected for export", style={'color': 'orange'})

    except Exception as e:
        return html.P(f"❌ Export failed: {str(e)}", style={'color': 'red'})

# Run the app
if __name__ == '__main__':
    print("Starting Interactive MACC Dashboard...")
    print("Open your browser and go to: http://127.0.0.1:8050")
    app.run(debug=True, port=8050)


