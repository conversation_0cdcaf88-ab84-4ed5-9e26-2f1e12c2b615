from flask import Flask, render_template, request, redirect, url_for, session
import os, csv, json, time, math, statistics
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import folium
import subprocess
import sys
import pickle
import tempfile
import uuid

import webbrowser
import os
from pathlib import Path
from geopy.geocoders import Nominatim
from folium.plugins import FastMarkerCluster
import plotly.offline as pyo
import plotly.graph_objects as go

app = Flask(__name__)
app.secret_key = 'your-secret-key'  # Needed for session management

# Create a temporary directory for storing large data files
TEMP_DATA_DIR = tempfile.mkdtemp(prefix='flask_app_data_')
print(f"Temporary data directory: {TEMP_DATA_DIR}")

def save_large_data(data, data_type="general"):
    """Save large data to a temporary file and return the file ID"""
    file_id = str(uuid.uuid4())
    file_path = os.path.join(TEMP_DATA_DIR, f"{data_type}_{file_id}.pkl")

    with open(file_path, 'wb') as f:
        pickle.dump(data, f)

    return file_id

def load_large_data(file_id, data_type="general"):
    """Load large data from a temporary file using the file ID"""
    file_path = os.path.join(TEMP_DATA_DIR, f"{data_type}_{file_id}.pkl")

    if not os.path.exists(file_path):
        return None

    try:
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def cleanup_old_files():
    """Clean up old temporary files (older than 1 hour)"""
    import time
    current_time = time.time()

    for filename in os.listdir(TEMP_DATA_DIR):
        file_path = os.path.join(TEMP_DATA_DIR, filename)
        if os.path.isfile(file_path):
            file_age = current_time - os.path.getctime(file_path)
            if file_age > 3600:  # 1 hour
                try:
                    os.remove(file_path)
                    print(f"Cleaned up old file: {filename}")
                except Exception as e:
                    print(f"Error cleaning up file {filename}: {e}")


# Dummy simulation function: replace with your actual simulation logic.
def run_simulation(building_params, battery_params, solar_params, multi_simulations, simulation_count):
    # (Here you would call your energy model simulation,
    #  write out CSV files (e.g., into a "simulation_results" subfolder),
    #  generate Plotly charts, etc.)
    #
    # For demonstration, we generate a sample Plotly chart and a sample Folium map.
    # Generate a sample Plotly chart.
    dates = [datetime(2023, 1, 1) + timedelta(days=i) for i in range(30)]
    usage = np.random.randint(100, 200, 30)
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=dates, y=usage, mode='lines+markers', name='Electricity Usage'))
    plot_html = fig.to_html(full_html=False)

    # Generate a sample Folium map.
    m = folium.Map(location=[51.5074, -0.1278], zoom_start=12)
    folium.Marker(location=[51.5074, -0.1278], popup="London HQ").add_to(m)
    map_html = m._repr_html_()

    # (Optionally, you can simulate multi‑simulation CSV output by saving files in a subfolder.)
    os.makedirs('simulation_results', exist_ok=True)
    output_filename = os.path.join('simulation_results', 'Standard_Office_1_Annual Data.csv')
    with open(output_filename, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=['Day Number', 'Electricity Usage'])
        writer.writeheader()
        for i in range(30):
            writer.writerow({'Day Number': i + 1, 'Electricity Usage': int(usage[i])})

    return plot_html, map_html


# Home page: replicates the main menu of your tkinter GUI.
@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        # Save all input parameters into the session
        session['building_params'] = {
            'floor_area': request.form.get('floor_area'),
            'building_height': request.form.get('building_height')
        }
        session['solar_params'] = {
            'SolarPV_size': request.form.get('SolarPV_size'),
            'orientation': request.form.get('orientation')
        }
        session['battery_params'] = {
            'bat_num': request.form.get('bat_num'),
            'bat_capacity': request.form.get('bat_capacity')
        }
        session['multi_simulations'] = (request.form.get('multi_simulations') == 'on')
        session['simulation_count'] = int(request.form.get('simulation_count') or 1)
        return redirect(url_for('run_simulation_route'))
    return render_template('index.html')


# Route that runs the simulation.
@app.route('/run_simulation')
def run_simulation_route():
    # Clean up old files before processing
    cleanup_old_files()

    building_params = session.get('building_params', {})
    battery_params = session.get('battery_params', {})
    solar_params = session.get('solar_params', {})
    multi_simulations = session.get('multi_simulations', False)
    simulation_count = session.get('simulation_count', 1)

    plot_html, map_html = run_simulation(building_params, battery_params, solar_params,
                                         multi_simulations, simulation_count)

    # Save large data to temporary files instead of session
    plot_file_id = save_large_data(plot_html, "plot")
    map_file_id = save_large_data(map_html, "map")

    # Store only the file IDs in session (much smaller)
    session['plot_file_id'] = plot_file_id
    session['map_file_id'] = map_file_id

    # Clear any old large data from session
    session.pop('plot_html', None)
    session.pop('map_html', None)

    return redirect(url_for('results'))


# Results page: shows the Plotly chart and the Folium map.
@app.route('/results')
def results():
    # Load data from temporary files using file IDs
    plot_file_id = session.get('plot_file_id')
    map_file_id = session.get('map_file_id')

    plot_html = ''
    map_html = ''

    if plot_file_id:
        plot_html = load_large_data(plot_file_id, "plot") or ''

    if map_file_id:
        map_html = load_large_data(map_file_id, "map") or ''

    # Fallback to session data if file loading fails (backward compatibility)
    if not plot_html:
        plot_html = session.get('plot_html', '')
    if not map_html:
        map_html = session.get('map_html', '')

    return render_template('results.html', plot_html=plot_html, map_html=map_html)


# Portfolio map route: replicates your Portfolio_Map.py functionality.
@app.route('/portfolio_map')
def portfolio_map():
    # -------------------------------
    # Load (or create) the geocoding cache.
    cache_file = "geocode_cache.json"
    if os.path.exists(cache_file):
        with open(cache_file, "r", encoding="utf-8") as cf:
            geocode_cache = json.load(cf)
    else:
        geocode_cache = {}

    # Initialize the geocoder.
    geolocator = Nominatim(user_agent="energy_model_app")

    def haversine(lon1, lat1, lon2, lat2):
        from math import radians, sin, cos, sqrt, atan2
        R = 6371
        dlon = radians(lon2 - lon1)
        dlat = radians(lat2 - lat1)
        a = sin(dlat / 2) ** 2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))
        return R * c

    # -------------------------------
    # Load building basic info from Portfolio.csv.
    portfolio_csv_path = os.path.join("Portfolio", "Portfolio.csv")
    buildings = []
    with open(portfolio_csv_path, "r", newline="", encoding="utf-8-sig") as f:
        reader = csv.DictReader(f)
        for row in reader:
            if "Name" not in row or not row["Name"].strip():
                continue
            building = {}
            building["name"] = row["Name"].strip()
            postcode = row["Postcode"].strip()
            # Check cache first.
            if postcode in geocode_cache:
                location = geocode_cache[postcode]
                print(f"Loaded geocode for {postcode} from cache: {location}")
            else:
                try:
                    location_obj = geolocator.geocode(postcode)
                    time.sleep(1)  # Respect usage policy.
                except Exception as e:
                    location_obj = None
                if location_obj is not None:
                    location = {"lat": location_obj.latitude, "lon": location_obj.longitude}
                    geocode_cache[postcode] = location
                    with open(cache_file, "w", encoding="utf-8") as cf:
                        json.dump(geocode_cache, cf)
                    print(f"Geocoded {postcode}: {location}")
                else:
                    print(f"Warning: Could not geocode postcode {postcode} for building {building['name']}")
                    location = None
            if location is None:
                continue
            building["lat"] = location["lat"]
            building["lon"] = location["lon"]

            building["area"] = float(row["GIA"])  # already in m²
            building["floors"] = int(row["Floors"])
            building["annual_elec"] = int(row["Annual Electricity Consumption"])
            building["annual_gas"] = int(row["Annual Gas Consumption"])
            building["epc"] = row["EPC Rating"].strip()

            # Define CSV file paths.
            daily_filename = os.path.join("Portfolio", f"{building['name']}_daily.csv")
            monthly_filename = os.path.join("Portfolio", f"{building['name']}_monthly.csv")

            # Check for daily data first.
            if os.path.exists(daily_filename):
                building["source_frequency"] = "daily"
                daily_records = []
                with open(daily_filename, "r", newline="", encoding="utf-8-sig") as df:
                    dreader = csv.DictReader(df)
                    for drow in dreader:
                        date_str = drow["Date"].strip()  # Expected "dd/mm/yyyy"
                        try:
                            dt_obj = datetime.strptime(date_str, "%d/%m/%Y")
                        except Exception as e:
                            continue
                        try:
                            elec = int(drow["Electricity"])
                        except:
                            elec = None
                        try:
                            gas = int(drow["Gas"])
                        except:
                            gas = None
                        daily_records.append({
                            "date_str": date_str,
                            "dt_obj": dt_obj,
                            "elec": elec,
                            "gas": gas
                        })
                building["daily_records"] = daily_records
                building["daily_dates"] = [r["date_str"] for r in daily_records]
                building["daily_elec"] = [r["elec"] for r in daily_records]
                building["daily_gas"] = [r["gas"] for r in daily_records]

                # Calculate monthly data from daily records.
                monthly_agg = {}
                for rec in daily_records:
                    month_key = rec["dt_obj"].strftime("%b %Y")
                    if month_key not in monthly_agg:
                        monthly_agg[month_key] = {"elec": 0, "gas": 0}
                    if rec["elec"] is not None:
                        monthly_agg[month_key]["elec"] += rec["elec"]
                    if rec["gas"] is not None:
                        monthly_agg[month_key]["gas"] += rec["gas"]
                sorted_months = sorted(monthly_agg.keys(), key=lambda m: datetime.strptime(m, "%b %Y"))
                monthly_labels = []
                monthly_elec = []
                monthly_gas = []
                for m in sorted_months:
                    monthly_labels.append(m)
                    monthly_elec.append(monthly_agg[m]["elec"])
                    monthly_gas.append(monthly_agg[m]["gas"])
                building["aggregated_monthly"] = {"months": monthly_labels, "elec": monthly_elec, "gas": monthly_gas}

            # Otherwise, if no daily data exists, check for monthly data.
            elif os.path.exists(monthly_filename):
                building["source_frequency"] = "monthly"
                monthly_data = {}
                with open(monthly_filename, "r", newline="", encoding="utf-8-sig") as mf:
                    mreader = csv.DictReader(mf)
                    for mrow in mreader:
                        month = mrow["Month"].strip()
                        try:
                            elec = int(mrow["Electricity"])
                        except:
                            elec = None
                        try:
                            gas = int(mrow["Gas"])
                        except:
                            gas = None
                        monthly_data[month] = {"Electricity": elec, "Gas": gas}
                # Use a fixed month order.
                months_order = ["January", "February", "March", "April", "May", "June",
                                "July", "August", "September", "October", "November", "December"]
                monthly_labels = []
                monthly_elec = []
                monthly_gas = []
                for m in months_order:
                    if m in monthly_data:
                        monthly_labels.append(m)
                        monthly_elec.append(monthly_data[m]["Electricity"])
                        monthly_gas.append(monthly_data[m]["Gas"])
                building["monthly_labels"] = monthly_labels
                building["monthly_elec"] = monthly_elec
                building["monthly_gas"] = monthly_gas
            else:
                building["source_frequency"] = None

            buildings.append(building)

    # -------------------------------
    # Create a Folium map centered over London.
    if buildings:
        m = folium.Map(location=[buildings[0]["lat"], buildings[0]["lon"]], zoom_start=12)
    else:
        m = folium.Map(location=[51.5074, -0.1278], zoom_start=12)
    hq = buildings[0] if buildings else None

    # Create a FastMarkerCluster.
    marker_cluster = FastMarkerCluster(data=[]).add_to(m)

    # -------------------------------
    # Build popups for each building.
    # (Half-hourly functionality has been removed.)
    for i, building in enumerate(buildings):
        b_id = f"b{i}"
        area_m2 = building["area"]
        area_formatted = f"{area_m2:,.0f}"
        floors_formatted = f"{building['floors']:,}"
        annual_elec_formatted = f"{building['annual_elec']:,}"
        annual_gas_formatted = f"{building['annual_gas']:,}"

        if building.get("source_frequency") == "daily":
            daily_labels_js = json.dumps(building["daily_dates"])
            daily_elec_js = json.dumps(building["daily_elec"])
            daily_gas_js = json.dumps(building["daily_gas"])
            monthly_labels_js = json.dumps(building["aggregated_monthly"]["months"])
            monthly_elec_js = json.dumps(building["aggregated_monthly"]["elec"])
            monthly_gas_js = json.dumps(building["aggregated_monthly"]["gas"])
            available_buttons = {"daily": True, "monthly": True}
        elif building.get("source_frequency") == "monthly":
            monthly_labels_js = json.dumps(building["monthly_labels"])
            monthly_elec_js = json.dumps(building["monthly_elec"])
            monthly_gas_js = json.dumps(building["monthly_gas"])
            available_buttons = {"monthly": True}
        else:
            available_buttons = {"daily": False, "monthly": False}

        daily_button_html = ""
        monthly_button_html = ""
        daily_detail_div = ""
        monthly_detail_div = ""

        if available_buttons.get("daily"):
            daily_button_html = f'<button onclick="showDetail(\'{b_id}\', \'daily\')">View Daily Consumption</button>'
            daily_detail_div = f"""
        <div id="detail-content-{b_id}-daily" style="display:none; border:1px solid #ccc; border-radius:5px; background-color:white; padding:10px; font-family:'Century Gothic', sans-serif;">
          <h4 style="background-color:#e6f7ff; padding:5px; margin:0; font-size:20px; text-decoration:underline;">Daily Consumption for {building['name']}</h4>
          <div style="width:400px; height:300px;">
            <canvas id="chart-{b_id}-daily" width="400" height="300"></canvas>
          </div>
          <br>
          <button onclick="hideDetail('{b_id}', 'daily')">Back</button>
        </div>
        """
        if available_buttons.get("monthly"):
            monthly_button_html = f'<button onclick="showDetail(\'{b_id}\', \'monthly\')">View Monthly Consumption</button>'
            monthly_detail_div = f"""
        <div id="detail-content-{b_id}-monthly" style="display:none; border:1px solid #ccc; border-radius:5px; background-color:white; padding:10px; font-family:'Century Gothic', sans-serif;">
          <h4 style="background-color:#e6f7ff; padding:5px; margin:0; font-size:20px; text-decoration:underline;">Monthly Consumption for {building['name']}</h4>
          <div style="width:400px; height:300px;">
            <canvas id="chart-{b_id}-monthly" width="400" height="300"></canvas>
          </div>
          <br>
          <button onclick="hideDetail('{b_id}', 'monthly')">Back</button>
        </div>
        """

        consumption_buttons = " ".join([btn for btn in [daily_button_html, monthly_button_html] if btn])

        # Popup with updated styling: white background overall; header with dark turquoise background, white bold text.
        popup_html = f"""
        <!-- Load Chart.js -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
          console.log("Chart.js loaded:", Chart);
        </script>
        <div id="popup-container-{b_id}" style="border:1px solid #ccc; border-radius:5px; background-color:white; font-family:'Century Gothic', sans-serif;">
          <div id="header-{b_id}" style="background-color:#008080; padding:10px; border-top-left-radius:5px; border-top-right-radius:5px;">
            <h4 style="color:white; font-weight:bold; margin:0; font-size:20px;">{building['name']}</h4>
          </div>
          <div id="content-{b_id}" style="padding:10px;">
            <p><b>Gross Internal Area:</b> {area_formatted} m<sup>2</sup></p>
            <p><b>Number of Floors:</b> {floors_formatted}</p>
            <p><b>Annual Electricity Consumption:</b> {annual_elec_formatted} kWh</p>
            <p><b>Annual Gas Consumption:</b> {annual_gas_formatted} kWh</p>
            <p><b>EPC Rating:</b> {building['epc']}</p>
            {consumption_buttons}
          </div>
        </div>
        {daily_detail_div}
        {monthly_detail_div}
        <script>
        function showDetail(b_id, freq) {{
            document.getElementById("popup-container-" + b_id).style.display = "none";
            document.getElementById("detail-content-" + b_id + "-" + freq).style.display = "block";
            if(!window["chartInitialized_" + b_id + "_" + freq]) {{
                var ctx = document.getElementById("chart-" + b_id + "-" + freq).getContext('2d');
                var chart;
                if(freq === "daily") {{
                    chart = new Chart(ctx, {{
                        type: 'line',
                        data: {{
                            labels: {daily_labels_js},
                            datasets: [
                                {{
                                    label: 'Electricity Consumption',
                                    data: {daily_elec_js},
                                    fill: false,
                                    borderColor: 'blue',
                                    pointRadius: 0
                                }},
                                {{
                                    label: 'Gas Consumption',
                                    data: {daily_gas_js},
                                    fill: false,
                                    borderColor: 'red',
                                    pointRadius: 0
                                }}
                            ]
                        }},
                        options: {{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {{
                                x: {{
                                    ticks: {{
                                        autoSkip: true,
                                        maxTicksLimit: 10,
                                        callback: function(value, index, values) {{
                                            var parts = value.split('/');
                                            if(parts.length === 3 && parts[0] === "01") {{
                                                var monthMap = {{
                                                    "01": "January", "02": "February", "03": "March", "04": "April",
                                                    "05": "May", "06": "June", "07": "July", "08": "August",
                                                    "09": "September", "10": "October", "11": "November", "12": "December"
                                                }};
                                                return monthMap[parts[1]];
                                            }} else {{
                                                return "";
                                            }}
                                        }}
                                    }}
                                }},
                                y: {{
                                    beginAtZero: true
                                }}
                            }},
                            plugins: {{
                                tooltip: {{
                                    callbacks: {{
                                        label: function(context) {{
                                            var label = context.dataset.label || '';
                                            if(label) {{
                                                label += ': ';
                                            }}
                                            if(context.parsed.y !== null) {{
                                                label += context.parsed.y.toLocaleString();
                                            }}
                                            return label;
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }});
                }} else if(freq === "monthly") {{
                    chart = new Chart(ctx, {{
                        type: 'line',
                        data: {{
                            labels: {monthly_labels_js},
                            datasets: [
                                {{
                                    label: 'Electricity Consumption',
                                    data: {monthly_elec_js},
                                    fill: false,
                                    borderColor: 'blue'
                                }},
                                {{
                                    label: 'Gas Consumption',
                                    data: {monthly_gas_js},
                                    fill: false,
                                    borderColor: 'red'
                                }}
                            ]
                        }},
                        options: {{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {{
                                x: {{
                                    ticks: {{ autoSkip: false }}
                                }},
                                y: {{
                                    beginAtZero: true
                                }}
                            }},
                            plugins: {{
                                tooltip: {{
                                    callbacks: {{
                                        label: function(context) {{
                                            var label = context.dataset.label || '';
                                            if(label) {{
                                                label += ': ';
                                            }}
                                            if(context.parsed.y !== null) {{
                                                label += context.parsed.y.toLocaleString();
                                            }}
                                            return label;
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }});
                }}
                window["chartInitialized_" + b_id + "_" + freq] = true;
                window["chart_" + b_id + "_" + freq] = chart;
            }} else {{
                window["chart_" + b_id + "_" + freq].resize();
            }}
        }}
        function hideDetail(b_id, freq) {{
            document.getElementById("detail-content-" + b_id + "-" + freq).style.display = "none";
            document.getElementById("popup-container-" + b_id).style.display = "block";
        }}
        </script>
        """
        iframe = folium.IFrame(html=popup_html, width=450, height=450)
        popup = folium.Popup(iframe, max_width=450)
        marker = folium.Marker(
            location=[building["lat"], building["lon"]],
            popup=popup,
            tooltip=building["name"],
            icon=folium.Icon(color="red" if i == 0 else "blue", icon="info-sign")
        )
        marker_cluster.add_child(marker)
        if i != 0 and hq is not None:
            distance = haversine(hq["lon"], hq["lat"], building["lon"], building["lat"])
            folium.PolyLine(
                locations=[[hq["lat"], hq["lon"]], [building["lat"], building["lon"]]],
                tooltip=f"Distance: {distance:,.2f} km",
                color="green"
            ).add_to(m)

    m.save("london_buildings_portfolio.html")
    print("Map has been saved to london_buildings_portfolio.html")
    #webbrowser.open("london_buildings_portfolio.html")

    # Save large map data to temporary file instead of passing directly
    map_html = m._repr_html_()
    map_file_id = save_large_data(map_html, "portfolio_map")

    # Store only the file ID in session
    session['portfolio_map_file_id'] = map_file_id

    # Load the map data immediately for rendering (since we just saved it)
    return render_template('portfolio_map.html', map_html=map_html)


# Route to manually clear session and cleanup files
@app.route('/clear_session')
def clear_session():
    """Clear session data and cleanup associated temporary files"""
    # Get file IDs before clearing session
    plot_file_id = session.get('plot_file_id')
    map_file_id = session.get('map_file_id')
    portfolio_map_file_id = session.get('portfolio_map_file_id')

    # Remove associated temporary files
    for file_id, data_type in [(plot_file_id, "plot"), (map_file_id, "map"), (portfolio_map_file_id, "portfolio_map")]:
        if file_id:
            file_path = os.path.join(TEMP_DATA_DIR, f"{data_type}_{file_id}.pkl")
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"Removed temporary file: {data_type}_{file_id}.pkl")
            except Exception as e:
                print(f"Error removing file {data_type}_{file_id}.pkl: {e}")

    # Clear session
    session.clear()
    return redirect(url_for('index'))

if __name__ == '__main__':
    # Clean up any existing temporary files on startup
    cleanup_old_files()
    print("Flask app starting with server-side data storage...")
    app.run(debug=True)
