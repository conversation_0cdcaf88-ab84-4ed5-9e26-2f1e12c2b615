import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import datetime

# Generate some data for the plot
start_date = datetime.datetime(2023, 1, 1)
x = [start_date + datetime.timedelta(hours=i) for i in range(24*365)]
y = [20 + 10 * (i % 24) / 24 for i in range(24*365)]  # Example temperature data

# Create the figure and axis objects
fig, ax = plt.subplots()

# Plot the data
ax.plot(x, y)

# Set the x-axis formatter to display day number and weekday
days = mdates.DayLocator()
days_fmt = mdates.DateFormatter('%d\n%a')
ax.xaxis.set_major_locator(days)
ax.xaxis.set_major_formatter(days_fmt)

# Set the x-axis label and y-axis label
ax.set_xlabel("Day")
ax.set_ylabel("Ambient air temperature (°C)")

# Display the plot
plt.show()
