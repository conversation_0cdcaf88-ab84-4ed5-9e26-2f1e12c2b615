""""
Version History
--- working ---

line 558 breaks for year 2021, but not 2018 or 2019 - even though header 'year' doesn't match..?
import formulas such as U-values and thermal capacity

Battery efficiency now extrapolates based on ambient air temperature

v0.10
Only downloads National Grid API data if the CSV does not already exist (Previously was downloading every time)
HTC_calc is imported and running

--- Description ---
This is a python based energy building model, developed further from my Master's degree in which I developed an excel based model
The Excel model takes historic hourly electricity and gas usage, combines with this hourly solar radiance and ambient air temperature
 and carbon intensity from the National Grid.
The model then balances this electricity consumption against theoretical solar PV arrays and battery storage to balance across a time-of-use
 tariff and carbon intensity to reduce annual energy costs and carbon emissions.
The model also included a basic thermal model of heat loss based on U-values and thermal capacity, heating and cooling loads, AHU,
 DHW, internal gains such as occupants, equipment and solar gains, and electric vehicle charging profiles.

"""

import csv
from datetime import date
from datetime import time
from datetime import datetime
from random import randrange
import statistics
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import statistics
import requests
from io import StringIO

# from lightwood.api.high_level import (
#     ProblemDefinition,
#     json_ai_from_problem,
#     code_from_json_ai,
#     predictor_from_code,
# )

print(f'Setting variables...')

# Use upper case LETTERS for defining a constant that never changes
from typing import List, Any

ML = False

#day = 0
#prevday = 0

row_num = 0
# Figures setup
prevday_dict = {}
day_dict = {}# list can have a mixture of types eg integers, strings
nextday_dict = {}       # order of dictionary is not important
# order of list is significant
# day_list[0] looks first item in list
day_list = []
daily_temp = [1]        # stores all daily temperatures (8760 floats)
elec = []
average_temp = [1]      # for some reason the list needed a value in here to append
average_elec = [1]
average_carint = [1]

# Days and calendar setup
year = 2019
days_week = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun']
week_num = 1
day = 1
#prevday = 0
time_increment = 0.5 # change this depending on half-hourly or hourly data
hour_count = time_increment # the above changes this, and the end of the row_writer trigger
hour_increment = 0.5

Time_periods = np.arange(0.5, 24.5, 0.5).tolist()  # This line replaced a long manual list above. Lovely.

Stats = {'Day Number': '',
        'Week Number': '',
        'Day of Week': '',
        'Day of Week Number': '',
        'Date':'',
        'Hour': '',
        'Ambient Temperature': '',
        'Electricity Usage': '',
        'Carbon Intensity': '',
        'Electricity Cost': '',
        'Solar Irradiance' : '',
        'Solar Production': '',
        'Gas': '',
        'Heating': '',
        'Hot Water': '',
        'Cooking': '',
        'Rank Ascending': '',
        'Rank Descending': '',
        'Electricity Cost Scen A': '',
        'Electricity Carbon Emissions Scen A': '',
        'Gas Cost Scen A': '',
        'Gas Carbon Emissions Scen A': '',
        'Excess Solar Production': '',
        'Efficiency Losses': '',
        'Battery SoC': '',
        'Export Electricity': '',
        'Battery Status': ''
}

D = dict.fromkeys(Time_periods, Stats)
Year_days = list(range(1, 366)) # Not sure why, but 365 here errors out
Y = dict.fromkeys(Year_days, D)
car_int_as = dict.fromkeys(Year_days, D)
car_int_ds = dict.fromkeys(Year_days, D)

if year == 2019:  # This will change depending on the year. eg. 2019 started on a Tuesday
    year_start_day = 'Tue'
    #day_of_week = 'Tue'
    year_start_day_num = 2
    #days_week_num = 2
elif year == 2018:
    year_start_day = 'Mon'
    #day_of_week = 'Mon'
    year_start_day_num = 1
    #days_week_num = 1
elif year == 2021:
    year_start_day = 'Fri'
    year_start_day_num = 5
elif year == 2020:
    year_start_day = 'Wed'
    year_start_day_num = 3

day_of_week = year_start_day
days_week_num = year_start_day_num

# show information
show_htc = False

# General variables
global_warming = 0.0
Hour = 0
Year_NG = 0
Temperature = 0.0
Electricity_Usage = 0.0
Electricity_Usage_B = 0.0
Excess_Solar_Production = 0.0
Gas = 0.0
Sol_IR = 0.0
SolPV_prod = 0.0
Elec_CarInt = 0.0
Elec_Cost = 0.0
day_breakdown = randrange(364)     # This is the day we use to breakdown for more info eg. temp, elec etc
Carbon_Int = [1]
Carbon_Int_dict = {}        # trying this to label high/low carbon intensity
Carbon_Int_sort = [1]    # this will be used to sort the daily carbon emissions
Carbon_Int_sortR = [1]   # this will be used as above from descending order
Temperature_list = [1]
Electricity_list = [1]

sum_Electricity_Usage = 0.0
sum_Electricity_Usage_B = 0.0

AmbTemp = [1] # pre corrected hourly ambient air temperature
AmbTempCor = [1]  # The corrected ambient air temperature to half hourly average difference from hourly data

# Induction hob
ind_hob_cooking = 0.0
ind_hob_efficiency = 0.8
gas_hob_efficiency = 0.4

# Heat Transfer Coefficients
tsp = 21 # temperature set point internal
dot = -3 # design outside temperature
boiler_ef = 0.85 # Assumed

daily_temp = []
daily_energy = 0
htc_list = []
htc_final = 0.0

# Building variables
tai = 20.0
tao = -4.0
t_delta = tai - tao

floor_height = 2.32

# Battery figures
Battery_Charge = 0.0
bat_num = 3          # Number of site batteries
bat_capacity = 10 # kWh
Max_Capacity = bat_num * bat_capacity
car_int = 1          # carbon intensity period for ranking method, currently 1 but will be adjusted for energy used
car_int_list = []   # carbon intensity list used for ranking method
Max_Charge = 5 # kW
Max_Charge_Capacity = bat_num * Max_Charge
Max_Discharge = Max_Charge
Max_Discharge_Capacity = Max_Charge_Capacity
#Battery_efficiency = 0.99 # This will change based on ambient temperature
Battery_SoC = 0.0 # Start batteries empty. Set to Max_Charge_Capacity to start filled
SoC_Excess = 0.0
Battery_Status = "None"
Export_Electricity = 0.0 # Electricity exported if excess battery charge or solar production

# Battery boolean checks
bat_SOC_full = False
solprod_exceeds_usage = False
low_carbon_period = False
high_carbon_period = False

# Solar PV
SolarPV_size = 4.0      # size of solar PV array in kWp
Daily_SolPV_prod = [1]
Gas_Convert = 11.1868 # m3 to kWh converter
Gas_CarInt = 230 # grams CO2 per kWh equivalent - Check source here
Gas_Cost = 4.0 # pence per kWh

# Building Model
heat_loss = 0.0

# Smart Export Guarantee (SEG)
# Will this fixed, or variable?
SEG = 5.5 # currently fixed at 5.5 p/kWh

array_as = np.array([])

# Efficiency values
SolarPV_efficiency = 0.3
SolartoBattery_efficiency = 0.9
Efficiency_Losses = 0.0 # Used between calculations to allow losses to sum
Efficiency_Losses_Sum = 0.0 # Used to sum losses using the above after the calculation

# Daily dictionaires
elec_dict = {}
sum_elec_data = {}
Sol_IR_dict = {}

# field_names = ['Hour',
#                'Temperature',
#                'Electricity_Usage',
#                'Carbon_Intensity_2018',
#                'Carbon_Intensity_2019',
#                'Cost',
#                'Solar_IR',
#                'Gas', # The three below sum to this
#                'Heating',
#                'Hot Water',
#                'Cooking'
# ]

field_names = ['Year',
               'Month',
               'Day',
               'Hour',
               'Minute',
               'forecast',
               'actual',
               'index'
]

field_names_w = [
    'Day Number',
    'Day of Week',
    'Average Daily Temperature',
    'Average Electrical Usage',
    'Average Carbon Intensity',
    'Solar Irradiance',
    'Solar Production'
]

field_names_combined = ['',
                        'Hour',
                        'Temperature',
                        'Electricity_Usage',
                        'Cost',
                        'Solar_IR',
                        'Gas',
                        'Heating',
                        'Hot Water',
                        'Cooking',
                        'forecast',
                        'actual',
                        'index',
                        'Rank Ascending',
                        'Rank Descending']
# Blank field above in the first column accounts for the numpy array creation later on

field_names_w_hourly = [
            'Day Number',
            'Week Number',
            'Day of Week',
            'Day of Week Number',
            'Hour',
            'Ambient Temperature',
            'Electricity Usage',
            'Carbon Intensity',
            'Electricity Cost',
            'Solar Irradiance',
            'Solar Production',
            'Gas',
            'Heating',
            'Hot Water',
            'Cooking',
            'Induction Cooking',
            'Gas Heating Carbon Intensity',
            'Gas DHW Carbon Intensity',
            'Gas Cooking Carbon Intensity',
            'Induction Cooking Carbon Intensity',
            'Rank Ascending',
            'Rank Descending',
            'Electricity Cost Scen A',
            'Electricity Carbon Emissions Scen A',
            'Gas Cost Scen A',
            'Gas Carbon Emissions Scen A',
            'Excess Solar Production',
            'Efficiency Losses',
            'Battery SoC',
            'Export Electricity',
            'Battery Status',
            'Electricity_Usage_Scen_B',
            'Elec_Cost_Scen_B',
            'ElecCarEm_Scen_B',
            'Heat Loss',
]

field_names_w_annual = [
            'Day',
            'Week_Number',
            'Day_Week_Number',
            'Temperature',
            'Electricity_Usage',
            'Carbon Intensity',
            'Electricity Cost',
            'Solar_IR',
            'SolPV_prod',
            'Gas',
            'Heating',
            'Hot Water',
            'Cooking',
            'Induction Cooking',
            'Gas Heating Carbon Intensity',
            'Gas DHW Carbon Intensity',
            'Gas Cooking Carbon Intensity',
            'Induction Cooking Carbon Intensity',
            'Rank Ascending',
            'Rank Descending',
            'Elec_Cost_Scen_A',
            'ElecCarEm_Scen_A',
            'Gas_Cost_Scen_A',
            'Gas_CarEm_Scen_A',
            'Excess_Solar_Production',
            'Efficiency_Losses',
            'Battery_SoC',
            'Export_Electricity',
            'Electricity_Usage_Scen_B',
            'Elec_Cost_Scen_B',
            'ElecCarEm_Scen_B',
            'Heat Loss',
]

field_names_temp_w = [
    'Hour',
    'Ambient Temperature'
]

field_names_car_int = [
    #'Hour',
    'Rank Ascending',
    'Rank Descending'
]

field_names_car_int_as = ['Rank Ascending']
field_names_car_int_ds = ['Rank Descending']

New_CSV = 'Output Data.csv'
combined_CSV = 'Combined Data.csv'
input_CSV = 'Basic data half hourly.csv'
New_CSV_temp = 'Output temp Data.csv'
Car_Int_as_CSV = 'Carbon Intensity Ranked Output as.csv'
Car_Int_ds_CSV = 'Carbon Intensity Ranked Output ds.csv'
Output_Text = 'report.txt'

with open(Output_Text, 'w') as f:
    f.write(f'Building Energy Modelling Simulation v0.1{2*chr(10)}') # {chr(10)} adds a new line, 2* does two lines


with open(New_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w_hourly)
  writer.writeheader()

with open(Car_Int_as_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)
  writer.writeheader()

with open(Car_Int_ds_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)
  writer.writeheader()

with open(combined_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_combined)
  writer.writeheader()

def write_row_hourly():
    with open(New_CSV,'a', newline='') as csv_w_file: # 'a' is append, 'w' is write
        writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w_hourly)
        writer.writerow({
            'Day Number': day,
            'Week Number': (day_dict[day][Hour]['Week_Number']),
            'Day of Week': (day_dict[day][Hour]['Day_of_week']),
            'Day of Week Number': (day_dict[day][Hour]['Day_Week_Number']),
            'Hour': Hour,
            'Ambient Temperature': (day_dict[day][Hour]['Temperature']),
            'Electricity Usage': (day_dict[day][Hour]['Electricity_Usage']),
            'Carbon Intensity': (day_dict[day][Hour]['Carbon Intensity']),
            'Electricity Cost': (day_dict[day][Hour]['Electricity Cost']),
            'Solar Irradiance' : (day_dict[day][Hour]['Solar_IR']),
            'Solar Production': (day_dict[day][Hour]['SolPV_prod']),
            'Gas': (day_dict[day][Hour]['Gas']),
            'Heating': (day_dict[day][Hour]['Heating']),
            'Hot Water': (day_dict[day][Hour]['Hot Water']),
            'Cooking': (day_dict[day][Hour]['Cooking']),
            'Induction Cooking': (day_dict[day][Hour]['Induction Cooking']),
            'Gas Heating Carbon Intensity': (day_dict[day][Hour]['Gas Heating Carbon Intensity']),
            'Gas DHW Carbon Intensity': (day_dict[day][Hour]['Gas DHW Carbon Intensity']),
            'Gas Cooking Carbon Intensity': (day_dict[day][Hour]['Gas Cooking Carbon Intensity']),
            'Induction Cooking Carbon Intensity': (day_dict[day][Hour]['Induction Cooking Carbon Intensity']),
            'Rank Ascending': (day_dict[day][Hour]['Rank Ascending']),
            'Rank Descending': (day_dict[day][Hour]['Rank Descending']),
            'Electricity Cost Scen A': (day_dict[day][Hour]['Elec_Cost_Scen_A']),
            'Electricity Carbon Emissions Scen A': (day_dict[day][Hour]['ElecCarEm_Scen_A']),
            'Gas Cost Scen A': (day_dict[day][Hour]['Gas_Cost_Scen_A']),
            'Gas Carbon Emissions Scen A': (day_dict[day][Hour]['Gas_CarEm_Scen_A']),
            'Excess Solar Production': (day_dict[day][Hour]['Excess_Solar_Production']),
            'Efficiency Losses': (day_dict[day][Hour]['Efficiency_Losses']),
            'Battery SoC': (day_dict[day][Hour]['Battery_SoC']),
            'Export Electricity': (day_dict[day][Hour]['Export_Electricity']),
            'Battery Status': (day_dict[day][Hour]['Battery_Status']),
            'Electricity_Usage_Scen_B': (day_dict[day][Hour]['Electricity_Usage_Scen_B']),
            'Elec_Cost_Scen_B': (day_dict[day][Hour]['Elec_Cost_Scen_B']),
            'ElecCarEm_Scen_B': (day_dict[day][Hour]['ElecCarEm_Scen_B']),
            'Heat Loss': (day_dict[day][Hour]['Heat Loss']),
        })

# This creates the function that calls an average over 24 hours
# def average(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x):
#     return (a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x) / 24

def average(x):
    return round(statistics.mean(x), 2)

def HTC_calc():
    #day_energy = (total_energy - heating_energy) / 24 # provides hourly average energy used for heating (gas)
    day_energy = daily_energy / 24
    temp_dif = tsp - statistics.mean(daily_temp) # delta T of internal set point temperature and average daily outside air temperature
    htc = ((day_energy / temp_dif) * boiler_ef) * (tsp - dot) # Watts
    if show_htc:
        print(f"HTC calculation today is {round(day_energy, 2)} kWh / {round(temp_dif, 2)} oC * {boiler_ef}) * ({tsp} oC - {dot} oC)")
    return round(htc, 2)

def sum_nested_dict_values(nested_dict):
    """
    Recursively sum the values in the last nest of a nested dictionary.
    """
    if not isinstance(nested_dict, dict):
        # Base case: not a dictionary, return 0.
        return 0
    elif all(isinstance(value, int) for value in nested_dict.values()):
        # Base case: all values are integers, return the sum.
        return sum(nested_dict.values())
    else:
        # Recursive case: call the function on each value in the dictionary.
        return sum(sum_nested_dict_values(value) for value in nested_dict.values())


# def sum(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x):
#     return (a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x)

class Events:
    def __init__(self, event_hold, event_full, event_charge, event_discharge, event_export):
        self.event_hold = event_hold
        self.event_full = event_full
        self.event_charge = event_charge
        self.event_discharge = event_discharge
        self.event_export = event_export

    def battery_event_hold(self):
        self.event_hold += 1

    def battery_event_full(self):
        self.event_full += 1

    def battery_event_charge(self):
        self.event_charge += 1

    def battery_event_discharge(self):
        self.event_discharge += 1

    def battery_event_export(self):
        self.event_export += 1

battery_events = Events(0, 0, 0, 0, 0)

def interpolate(x, x1, y1, x2, y2):
    """Linear interpolation between two points."""
    return y1 + ((x - x1) * (y2 - y1)) / (x2 - x1)

def get_efficiency(temperature):
    """Get the efficiency for a given temperature."""
    if Temperature <= -10 or Temperature >= 41:
        return "Temperature is outside the valid range."

    # Efficiency values for each increment temperature
    Temperature_values = [-10, 0, 5, 10, 15, 20, 25, 30, 35, 40, 50]
    efficiency_values = [0.85, 0.88, 0.90, 0.92, 0.94, 0.96, 0.96, 0.92, 0.90, 0.85, 0.80]

    # Find the nearest temperature values
    lower_temp = max(t for t in Temperature_values if t <= Temperature)
    upper_temp = min(t for t in Temperature_values if t >= Temperature)

    # Check if lower and upper temperature values are equal
    if lower_temp == upper_temp:
        efficiency = efficiency_values[Temperature_values.index(lower_temp)]
    else:
        # Find the corresponding efficiency values
        lower_efficiency = efficiency_values[Temperature_values.index(lower_temp)]
        upper_efficiency = efficiency_values[Temperature_values.index(upper_temp)]

        # Interpolate the efficiency for the given temperature
        efficiency = interpolate(Temperature, lower_temp, lower_efficiency, upper_temp, upper_efficiency)

    return efficiency

def ranked(sort):
    for i in range(0, bat_num):
        result = np.where(sort == i)
        # print(result)
        # print(float(a[result]))
        x = float(a[result] * car_int)
        print(f'Carbon intensity: {x} gCO2')

''' Building Data '''

class surface():
    AU = 0
    heat_loss = 0

    def __init__(self, name, area, U_value, t_delta):
        self.name = name
        self.area = area
        self.U_value = U_value
        self.t_delta = t_delta

    def calc_t_delta(self, tai, tao):
        t_delta = tai - tao
        return t_delta

    def calc_AU(self, area, U_value):
        AU = area * U_value
        return AU

    def sum_heat_loss(self, tai, tao):
        heat_loss = (self.area * self.U_value * (tai - tao))
        #print(f'{heat_loss} W')
        return heat_loss

    def area(self):
        name = self.name
        if name == 'Floor' or 'Ceiling':
            return floor_height * self.area
        else:
            print('Not floor or ceiling')
            return 0

class room():

    def __init__(self, room_name, surfaces, ach): # surfaces=None
        self.room_name = room_name
        self.surfaces = surfaces
        self.ach = ach # air changes per hour

    def sum_heat_loss(self, tai, tao, called_from_class=True):
        total_heat_loss = 0
        for surface in self.surfaces:
            #surface.calc_heat_loss(surface.area, surface.U_value, tai, tao)
            total_heat_loss += surface.sum_heat_loss(tai, tao)
        if called_from_class == True: # means this only prints if the room called it (Not the building)
            total_heat_loss = round(total_heat_loss,2)
            print(f'Total heat loss {total_heat_loss} W')
        return total_heat_loss

    def list_surfaces(self):
        surfaces = self.surfaces
        #print(f'--{room.room_name}--')
        for surface in self.surfaces:
            print(surface.name)

    # current problem with this possibly is that a surface MUST be named 'Floor' to calculate volume
    def calc_volume(self):
        floor_area = None
        for floor in self.surfaces:
            if isinstance(floor, surface) and floor.name == 'Floor':
                floor_area = floor.area
                break
        return floor_area * floor_height

    def calc_infiltration(self, tai, tao):
        ach = self.ach
        floor_area = None
        for floor in self.surfaces:
            if isinstance(floor, surface) and floor.name == 'Floor':
                floor_area = floor.area
                break
        infiltration = round(floor_area * floor_height * ach * (tai - tao),2)
        return infiltration

class building():
    def __init__(self, building_name, rooms):
        self.building_name = building_name
        self.rooms = rooms

    def sum_heat_loss(self, tai, tao):
        building_heat_loss = 0
        for room in self.rooms: # called_from_class means this won't print room level heat loss
            building_heat_loss += room.sum_heat_loss(tai, tao, called_from_class=False)
        #print(f'Building heat loss {round(building_heat_loss,2)} W')
        return building_heat_loss

    def list_surfaces(self):
        surfaces = self.surfaces
        #print(f'--{room.room_name}--')
        for surface in self.surfaces:
            print(surface.name)

    def sum_volume(self):
        building_volume = 0
        for room in self.rooms:
            building_volume += room.calc_volume()
            #print(f'{room.room_name} has volume {room.calc_volume()} m^3')
        print(f'Building volume {round(building_volume,2)} m^3')
        return building_volume

# R-value in m2.K/W, l/lambda in W/m.K
# U-value in W/m2.K

material_external_wall = {
    'rsi': 0.13, # internal heat transfer, constant
    'rse': 0.13, # external heat transfer, constant
    'outerleaf_brick': {'thickness': '0.105', 'lambda': '0.84'},
    'cavity': {'thickness': '0.05', 'lambda': '0.18'},
    'aerated_concrete_block': {'thickness': '0.15', 'lambda': '0.24'},
    'dense_plaster': {'thickness': '0.013', 'lambda': '0.57'},
}

def calculate_u_value(materials):
    u_value = materials['rsi'] + materials['rse']

    for material in materials:
        if material not in ['rsi', 'rse']:
            thickness = float(materials[material]['thickness'])
            thermal_conductivity = float(materials[material]['lambda'])
            u_value += thickness / thermal_conductivity

    return 1 / u_value

u_value = calculate_u_value(material_external_wall)
print("U-value: {:.2f}".format(u_value))

# --- Bedroom 1 ---
external_wall_1 = surface('External Wall 1', 6.4, 0.89, (tai - tao))
external_wall_2 = surface('External Wall 2', 6.4, 0.89, (tai - tao))
internal_wall_1 = surface('Internal Wall 1', 8.9, 0.89, 0.0)
external_wall_3 = surface('External Wall 3', 7.9, 0.89, (tai - tao))
window_1 = surface('Window 1', 1.0, 2.5, (tai - tao))
ceiling = surface('Ceiling', 10.5, 0.42, (tai - tao))
floor = surface('Floor', 10.5, 2.35, 0.0)

bedroom_1 = room('Bedroom 1',[external_wall_1, external_wall_2, internal_wall_1, external_wall_3, window_1, ceiling, floor], 1)

# --- Bedroom 2 ---
external_wall_4 = surface('External Wall 4', 4.9, 0.89, (tai - tao))
external_wall_5 = surface('External Wall 5', 4.9, 0.89, (tai - tao))
internal_wall_2 = surface('Internal Wall 2', 7.0, 0.89, 0.0)
external_wall_6 = surface('External Wall 6', 6.0, 0.89, (tai - tao))
window_2 = surface('Window 1', 1.0, 2.5, (tai - tao))
ceiling_2 = surface('Ceiling', 6.3, 0.42, (tai - tao))
floor_2 = surface('Floor', 6.3, 2.35, 0.0)

bedroom_2 = room('Bedroom 2',[external_wall_4, external_wall_5, internal_wall_2, external_wall_6, window_2, ceiling_2, floor_2], 1)

# --- Bathroom ---
external_wall_7 = surface('External Wall 7', 4.4, 0.89, (tai - tao))
internal_wall_3 = surface('Internal Wall 3', 4.4, 0.89, (tai - tao))
internal_wall_4 = surface('Internal Wall 4', 4.1, 0.89, 0.0)
internal_wall_5 = surface('Internal Wall 5', 4.1, 0.89, (tai - tao))
ceiling_3 = surface('Ceiling', 3.3, 0.42, (tai - tao))
floor_3 = surface('Floor', 3.3, 2.35, 0.0)

bathroom = room('Bathroom',[external_wall_7, internal_wall_3, internal_wall_4, internal_wall_5, ceiling_3, floor_3], 2)

# --- Stairwell ---
external_wall_8 = surface('External Wall 8', 18.4, 0.89, (tai - tao))
internal_wall_6 = surface('Internal Wall 6', 4.6, 0.89, (tai - tao))
internal_wall_7 = surface('Internal Wall 7', 4.4, 0.89, 0.0)
internal_wall_8 = surface('Internal Wall 8', 2.7, 0.89, (tai - tao))
window_3 = surface('Window 3', 0.6, 2.5, (tai - tao))
ceiling_4 = surface('Ceiling', 5.5, 0.42, (tai - tao))
floor_4 = surface('Floor', 5.5, 2.35, 0.0)

stairwell = room('Stairwell',[external_wall_8, internal_wall_6, internal_wall_7, internal_wall_8, window_3, ceiling_4, floor_4], 2)

# --- Kitchen ---
external_wall_9 = surface('External Wall 9', 7.0, 0.89, (tai - tao))
internal_wall_9 = surface('Internal Wall 9', 7.0, 0.89, (tai - tao))
internal_wall_10 = surface('Internal Wall 10', 3.1, 0.89, 0.0)
internal_wall_11 = surface('Internal Wall 11', 3.7, 0.89, (tai - tao))
window_4 = surface('Window 4', 0.6, 2.5, (tai - tao))
ceiling_5 = surface('Ceiling', 4.8, 0.42, (tai - tao))
floor_5 = surface('Floor', 4.8, 2.35, 0.0)

kitchen = room('Kitchen',[external_wall_9, internal_wall_9, internal_wall_10, internal_wall_11, window_4, ceiling_5, floor_5], 1)

# --- Lounge/Diner ---
external_wall_10 = surface('External Wall 10', 16.1, 0.89, (tai - tao))
external_wall_11 = surface('External Wall 11', 7.3, 0.89, (tai - tao))
external_wall_12 = surface('External Wall 12', 2.5, 0.89, (tai - tao))
external_wall_13 = surface('External Wall 13', 1.9, 0.89, (tai - tao))
internal_wall_12 = surface('Internal Wall 12', 3.9, 0.89, (tai - tao))
internal_wall_13 = surface('Internal Wall 13', 7.2, 0.89, 0.0)
external_wall_14 = surface('External Wall 14', 2.2, 0.89, (tai - tao))
external_wall_15 = surface('External Wall 15', 0.4, 0.89, (tai - tao))
external_wall_16 = surface('External Wall 16', 2.0, 0.89, (tai - tao))
window_5 = surface('Window 5', 1.6, 2.5, (tai - tao))
door_1 = surface('Front Door', 1.8, 1.8, (tai - tao))
door_2 = surface('Patio Door', 2.4, 2.4, (tai - tao))
ceiling_6 = surface('Ceiling', 21.7, 0.42, (tai - tao))
floor_6 = surface('Floor', 21.7, 2.35, 0.0)

lounge_diner = room('Open Lounge Diner',[external_wall_10, external_wall_11, external_wall_12, external_wall_13,
                              internal_wall_12, internal_wall_13, external_wall_14, external_wall_15,
                              external_wall_15, window_5, door_1, door_2, ceiling_6, floor_6],
                    2
                 )

house = building('House', [bedroom_1, bedroom_2, bathroom, stairwell, kitchen, lounge_diner])

''' ---------------------------------------------------------------- '''

# for i in time_periods:
#     # day_list = [{i: {'Day_of_week': day_of_week,
#     i = {'Day_of_week': day_of_week,
#          'Temperature': Temperature,
#          'Electricity_Usage': Electricity_Usage,
#          'Carbon Intensity': CarInt,
#          'Electricity Cost': Elec_Cost,
#          'Solar_IR': Sol_IR,
#          'SolPV_prod': SolPV_prod,
#          'Gas': Gas,
#          'Excess_Solar_Production': Excess_Solar_Production,
#          'Efficiency_Losses': Efficiency_Losses_Sum,
#          'Battery_SoC': Battery_SoC,
#          'Export_Electricity': Export_Electricity,
#          'Battery_Status': Battery_Status
#          }

# Sort ambient air temperature by inserting half hourly data to the available hourly data
# with open(New_CSV_temp, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
#   writer = csv.DictWriter(csv_w_file, fieldnames=field_names_temp_w)
#   writer.writeheader()
#
# field_names_temp = ['Hour', 'Temperature']
# with open('H:\\My Documents\\Ian\\Python\\Energy Model\\Ambient Temperature data.csv') as csv_file:
#     csv_dict_reader_temp = csv.DictReader(csv_file, field_names_temp)
#     next(csv_dict_reader_temp)  # skips header
#     print("Sorting ambient air temperature data...")
#     for row in csv_dict_reader_temp:
#         Hour = (row['Hour'])
#         Temperature = float(row['Temperature'])    # changes integer on every cycle
#
#         #AmbTemp.append(['Temperature'])
#
#         AmbTemp = (Temperature)
#         AmbTemp.insert(-1,0)
#         #AmbTemp[-1] = #(float(AmbTemp[-2]) + float(AmbTemp[-1])) / 2
#         write_row_temp()
#     # for i in range(len(AmbTemp)):
#     #     if i == 'HH':
#     #         AmbTemp[i] = 99
#     #         print("HH changed")
#     #print(len(AmbTemp))
#     print("Ambient Temperate list is " + str(AmbTemp))
#     #for row in csv_dict_reader_temp:
#     #    write_row_temp()

# Plots
plot = 1
x = []
y = []
y_mean = []
def save_plot():
    global plot
    plt.savefig('EnergyModel'+str(plot)+'.png')
    plot = plot + 1

# Plot 1 - Electricity Usage over time
# x = np.array([1, 2, 3])
# y = np.array([1, 2, 3])
plt.title("Annual Electricity Usage")
plt.xlabel("Time")
plt.ylabel("Electricity Usage (kWh)")

# Information
print(f'# # # # #')
print(f'Chosen year for data is {year}.')
print(f'Site has {SolarPV_size} kW of solar PV, {bat_num} batteries each with {bat_capacity} kWh of storage.')

''' National Grid download data '''

NG_CSV = 'national_grid_'+str(year)+'.csv'
if os.path.isfile(NG_CSV):
    print(f'National Grid Carbon Intensity data already downloaded')
else:
    NG_url = 'https://data.nationalgrideso.com/backend/dataset/f406810a-1a36-48d2-b542-1dfb1348096e/resource/0e5fde43-2de7-4fb4-833d-c7bca3b658b0/download/gb_carbon_intensity.csv'
    response = requests.get(NG_url)
    # Load CSV data into a pandas DataFrame
    df = pd.read_csv(StringIO(response.content.decode('utf-8')))

    # Split datetime column into separate date and time columns
    df['Date'] = pd.to_datetime(df['datetime'], format='%Y-%m-%dT%H:%M:%S')
    df['Day'] = df['Date'].dt.day
    df['Month'] = df['Date'].dt.month
    df['Year'] = df['Date'].dt.year
    df['Hour'] = df['Date'].dt.hour
    df['Minute'] = df['Date'].dt.minute

    # Reorder columns and save each year's data to a separate CSV file
    for ng_year in df['Year'].unique():
        filename = f'national_grid_{ng_year}.csv'
        df_year = df[df['Year'] == ng_year]
        #df_year = df_year[['Year', 'Month', 'Day', 'Hour', 'Minute', 'forecast', 'actual', 'index']]
        df_year = df_year[['forecast', 'actual', 'index']]
        df_year.to_csv(filename, index=False)
    print(f'National Grid Carbon Intensity data downloaded and updated, and saved to {NG_CSV}')
    # # Save updated data to a new CSV file
    # NG_CSV = 'gb_carbon_intensity'+str(Year_NG)+'.csv'
    # df.to_csv(NG_CSV, index=False)
''' --------------------------------------------- '''

#def main():
display_info = 0 # change to 1 to print yearly data
display_car_int = 0
with open(NG_CSV) as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names)
    next(csv_dict_reader)  # skips header
    # rank carbon intensity first
    print(f'# # # # #')
    print(f'Sorting National Grid ESO 24-hour forecast ranking...')
    for row in csv_dict_reader:
        Year_NG = float(row['Year'])
        if year == Year_NG:
            Elec_CarInt = float(row['forecast'])
        # else:
        #     break
        row_num = row_num + 1
        # Hour = float(row['Hour'])
        # Minute = float(row['Minute'])
        # if Minute == 30:
        #     Hour += 0.5
        hour_count = hour_count + time_increment
        car_int_list.append(Elec_CarInt)  # adds the carbon intensity period to the rank list (In future this will be changed to the forecast list)
        if hour_count > 24:
            # Elec_CarInt
            a = np.array(car_int_list)
            sorted_indices_as = np.argsort(a)
            sorted_indices_ds = np.argsort(-a)  # -a is descending
            ranks_as = np.empty_like(sorted_indices_as)
            ranks_as[sorted_indices_as] = np.arange(len(a))
            car_int_as[day] = ranks_as
            if display_car_int:
                print(f'Ascending order: {ranks_as}')
            ranks_ds = np.empty_like(sorted_indices_ds)
            ranks_ds[sorted_indices_ds] = np.arange(len(a))
            car_int_ds[day] = ranks_ds
            if display_car_int:
                print(f'Descending order: {ranks_ds}')
                print(f'Day is {day}')
            # writes the array to row
            with open(Car_Int_as_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
                writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)  #
                for element in ranks_as:  # for element in ranks_as
                    writer.writerow({
                        #'Hour': hour_increment, # Hour always comes out as 24
                        'Rank Ascending': element
                    })
                    hour_increment = hour_increment + time_increment
            hour_count = time_increment
            hour_increment = 0.5
            with open(Car_Int_ds_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
                writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)  #
                for element in ranks_ds:  # for element in ranks_as
                    writer.writerow({
                        #'Hour': hour_increment, # Hour always comes out as 24
                        'Rank Descending': element
                    })
                    hour_increment = hour_increment + time_increment
            array_as = np.concatenate((array_as, ranks_as), axis=None)
            car_int_list = []  # clear list for next 24 hours
            hour_count = time_increment
            day = day + 1
            hour_increment = 0.5
    #print(f'Ranked array: {array_as}')
    Hour = 0
    time_increment = 0.5
    hour_count = 0.5
    row_num = 0
    day = 1
    # read data and write
    print(f'# # # # #')
    print(f'National Grid ESO 24-hour forecast ranked')
    #print(f'Writing National Grid ESO 24-hour forecast...')

print(f'# # # # #')
print(f'Combining National Grid ESO 24-hour forecast with input data...')

# merging the csv files of input plus ascending and descending rank of carbon intensity
df = pd.concat(map(pd.read_csv, [input_CSV, NG_CSV, Car_Int_as_CSV, Car_Int_ds_CSV]), axis=1)#, ignore_index=True)
df.to_csv(combined_CSV)

print(f'# # # # #')
print(f'Running Building Energy Modelling System (BEMS)...')
# Previously loaded input_CSV, now loads from combined_CSV to include the ranked National Grid ESO carbon intensity
# field_names also needed changing to the field_names_combined
with open(combined_CSV, 'r') as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names_combined)
    next(csv_dict_reader)  # skips header
    for row in csv_dict_reader:
        #global row_num, hour_count, Battery_SoC, week_num, day_of_week, days_week_num, day, day_breakdown
        row_num = row_num + 1
        # Variable resets
        Export_Electricity = 0.0  # Resets the export electricity
        Efficiency_Losses_Sum = 0.0  # Resets the efficiency losses
        Battery_Status = "Ready"
        Hour = float(row['Hour'])
        hour_count = hour_count + time_increment
        Temperature = float(row['Temperature'])    # changes integer on every cycle
        Electricity_Usage = float(row['Electricity_Usage']) #+ Battery_Charge    # adds battery_charge from previous HH period
        Electricity_Usage_B = Electricity_Usage
        Battery_Charge = 0 # resets the battery_level to 0 ready to charge/discharge again in next phase
        Elec_CarInt = float(row['forecast'])
        Gas = float(row['Gas']) #* Gas_Convert # used to be m3 which required conversion to kWh
        gas_heating = float(row['Heating'])
        gas_dhw = float(row['Hot Water'])
        gas_cooking = float(row['Cooking'])
        ind_hob_cooking = (gas_cooking * gas_hob_efficiency) / ind_hob_efficiency
        # Carbon Intensitites
        gas_heating_CarInt = gas_heating * Gas_CarInt
        gas_dhw_CarInt = gas_dhw * Gas_CarInt
        gas_cooking_CarInt = gas_cooking * Gas_CarInt
        ind_hob_cooking_CarInt = ind_hob_cooking * Elec_CarInt
        # car_int_list.append(Elec_CarInt)    # adds the carbon intensity period to the rank list (In future this will be changed to the forecast list)
        rank_as = float(row['Rank Ascending']) + 1
        rank_ds = float(row['Rank Descending']) + 1
        Elec_Cost = float(row['Cost'])
        Sol_IR = float(row['Solar_IR'])
        SolPV_prod = Sol_IR * SolarPV_size * SolarPV_efficiency
        # Heat Transfer Coefficient
        daily_temp.append(Temperature)
        daily_energy = daily_energy + gas_heating
        # -------------
        # Battery Phase
        # -------------
        #
        Battery_efficiency = get_efficiency(Temperature)
        # Battery Condition setup
        if Battery_SoC >= Max_Capacity:
            bat_SOC_full = True
        elif Battery_SoC < Max_Capacity:
            bat_SOC_full = False
        Excess_Solar_Production = Electricity_Usage - SolPV_prod
        if Excess_Solar_Production < 0:
            solprod_exceeds_usage = True
        else:
            solprod_exceeds_usage = False
        if rank_as <= 1 + bat_num - 1: # integer each way is battery numbers
            low_carbon_period = True
        else:
            low_carbon_period = False
        if rank_as >= 48 - bat_num + 1: # integer each way is battery numbers
            high_carbon_period = True
        else:
            high_carbon_period = False
        #
        # Battery System flow
        if bat_SOC_full:
            if solprod_exceeds_usage:
                Battery_Status = "Export"
                battery_events.battery_event_export()
            elif solprod_exceeds_usage == False:
                if high_carbon_period:
                    Battery_Status = "Discharge"
                    battery_events.battery_event_discharge()
                elif high_carbon_period == False and Battery_SoC == Max_Capacity:
                    Battery_Status = "Full"
                    battery_events.battery_event_full()
                elif Battery_SoC > Max_Capacity:
                    #Battery_SoC == Max_Capacity
                    Battery_Status = "Export" # an additional export if the SoC goes above max capacity
                    battery_events.battery_event_export()
        elif bat_SOC_full == False:
            if solprod_exceeds_usage:
                Battery_Status = "Charge"
                battery_events.battery_event_charge()
            elif solprod_exceeds_usage == False:
                if low_carbon_period:
                    Battery_Status = "Charge"
                    battery_events.battery_event_charge()
                elif low_carbon_period == False:
                    Battery_Status = "Hold"
                    battery_events.battery_event_hold()
        #
        # Battery Actions
        if Battery_Status == "Export":
            Battery_SoC = Battery_SoC + -(Excess_Solar_Production * Battery_efficiency) # adds the total excess on top (Remember solar is a -ve int)
            SoC_Excess = Battery_SoC - Max_Capacity # This is the excess to export, but charges the battery to full first
            Efficiency_Losses_Sum = Efficiency_Losses_Sum + -(Excess_Solar_Production * (1 - Battery_efficiency))
            Export_Electricity = Export_Electricity + SoC_Excess # exports excess energy
            Battery_SoC = Max_Capacity # Resets the battery to full
            Electricity_Usage_B = Electricity_Usage_B - Export_Electricity
            if Electricity_Usage_B < 0: # Export to grid if excess is more than consumed
                Elec_Cost = SEG
        if Battery_Status == "Discharge":
            Battery_SoC = Battery_SoC - Max_Discharge_Capacity
            Electricity_Usage_B = Electricity_Usage - (Max_Discharge_Capacity * Battery_efficiency) # Reduces the electricity usage by the discharge amount
            Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Max_Discharge_Capacity * (1 - Battery_efficiency))
            Elec_Cost = SEG # Smart Export Guarantee (SEG)
        #if Battery_Status == "Hold": # Do nothing
        if Battery_Status == "Charge":
            Battery_SoC = Battery_SoC + SolPV_prod # SolPV_prod includes efficiency losses
            if low_carbon_period:
                if Battery_SoC + Max_Charge_Capacity <= Max_Capacity: # full charge without exceeding the batteries SoC
                    Battery_SoC = Battery_SoC + (Max_Charge_Capacity * Battery_efficiency)
                    Electricity_Usage_B = Electricity_Usage + Max_Charge_Capacity # Adds live electricity usage
                    #Battery_Charge = Max_Charge_Capacity # This charge amount is added to next period's electricity usage
                    Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Max_Charge_Capacity * (1 - Battery_efficiency))
                elif Battery_SoC + Max_Charge_Capacity > Max_Capacity: # full charge which exceeds the batteries SoC and requires balancing
                    Battery_SoC = Battery_SoC + Max_Charge_Capacity # include efficiency loss in a second..
                    SoC_Excess = Battery_SoC - Max_Capacity # calculates the excess
                    Battery_Charge = Max_Charge_Capacity - SoC_Excess # calculates actual energy used to charge
                    Electricity_Usage_B = Electricity_Usage + (Battery_Charge / Battery_efficiency) # now take into account efficiency loss
                    Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Battery_Charge * (1 - Battery_efficiency))
                    Battery_SoC = Max_Capacity  # Resets the battery to full
        #
        # -------------
        # Building Model
        # -------------
        #
        tao = Temperature  # this is the link to formula.py so at some point I'll change Temperature to tao
        heat_loss = (house.sum_heat_loss(tai, tao) / 1000) # W to kW
        #print(f'Heat loss is {heat_loss}')
        #
        if display_info == 1:
            #print('Hour ' + Hour + ' has a temperature of ' + Temperature + ' and uses ' + Electricity + ' kWh of electricity' + ' costing ' + Elec_Cost +' p/kWh and emitting ' + CarInt + ' g/kWh. Solar PV production is ' + SolPV_prod + ' kWh')
            print(f"Hour {Hour} has a temperature of {Temperature} and uses {Electricity_Usage} kWh of electricity costing {'{0:.1f}'.format(Elec_Cost)} p/kWh, emits {Elec_CarInt} g/kWh. Solar PV produces {'{0:.2f}'.format(SolPV_prod)} kWh")
        # Formulas
        Elec_Cost_Scen_A =  (Elec_Cost * Electricity_Usage) / 100
        ElecCarEm_Scen_A = (Elec_CarInt * Electricity_Usage) / 1000
        Gas_Cost_Scen_A = (Gas_Cost * Gas) / 100
        Gas_CarEm_Scen_A = (Gas_CarInt * Gas) / 1000
        Elec_Cost_Scen_B = (Elec_Cost * Electricity_Usage_B) / 100
        ElecCarEm_Scen_B = (Elec_CarInt * Electricity_Usage_B) / 1000
        #
        #day_dict[Hour] = day_list  # puts lists within the dictionary
        day_dict[day] = {Hour: {'Week_Number': week_num,
                  'Day_of_week': day_of_week,
                  'Day_Week_Number': days_week_num,
                  'Temperature': Temperature,
                  'Electricity_Usage': Electricity_Usage,
                  'Carbon Intensity': Elec_CarInt,
                  'Electricity Cost': Elec_Cost,
                  'Solar_IR': Sol_IR,
                  'SolPV_prod': SolPV_prod,
                  'Gas': Gas,
                  'Heating': gas_heating,
                  'Hot Water': gas_dhw,
                  'Cooking': gas_cooking,
                  'Induction Cooking': ind_hob_cooking,
                  'Gas Heating Carbon Intensity': gas_heating_CarInt,
                  'Gas DHW Carbon Intensity': gas_dhw_CarInt,
                  'Gas Cooking Carbon Intensity': gas_cooking_CarInt,
                  'Induction Cooking Carbon Intensity': ind_hob_cooking_CarInt,
                  'Rank Ascending': rank_as,
                  'Rank Descending': rank_ds,
                  'Elec_Cost_Scen_A': Elec_Cost_Scen_A,
                  'ElecCarEm_Scen_A': ElecCarEm_Scen_A,
                  'Gas_Cost_Scen_A': Gas_Cost_Scen_A,
                  'Gas_CarEm_Scen_A': Gas_CarEm_Scen_A,
                  'Excess_Solar_Production': Excess_Solar_Production,
                  'Efficiency_Losses': Efficiency_Losses_Sum,
                  'Battery_SoC': Battery_SoC,
                  'Export_Electricity': Export_Electricity,
                  'Battery_Status': Battery_Status,
                  'Electricity_Usage_Scen_B': Electricity_Usage_B,
                  'Elec_Cost_Scen_B': Elec_Cost_Scen_B,
                  'ElecCarEm_Scen_B': ElecCarEm_Scen_B,
                  'Heat Loss': heat_loss,
                  }}
        # D[Hour] = {'Day_of_week': day_of_week,
        #           'Temperature': Temperature,
        #           'Electricity_Usage': Electricity_Usage,
        #           'Carbon Intensity': CarInt,
        #           'Electricity Cost': Elec_Cost,
        #           'Solar_IR': Sol_IR,
        #           'SolPV_prod': SolPV_prod,
        #           'Gas': Gas,
        #           'Excess_Solar_Production': Excess_Solar_Production,
        #           'Efficiency_Losses': Efficiency_Losses_Sum,
        #           'Battery_SoC': Battery_SoC,
        #           'Export_Electricity': Export_Electricity,
        #           'Battery_Status': Battery_Status
        #           }
        Y[day][Hour] = {'Week_Number': week_num,
                  'Day_of_week': day_of_week,
                  'Day_Week_Number': days_week_num,
                  'Temperature': Temperature,
                  'Electricity_Usage': Electricity_Usage,
                  'Carbon Intensity': Elec_CarInt,
                  'Electricity Cost': Elec_Cost,
                  'Solar_IR': Sol_IR,
                  'SolPV_prod': SolPV_prod,
                  'Gas': Gas,
                  'Heating': gas_heating,
                  'Hot Water': gas_dhw,
                  'Cooking': gas_cooking,
                  'Induction Cooking': ind_hob_cooking,
                  'Gas Heating Carbon Intensity': gas_heating_CarInt,
                  'Gas DHW Carbon Intensity': gas_dhw_CarInt,
                  'Gas Cooking Carbon Intensity': gas_cooking_CarInt,
                  'Induction Cooking Carbon Intensity': ind_hob_cooking_CarInt,
                  'Rank Ascending': rank_as,
                  'Rank Descending': rank_ds,
                  'Elec_Cost_Scen_A': Elec_Cost_Scen_A,
                  'ElecCarEm_Scen_A': ElecCarEm_Scen_A,
                  'Gas_Cost_Scen_A': Gas_Cost_Scen_A,
                  'Gas_CarEm_Scen_A': Gas_CarEm_Scen_A,
                  'Excess_Solar_Production': Excess_Solar_Production,
                  'Efficiency_Losses': Efficiency_Losses_Sum,
                  'Battery_SoC': Battery_SoC,
                  'Export_Electricity': Export_Electricity,
                  'Battery_Status': Battery_Status,
                  'Electricity_Usage_Scen_B': Electricity_Usage_B,
                  'Elec_Cost_Scen_B': Elec_Cost_Scen_B,
                  'ElecCarEm_Scen_B': ElecCarEm_Scen_B,
                  'Heat Loss': heat_loss,
                  }
        sum_Electricity_Usage += Electricity_Usage
        sum_Electricity_Usage_B += Electricity_Usage_B

        # Writes the daily output CSV file
        #elec_dict[Hour] = Electricity_Usage
        Sol_IR_dict[Hour] = Sol_IR
        write_row_hourly()  # This triggers the write_row trigger to write all information to the CSV data file
        if hour_count > 24:
            list = Sol_IR_dict.values()
            # HTC
            if show_htc:
                print(f"Day {day}")
                print(f"Daily energy used for heating is {round(daily_energy, 2)} kWh")
                print(f"Average DOT is {round(statistics.mean(daily_temp), 2)} degrees C")
                print(f"Energy required to heat property from {dot}C to {tsp}C is {HTC_calc()} kW")
            #
            # Rank carbon intensity in 24 hours

            # --------------------
            # print(f'Lowest carbon intensity values:')
            # ranked(ranks_as)
            # print(f'Highest carbon intensity values:')
            # ranked(ranks_ds)

            # ---------------------------------
            # Plots
            # mean_data = statistics.mean(list)
            # sum_data = sum(list)
            # x.append(day)
            # y.append(sum_data)
            # y_mean.append(mean_data)
            # -----
            #Y[day] = [{D1}
            hour_count = time_increment
            index = days_week.index(day_of_week)
            # Start a new week
            if days_week_num == 7: # end of week
                days_week_num = 1
            else:
                days_week_num = days_week_num + 1
            # Start a new week
            if index >= 6:
                day_of_week = 'Mon'
            else:
                day_of_week = days_week[index + 1]
            # Setup week number, starting from the day the year started with, rather than always a Monday - So all weeks are 7 days
            if days_week_num == year_start_day_num and day > 6:
                week_num = week_num + 1
            #Y[day] = D
            day = day + 1
            day_dict[day] = {}
            #if day < 60 or day > 335:  # temporary crude way of returning heating days, could also try heating amount
            htc_list.append(HTC_calc())
            # Clear HTC data
            daily_temp.clear()
            daily_energy = 0

# for i in range(1,365):
#     for h in range(1,25):
#         elec_dict[h] = Y[i][h]['Electricity_Usage']
#         print(elec_dict[h])
#         if h == 24:
#             sum_elec_data[i] = sum(elec_dict.values())
#     x.append(i)
#     y.append(sum_elec_data[i])


# Plots
#plotList = Y[day][Hour]['Carbon Intensity']
#x = np.array([1,17521])
#y = np.array(day_dict[day][Hour][Electricity_Usage])

#plt.plot(x, y)

#plt.plot(x, y_mean)

#print(year_start_day)





print(f'# # # # #')
today = date.today()
print("Today's date is ", today, " which is a",days_week[today.weekday()])    # also use today.day, today.month, today.year
sum_htc = sum(htc_list)
print(f"Heating Season Energy: {round(sum_htc, 2)} kW")
print(f"{len(htc_list)} heating periods")
htc_final = sum(htc_list) / len(htc_list)
print(f"Average Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW")
print(f'# # # # #')
print(f"Calculation completed") # Next calculate mean of htc_list ie all HTC in heating season
with open(Output_Text, 'a') as f:
    f.write(f"Todays date is {today} which is a {days_week[today.weekday()]}{2*chr(10)}")
    f.write(f"The battery carried out {battery_events.event_hold} hold events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_full} full events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_charge} charge events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_discharge} discharge events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_export} export events{2*chr(10)}")
    f.write(f"Average Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW{2*chr(10)}")
    f.write(f"Scenario A Electricity Usage {round(sum_Electricity_Usage, 2)} kWh{chr(10)}")
    f.write(f"Scenario B Electricity Usage {round(sum_Electricity_Usage_B, 2)} kWh{chr(10)}")


# today.weekday 0 is monday, 6 is sunday
#print(day_of_week)

# Read the input CSV file
input_file = 'Output Data.csv'
df = pd.read_csv(input_file)

# Define a dictionary with the number of days in each month (ignoring leap years)
month_days = {
    1: 31,  # January
    2: 28,  # February
    3: 31,  # March
    4: 30,  # April
    5: 31,  # May
    6: 30,  # June
    7: 31,  # July
    8: 31,  # August
    9: 30,  # September
    10: 31,  # October
    11: 30,  # November
    12: 31  # December
}

# Add new columns for day of the month and month
df['day_of_month'] = df['Day Number'] % 31
df['month'] = (df['Day Number']) // 31 + 1

# Group the data by day and calculate sum and average for each day
grouped_data = df.groupby('Day Number').agg({'day_of_month': 'first', 'month': 'first', 'Electricity Usage': 'sum', 'Electricity_Usage_Scen_B': 'sum', 'Ambient Temperature': 'mean'}).reset_index()

# Save the grouped data to a new CSV file
output_file = 'Annual Data.csv'
grouped_data.to_csv(output_file, index=False)

print(f"Data saved to {output_file}.")

''' Machine Learning part that isn't finished
if ML:
    day_breakdown = day_breakdown + 1
    #print(f"Day {day_breakdown} of {year} is a {Y[day_breakdown][Hour]['Day_of_week']}")   # +1 day because it starts at 0
    #print(day_dict[day_breakdown])
    if display_info:
        print(f'# # # # #')
        print(f'Sorted carbon intensity on day {day_breakdown} is {car_int_as[day_breakdown]}')
    #print(day_list)
    if display_info:
        print(f'# # # # #')
        print(D)
    #for i in range(1,24):
    if display_info:
        print(f'# # # # #')
        print(Y[1])


    df_model = pd.read_csv("Output Data.csv")
    # Define the prediction task by naming the target column
    pdef = ProblemDefinition.from_dict(
        {
            "target": "Electricity Usage",  # column you want to predict
        }
    )

    # Generate JSON-AI code to model the problem
    json_ai = json_ai_from_problem(df_model, problem_definition=pdef)

    # OPTIONAL - see the JSON-AI syntax
    # print(json_ai.to_json())

    # Generate python code
    code = code_from_json_ai(json_ai)

    # OPTIONAL - see generated code
    # print(code)

    # Create a predictor from python code
    predictor = predictor_from_code(code)

    # Train a model end-to-end from raw data to a finalized predictor
    predictor.learn(df_model)

    # Make the train/test splits and show predictions for a few examples
    test_df = predictor.split(predictor.preprocess(df_model))["test"]
    preds = predictor.predict(test_df).iloc[:10]
    print(preds)
'''

#save_plot()
#plt.show()

# result_dict = {}

# for day, Hour in day_dict.items():
#     variables_sum = {}
#     variables_count = {}
#
#     for hour in Hour.values():
#         for variable, value in hour.items():
#             if isinstance(value, (int, float)):
#                 if variable not in variables_sum:
#                     variables_sum[variable] = 0
#                     variables_count[variable] = 0
#                 variables_sum[variable] += value
#                 variables_count[variable] += 1
#
#         variables_avg = {}
#         for variable, total_sum in variables_sum.items():
#             variables_avg[variable] = total_sum / variables_count[variable] if variables_count[variable] != 0 else 0
#
#         result_dict[day] = variables_avg

# annual_sum = {
#
#
#
# }
#
# for day, Hour in day_dict.items():
#     temp_sum = 0
#     elec_sum = 0
#     num_hours = 0
#
#     for hour in Hour.values():
#         temp_sum += hour['Temperature']
#         elec_sum += hour['Electricity_Usage']
#         num_hours += 1
#
#     avg_temp = temp_sum / num_hours if num_hours != 0 else 0
#     result_dict[day] = {
#         'Temperature': avg_temp,
#         'Electricity_Usage': elec_sum
#     }
#
# print(result_dict)
# # Write results to CSV file
# csv_filename = 'annual_results.csv'
#
# with open(csv_filename, 'w', newline='') as csvfile:
#     #fieldnames = ['Day'] + list(result_dict[next(iter(result_dict))].keys())  # Get variable names as fieldnames
#     writer = csv.DictWriter(csvfile, fieldnames=field_names_w_annual)
#     writer.writeheader()
#
#     for day, variables in result_dict.items():
#         row = {'Day': day, **variables}
#         writer.writerow(row)
#
# print(f"Results have been written to '{csv_filename}' file.")



#print("Electricity usage was " + str(day_dict[day_breakdown]))

# # print(f"Average daily temperature is {'{0:.2f}'.format(average_temp[day_breakdown])} degrees C")        #prints, which means the last field is duplicated
# # print(f"Average daily electricity usage is {'{0:.2f}'.format(average_elec[day_breakdown])} kWh")
# print(Carbon_Int[day_breakdown])
#
# # This group sorts the carbon intensity in the current day to the number of batteries
# Carbon_Int_sort = sorted(Carbon_Int[day_breakdown])
# Carbon_Int_sort = Carbon_Int_sort[0:bat_num]
#
# Carbon_Int_dict = {""}
#
# print(Carbon_Int_sort)          # sorts carbon intensity, sorted by lowest, for number of batteries
#
# Carbon_Int_sortR = sorted(Carbon_Int[day_breakdown])
# Carbon_Int_sortR.sort(reverse=True)
# Carbon_Int_sortR = Carbon_Int_sortR[0:bat_num]
# print(Carbon_Int_sortR)             # for some reason above two need to go after Carbon_Int_sort
#
# #for i in range(0,bat_num):
#     #Carbon_Int_dict[i] = 1
#     #Carbon_Int_dict["Carbon_Int_sortR"] = 0
#     #if Carbon_Int_sort[i] == Carbon_Int[i]:
#     #    (Carbon_Int[i][i], "hmm")
# print(Carbon_Int_sort[0])
# print(Carbon_Int_dict)
#
# print(Temperature_list[day_breakdown])      # prints 24 hourly temperature
# print("Electricity is " + str(Electricity_list[day_breakdown]))

# print(day_of_week[200])     # returns the day of week for specified day number
#for i in range(1,24):
#    print(str(average_temp))
#for i in range(1,24):



            #print(type(day_dict['4'][0])) # returns class type
            #print((day_dict['4'][0]))  # returns a set position
            #new_value = new_value + float(day_dict['4'][0])
            #print('Daily temperatures ' + str(daily_temp))
            #print(max(daily_temp))
            #print(day_dict.keys())     # prints the keys eg 1-24
            #print(day_dict.values())   # prints the values

            #print(day_dict[day_list[1]])


# if __name__ == "__main__":
#     main()