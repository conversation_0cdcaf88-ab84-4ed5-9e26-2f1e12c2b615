import CoolProp.CoolProp as CP
import numpy as np
import matplotlib.pyplot as plt
import time


def plot_cycle(evap_temp_C, cond_temp_C):
    start_time = time.time()

    # Convert temperatures to Kelvin
    evaporator_temp = evap_temp_C + 273.15
    condenser_temp = cond_temp_C + 273.15

    # Define the refrigerant
    refrigerant = 'Propane'  # R290

    # Define pressures corresponding to these temperatures
    P_evap = CP.PropsSI('P', 'T', evaporator_temp, 'Q', 1, refrigerant)
    P_cond = CP.PropsSI('P', 'T', condenser_temp, 'Q', 0, refrigerant)

    # Define state points dynamically
    # State 1: Saturated vapor at evaporator outlet
    h1 = CP.PropsSI('H', 'T', evaporator_temp, 'Q', 1, refrigerant)
    s1 = CP.PropsSI('S', 'T', evaporator_temp, 'Q', 1, refrigerant)

    # State 2: Superheated vapor at compressor outlet
    h2 = CP.PropsSI('H', 'P', P_cond, 'S', s1, refrigerant)
    s2 = s1

    # State 3: Saturated liquid at condenser outlet
    h3 = CP.PropsSI('H', 'T', condenser_temp, 'Q', 0, refrigerant)
    s3 = CP.PropsSI('S', 'T', condenser_temp, 'Q', 0, refrigerant)

    # State 4: Mixture at evaporator inlet (after expansion valve)
    h4 = h3
    P4 = P_evap
    s4 = CP.PropsSI('S', 'H', h4, 'P', P4, refrigerant)

    # Plotting the P-H diagram dynamically
    h_vals = np.linspace(CP.PropsSI('H', 'T', 200, 'Q', 0, refrigerant),
                         CP.PropsSI('H', 'T', 400, 'Q', 1, refrigerant), 500)
    P_vals = [CP.PropsSI('P', 'H', h, 'Q', 0, refrigerant) for h in h_vals]

    plt.figure(figsize=(10, 6))
    plt.plot(h_vals / 1000, P_vals, label='Saturated Liquid Line')

    P_vals = [CP.PropsSI('P', 'H', h, 'Q', 1, refrigerant) for h in h_vals]
    plt.plot(h_vals / 1000, P_vals, label='Saturated Vapor Line')

    # Plot cycle points dynamically
    cycle_points = [(h1, P_evap), (h2, P_cond), (h3, P_cond), (h4, P_evap)]
    cycle_points_h = [p[0] / 1000 for p in cycle_points]
    cycle_points_P = [p[1] for p in cycle_points]

    plt.plot(cycle_points_h, cycle_points_P, 'ro-')
    plt.xlabel('Enthalpy (kJ/kg)')
    plt.ylabel('Pressure (Pa)')
    plt.title(f'P-H Diagram for R290 at Evap {evap_temp_C}C and Cond {cond_temp_C}C')
    plt.legend()
    plt.grid(True)
    plt.show()

    end_time = time.time()
    print(f"Calculation and plotting took {end_time - start_time:.2f} seconds")


# Example usage with dynamic temperatures
ambient_temp_C = 30
evap_temp_C = 5
cond_temp_C = 50

plot_cycle(evap_temp_C, cond_temp_C)