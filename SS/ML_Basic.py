from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

input_file = 'Output Data.csv'
data = pd.read_csv(input_file)

# Preprocessing the data
# Selecting features and target variable
features = ['Ambient Temperature', 'Hour', 'Day of Week', 'Solar Production']
target = 'ElecCarEm_Scen_B' #'Electricity Usage'

# Handling categorical data - 'Day of Week'
categorical_features = ['Day of Week']
categorical_transformer = OneHotEncoder()

# Creating a preprocessor pipeline that only transforms categorical features
preprocessor = ColumnTransformer(
    transformers=[
        ('cat', categorical_transformer, categorical_features)],
    remainder='passthrough')

# Applying the transformation to our data
X = data[features]
y = data[target]

# Splitting the dataset into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=0)

# Creating a machine learning pipeline with preprocessing and model
model = Pipeline(steps=[('preprocessor', preprocessor),
                        ('regressor', LinearRegression())])

# Fitting the model
model.fit(X_train, y_train)

# Predicting on the test set
y_pred = model.predict(X_test)

# Evaluating the model
mse = mean_squared_error(y_test, y_pred)
rmse = np.sqrt(mse)

print(f'Mean Squared Error (MSE): {round(mse, 4)},'
      f'Root Mean Squared Error (RMSE): {round(rmse, 4)}')

# Creating a figure with three subplots
fig, axes = plt.subplots(nrows=3, ncols=1, figsize=(12, 18))

# Actual vs. Predicted Values Plot
axes[0].scatter(y_test, y_pred, alpha=0.5)
axes[0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
axes[0].set_xlabel('Actual Values')
axes[0].set_ylabel('Predicted Values')
axes[0].set_title('Actual vs Predicted Values')

# Residual Plot
residuals = y_test - y_pred

# Residual Plot
axes[1].scatter(y_pred, residuals, alpha=0.5)
axes[1].axhline(y=0, color='k', linestyle='--', lw=2)
axes[1].set_xlabel('Predicted Values')
axes[1].set_ylabel('Residuals')
axes[1].set_title('Residual Plot')

# Feature Importance (Coefficients)
# Getting feature names after transformation
feature_names = model.named_steps['preprocessor'].transformers_[0][1].get_feature_names_out(categorical_features)
feature_names = np.concatenate([feature_names, np.array(features)[1:]])

# Getting coefficients
coefficients = model.named_steps['regressor'].coef_

# Feature Importance (Coefficients)
axes[2].barh(feature_names, coefficients)
axes[2].set_xlabel('Coefficient Value')
axes[2].set_ylabel('Features')
axes[2].set_title('Feature Importance')

# Adjust layout and show the combined plot
plt.tight_layout()
plt.show()

