""""
Version History
--- working ---
v0.13
Added heating and cooling setpoints, heating and cooling outputs, thermal mass temperature
Heat loss (UA) has been temporarily disconnected from previous HTC and house size values for more basic office cube
Building_Name added, included in Output_Data.csv file for future reference
Carbon intensity ranking does not currently work - Only takes one day and duplicates it. Large amount commented out.
Splitting out into separate files
Files so far: variables, fabric, battery, calculations (.py)

v0.12
Graph annual output data and apply some ML optimisation to it
Include electricity consumption SCEN_A (Which I've forgotten) which is solar PV but no batteries
Battery SOC doesn't work

New_CSV and Annual Data.csv files seem to be mixed up.. check this at some point
line 558 breaks for year 2021, but not 2018 or 2019 - even though header 'year' doesn't match..?

v0.11
import formulas such as U-values and thermal capacity

Battery efficiency now extrapolates based on ambient air temperature

v0.10
Only downloads National Grid API data if the CSV does not already exist (Previously was downloading every time)
HTC_calc is imported and running

--- Description ---
This is a python based energy building model, developed further from my Master's degree in which I developed an excel based model
The Excel model takes historic hourly electricity and gas usage, combines with this hourly solar radiance and ambient air temperature
 and carbon intensity from the National Grid.
The model then balances this electricity consumption against theoretical solar PV arrays and battery storage to balance across a time-of-use
 tariff and carbon intensity to reduce annual energy costs and carbon emissions.
The model also included a basic thermal model of heat loss based on U-values and thermal capacity, heating and cooling loads, AHU,
 DHW, internal gains such as occupants, equipment and solar gains, and electric vehicle charging profiles.

"""

import csv
from datetime import date
from datetime import time
from datetime import datetime, timedelta

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import statistics
import requests
from io import StringIO

from fabric import *
from battery import *
from building import *

# from lightwood.api.high_level import (
#     ProblemDefinition,
#     json_ai_from_problem,
#     code_from_json_ai,
#     predictor_from_code,
# )

print(f'Setting variables...')

# Use upper case LETTERS for defining a constant that never changes
from typing import List, Any

ML = False

with open(Output_Text, 'w') as f:
    f.write(f'Building Energy Modelling Simulation v0.1{2*chr(10)}') # {chr(10)} adds a new line, 2* does two lines

with open(New_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w_hourly)
  writer.writeheader()

with open(Car_Int_as_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)
  writer.writeheader()

with open(Car_Int_ds_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)
  writer.writeheader()

with open(combined_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_combined)
  writer.writeheader()

def write_row_hourly():
    with open(New_CSV,'a', newline='') as csv_w_file: # 'a' is append, 'w' is write
        writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w_hourly)
        writer.writerow({
            'Day Number': day,
            'Week Number': (day_dict[day][Hour]['Week_Number']),
            'Day of Week': (day_dict[day][Hour]['Day_of_week']),
            'Day of Week Number': (day_dict[day][Hour]['Day_Week_Number']),
            'Hour': Hour,
            'Ambient Temperature': (day_dict[day][Hour]['Temperature']),
            'Electricity Usage': (day_dict[day][Hour]['Electricity_Usage']),
            'Carbon Intensity': (day_dict[day][Hour]['Carbon Intensity']),
            'Electricity Cost': (day_dict[day][Hour]['Electricity Cost']),
            'Solar Irradiance' : (day_dict[day][Hour]['Solar_IR']),
            'Solar Production': (day_dict[day][Hour]['SolPV_prod']),
            'Gas': (day_dict[day][Hour]['Gas']),
            'Heating': (day_dict[day][Hour]['Heating']),
            'Hot Water': (day_dict[day][Hour]['Hot Water']),
            'Cooking': (day_dict[day][Hour]['Cooking']),
            'Induction Cooking': (day_dict[day][Hour]['Induction Cooking']),
            'Gas Heating Carbon Intensity': (day_dict[day][Hour]['Gas Heating Carbon Intensity']),
            'Gas DHW Carbon Intensity': (day_dict[day][Hour]['Gas DHW Carbon Intensity']),
            'Gas Cooking Carbon Intensity': (day_dict[day][Hour]['Gas Cooking Carbon Intensity']),
            'Induction Cooking Carbon Intensity': (day_dict[day][Hour]['Induction Cooking Carbon Intensity']),
            'Rank Ascending': (day_dict[day][Hour]['Rank Ascending']),
            'Rank Descending': (day_dict[day][Hour]['Rank Descending']),
            'Electricity Cost Scen A': (day_dict[day][Hour]['Elec_Cost_Scen_A']),
            'Electricity Carbon Emissions Scen A': (day_dict[day][Hour]['ElecCarEm_Scen_A']),
            'Gas Cost Scen A': (day_dict[day][Hour]['Gas_Cost_Scen_A']),
            'Gas Carbon Emissions Scen A': (day_dict[day][Hour]['Gas_CarEm_Scen_A']),
            'Excess Solar Production': (day_dict[day][Hour]['Excess_Solar_Production']),
            'Battery Efficiency': (day_dict[day][Hour]['Battery_efficiency']),
            'Efficiency Losses': (day_dict[day][Hour]['Efficiency_Losses']),
            'Battery SoC': (day_dict[day][Hour]['Battery_SoC']),
            'Export Electricity': (day_dict[day][Hour]['Export_Electricity']),
            'Battery Status': (day_dict[day][Hour]['Battery_Status']),
            'Electricity_Usage_Scen_B': (day_dict[day][Hour]['Electricity_Usage_Scen_B']),
            'Elec_Cost_Scen_B': (day_dict[day][Hour]['Elec_Cost_Scen_B']),
            'ElecCarEm_Scen_B': (day_dict[day][Hour]['ElecCarEm_Scen_B']),
            'Electricity_Usage_Scen_C': (day_dict[day][Hour]['Electricity_Usage_Scen_C']),
            'Elec_Cost_Scen_C': (day_dict[day][Hour]['Elec_Cost_Scen_C']),
            'ElecCarEm_Scen_C': (day_dict[day][Hour]['ElecCarEm_Scen_C']),
            'Heat Loss': (day_dict[day][Hour]['Heat Loss']),
            'Occupancy': (day_dict[day][Hour]['Occupancy']),
            'Occupant Gains': (day_dict[day][Hour]['Occupant Gains']),
            'Thermal Mass Temperature': (day_dict[day][Hour]['Thermal Mass Temperature']),
            'Heating Setpoint': (day_dict[day][Hour]['Heating Setpoint']),
            'Cooling Setpoint': (day_dict[day][Hour]['Cooling Setpoint']),
            'Thermal Status': (day_dict[day][Hour]['Thermal Status']),
            'Heating Output': (day_dict[day][Hour]['Heating Output']),
            'Cooling Output': (day_dict[day][Hour]['Cooling Output']),
        })

# Information
print(f'# # # # #')
print(f'Chosen year for data is {year}.')
print(f'Site has {SolarPV_size} kW of solar PV, {bat_num} batteries each with {bat_capacity} kWh of storage.')

''' National Grid download data '''

NG_CSV = 'national_grid_'+str(year)+'.csv'
if os.path.isfile(NG_CSV):
    print(f'National Grid Carbon Intensity data already downloaded')
else:
    NG_url = 'https://data.nationalgrideso.com/backend/dataset/f406810a-1a36-48d2-b542-1dfb1348096e/resource/0e5fde43-2de7-4fb4-833d-c7bca3b658b0/download/gb_carbon_intensity.csv'
    response = requests.get(NG_url)
    # Load CSV data into a pandas DataFrame
    df = pd.read_csv(StringIO(response.content.decode('utf-8')))

    # Split datetime column into separate date and time columns
    df['Date'] = pd.to_datetime(df['datetime'], format='%Y-%m-%dT%H:%M:%S')
    df['Day'] = df['Date'].dt.day
    df['Month'] = df['Date'].dt.month
    df['Year'] = df['Date'].dt.year
    df['Hour'] = df['Date'].dt.hour
    df['Minute'] = df['Date'].dt.minute

    # Reorder columns and save each year's data to a separate CSV file
    for ng_year in df['Year'].unique():
        filename = f'national_grid_{ng_year}.csv'
        df_year = df[df['Year'] == ng_year]
        #df_year = df_year[['Year', 'Month', 'Day', 'Hour', 'Minute', 'forecast', 'actual', 'index']]
        df_year = df_year[['forecast', 'actual', 'index']]
        df_year.to_csv(filename, index=False)
    print(f'National Grid Carbon Intensity data downloaded and updated, and saved to {NG_CSV}')
    # # Save updated data to a new CSV file
    # NG_CSV = 'gb_carbon_intensity'+str(Year_NG)+'.csv'
    # df.to_csv(NG_CSV, index=False)
''' --------------------------------------------- '''

#def main():
display_info = 0 # change to 1 to print yearly data
display_car_int = 0

# Read data and write
# with open(NG_CSV, 'r') as csv_file:
#     csv_dict_reader = csv.DictReader(csv_file, field_names)
#     next(csv_dict_reader)  # Skip header
#     for row in csv_dict_reader:
#         # Initialize lists for each day
#         car_int_list = []
#         Year_NG = float(row['Year'])
#         if year == Year_NG:
#             Elec_CarInt = float(row['forecast'])
#         # Read half-hourly data for the day
#         for _ in range(48):
#             #Elec_CarInt = float(row['forecast'])
#             car_int_list.append(Elec_CarInt)
#
#             row = next(csv_dict_reader)  # Move to next row
#
#         # Rank carbon intensity for the day
#         a = np.array(car_int_list)
#         sorted_indices_as = np.argsort(a)
#         sorted_indices_ds = np.argsort(-a)  # -a is descending
#         ranks_as = np.empty_like(sorted_indices_as)
#         ranks_as[sorted_indices_as] = np.arange(len(a))
#         ranks_ds = np.empty_like(sorted_indices_ds)
#         ranks_ds[sorted_indices_ds] = np.arange(len(a))
#
#         print(ranks_as)
#         print(ranks_ds)
#
#         # Write to CSV for ascending order
#         with open(Car_Int_as_CSV, 'a', newline='') as csv_w_file:
#             writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)
#             for element in ranks_as:
#                 writer.writerow({'Rank Ascending': element})
#
#         # Write to CSV for descending order
#         with open(Car_Int_ds_CSV, 'a', newline='') as csv_w_file:
#             writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)
#             for element in ranks_ds:
#                 writer.writerow({'Rank Descending': element})
#
#         day += 1  # Move to next day

    # for row in csv_dict_reader:
    #
    #     row_num = row_num + 1
    #     hour_count = hour_count + time_increment
    #     car_int_list.append(Elec_CarInt)
    #     if hour_count > 24:
    #         # Elec_CarInt
    #         a = np.array(car_int_list)
    #         sorted_indices_as = np.argsort(a)
    #         sorted_indices_ds = np.argsort(-a)  # -a is descending
    #         ranks_as = np.empty_like(sorted_indices_as)
    #         ranks_as[sorted_indices_as] = np.arange(len(a))
    #         car_int_as[day] = ranks_as
    #         if display_car_int:
    #             print(f'Ascending order: {ranks_as}')
    #         ranks_ds = np.empty_like(sorted_indices_ds)
    #         ranks_ds[sorted_indices_ds] = np.arange(len(a))
    #         car_int_ds[day] = ranks_ds
    #         if display_car_int:
    #             print(f'Descending order: {ranks_ds}')
    #             print(f'Day is {day}')
    #         # writes the array to row
    #         with open(Car_Int_as_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
    #             writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)  #
    #             for element in ranks_as:  # for element in ranks_as
    #                 writer.writerow({
    #                     #'Hour': hour_increment, # Hour always comes out as 24
    #                     'Rank Ascending': element
    #                 })
    #                 hour_increment = hour_increment + time_increment
    #         hour_count = time_increment
    #         hour_increment = 0.5
    #         with open(Car_Int_ds_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
    #             writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)  #
    #             for element in ranks_ds:  # for element in ranks_as
    #                 writer.writerow({
    #                     #'Hour': hour_increment, # Hour always comes out as 24
    #                     'Rank Descending': element
    #                 })
    #                 hour_increment = hour_increment + time_increment
    #         array_as = np.concatenate((array_as, ranks_as), axis=None)
    #         car_int_list = []  # clear list for next 24 hours
    #         hour_count = time_increment
    #         day = day + 1
    #         hour_increment = 0.5
    # #print(f'Ranked array: {array_as}')
    # Hour = 0
    # time_increment = 0.5
    # hour_count = 0.5
    # row_num = 0
    # day = 1
    # # read data and write
    # print(f'# # # # #')
    # print(f'National Grid ESO 24-hour forecast ranked')
    # #print(f'Writing National Grid ESO 24-hour forecast...')


with open(NG_CSV) as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names)
    next(csv_dict_reader)  # skips header
    # rank carbon intensity first
    print(f'# # # # #')
    print(f'Sorting National Grid ESO 24-hour forecast ranking...')
    for row in csv_dict_reader:
        Year_NG = float(row['Year'])
        if year == Year_NG:
            Elec_CarInt = float(row['forecast'])
        # else:
        #     break
        row_num = row_num + 1
        # Hour = float(row['Hour'])
        # Minute = float(row['Minute'])
        # if Minute == 30:
        #     Hour += 0.5
        hour_count = hour_count + time_increment
        car_int_list.append(Elec_CarInt)  # adds the carbon intensity period to the rank list (In future this will be changed to the forecast list)
        if hour_count > 24:
            # Elec_CarInt
            a = np.array(car_int_list)
            sorted_indices_as = np.argsort(a)
            sorted_indices_ds = np.argsort(-a)  # -a is descending
            ranks_as = np.empty_like(sorted_indices_as)
            ranks_as[sorted_indices_as] = np.arange(len(a))
            car_int_as[day] = ranks_as
            if display_car_int:
                print(f'Ascending order: {ranks_as}')
            ranks_ds = np.empty_like(sorted_indices_ds)
            ranks_ds[sorted_indices_ds] = np.arange(len(a))
            car_int_ds[day] = ranks_ds
            if display_car_int:
                print(f'Descending order: {ranks_ds}')
                print(f'Day is {day}')
            # writes the array to row
            with open(Car_Int_as_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
                writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)  #
                for element in ranks_as:  # for element in ranks_as
                    writer.writerow({
                        #'Hour': hour_increment, # Hour always comes out as 24
                        'Rank Ascending': element
                    })
                    hour_increment = hour_increment + time_increment
            hour_count = time_increment
            hour_increment = 0.5
            with open(Car_Int_ds_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
                writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)  #
                for element in ranks_ds:  # for element in ranks_as
                    writer.writerow({
                        #'Hour': hour_increment, # Hour always comes out as 24
                        'Rank Descending': element
                    })
                    hour_increment = hour_increment + time_increment
            array_as = np.concatenate((array_as, ranks_as), axis=None)
            car_int_list = []  # clear list for next 24 hours
            hour_count = time_increment
            day = day + 1
            hour_increment = 0.5
    #print(f'Ranked array: {array_as}')
    Hour = 0
    time_increment = 0.5
    hour_count = 0.5
    row_num = 0
    day = 1
    # read data and write
    print(f'# # # # #')
    print(f'National Grid ESO 24-hour forecast ranked')
    #print(f'Writing National Grid ESO 24-hour forecast...')


print(f'# # # # #')
print(f'Combining National Grid ESO 24-hour forecast with input data...')

# merging the csv files of input plus ascending and descending rank of carbon intensity
df = pd.concat(map(pd.read_csv, [input_CSV, NG_CSV, Car_Int_as_CSV, Car_Int_ds_CSV]), axis=1)#, ignore_index=True)
df.to_csv(combined_CSV)

print(f'# # # # #')
print(f'Running Building Energy Modelling System (BEMS)...')
# Previously loaded input_CSV, now loads from combined_CSV to include the ranked National Grid ESO carbon intensity
# field_names also needed changing to the field_names_combined
with open(combined_CSV, 'r') as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names_combined)
    next(csv_dict_reader)  # skips header
    for row in csv_dict_reader:
        #global row_num, hour_count, Battery_SoC, week_num, day_of_week, days_week_num, day, day_breakdown
        row_num = row_num + 1
        # Variable resets
        Export_Electricity = 0.0  # Resets the export electricity
        Efficiency_Losses_Sum = 0.0  # Resets the efficiency losses
        Battery_Status = "Ready"
        Hour = float(row['Hour'])
        hour_count = hour_count + time_increment
        Temperature = float(row['Temperature'])    # changes integer on every cycle
        Electricity_Usage = float(row['Electricity_Usage']) #+ Battery_Charge    # adds battery_charge from previous HH period
        Electricity_Usage_B = float(row['Electricity_Usage'])
        Electricity_Usage_C = float(row['Electricity_Usage'])
        #print(f'Step 1 {day} {Hour}: {Electricity_Usage} {Electricity_Usage_B} {Electricity_Usage_C}')
        Battery_Charge = 0 # resets the battery_level to 0 ready to charge/discharge again in next phase
        Elec_CarInt = float(row['forecast'])
        Gas = float(row['Gas']) #* Gas_Convert # used to be m3 which required conversion to kWh
        gas_heating = float(row['Heating'])
        gas_dhw = float(row['Hot Water'])
        gas_cooking = float(row['Cooking'])
        ind_hob_cooking = (gas_cooking * gas_hob_efficiency) / ind_hob_efficiency
        # Carbon Intensitites
        gas_heating_CarInt = gas_heating * Gas_CarInt
        gas_dhw_CarInt = gas_dhw * Gas_CarInt
        gas_cooking_CarInt = gas_cooking * Gas_CarInt
        ind_hob_cooking_CarInt = ind_hob_cooking * Elec_CarInt
        # car_int_list.append(Elec_CarInt)    # adds the carbon intensity period to the rank list (In future this will be changed to the forecast list)
        rank_as = float(row['Rank Ascending']) + 1
        rank_ds = float(row['Rank Descending']) + 1
        Elec_Cost = float(row['Cost'])
        Elec_Cost_Batt = Elec_Cost
        Sol_IR = float(row['Solar_IR'])
        SolPV_prod = Sol_IR * SolarPV_size * SolarPV_efficiency
        # Internal gains
        occupancy = np.ceil(occupants_max * get_occupancy_ratio(Hour))
        occupancy_gains = occupancy * occupant_gains_kW
        # solar_gains
        # equipment_gains
        internal_gains = occupancy_gains # + solar_gains + equipment_gains
        # Heat Transfer Coefficient
        daily_temp.append(Temperature)
        daily_energy = daily_energy + gas_heating
        # AHU
        # Come back to this - Heating system might fight the AHU heater and similar for cooling
        Fresh_Air = occupancy * AHU_FreshAirReq # l/s, add heat recovery and recirculation later
        AHU_Mix_Temp = (AHU_SupPercent * Temperature) + (AHU_HR * THM_temp)
        # -------------
        # Solar PV Phase
        # -------------
        #
        # Note solar PV efficiency covered above
        Excess_Solar_Production = Electricity_Usage - SolPV_prod
        Electricity_Usage_B = Electricity_Usage - SolPV_prod  # solar PV only mode using the solar PV
        if Excess_Solar_Production < 0:
            solprod_exceeds_usage = True
            Electricity_Usage_B_Export = (Electricity_Usage - SolPV_prod) # export/SEG
        else:
            solprod_exceeds_usage = False
            Electricity_Usage_B_Export = 0 # not export/SEG
            Excess_Solar_Production = 0
        # -------------
        # Battery Phase
        # -------------
        #
        Battery_efficiency = get_efficiency(Temperature)
        # Battery Condition setup
        if Battery_SoC >= Max_Capacity:
            bat_SOC_full = True
        elif Battery_SoC < Max_Capacity:
            bat_SOC_full = False
        if rank_as <= 1 + bat_num - 1: # integer each way is battery numbers
            low_carbon_period = True
        else:
            low_carbon_period = False
        if rank_as >= 48 - bat_num + 1: # integer each way is battery numbers
            high_carbon_period = True
        else:
            high_carbon_period = False
        #
        # Battery System flow
        if bat_SOC_full:
            if solprod_exceeds_usage:
                Battery_Status = "Export"
                battery_events.battery_event_export()
            elif solprod_exceeds_usage == False:
                if high_carbon_period:
                    Battery_Status = "Discharge"
                    battery_events.battery_event_discharge()
                elif high_carbon_period == False and Battery_SoC == Max_Capacity:
                    Battery_Status = "Full"
                    battery_events.battery_event_full()
                elif Battery_SoC > Max_Capacity:
                    #Battery_SoC == Max_Capacity
                    Battery_Status = "Export" # an additional export if the SoC goes above max capacity
                    battery_events.battery_event_export()
        elif bat_SOC_full == False:
            if solprod_exceeds_usage:
                Battery_Status = "Charge"
                battery_events.battery_event_charge()
            elif solprod_exceeds_usage == False:
                if low_carbon_period:
                    Battery_Status = "Charge"
                    battery_events.battery_event_charge()
                elif low_carbon_period == False:
                    Battery_Status = "Hold"
                    battery_events.battery_event_hold()
        #
        # Battery Actions
        if Battery_Status == "Export":
            Battery_SoC = Battery_SoC - (Excess_Solar_Production * Battery_efficiency) # adds the total excess on top (Remember solar is a -ve int)
            SoC_Excess = Battery_SoC - Max_Capacity # This is the excess to export, but charges the battery to full first
            Efficiency_Losses_Sum = Efficiency_Losses_Sum + -(Excess_Solar_Production * (1 - Battery_efficiency))
            Export_Electricity = Export_Electricity + SoC_Excess # exports excess energy
            Battery_SoC = Max_Capacity # Resets the battery to full
            Electricity_Usage_C = -Export_Electricity
            #print(f'Step 2 Export {day} {Hour}: {Electricity_Usage} {Electricity_Usage_B} {Electricity_Usage_C}')
            # Moved export elec costs further down
            # if Electricity_Usage_C < 0: # Export to grid if excess is more than consumed
            #     Elec_Cost = SEG
        elif Battery_Status == "Discharge":
            Battery_SoC = Battery_SoC - Max_Discharge_Capacity
            Electricity_Usage_C = Electricity_Usage - (Max_Discharge_Capacity * Battery_efficiency) - SolPV_prod # Reduces the electricity usage by the discharge amount
            if Electricity_Usage_C < 0: # This covers an event where discharge and solar exceed consumption
                Export_Electricity = Electricity_Usage_C
                #Electricity_Usage_C = 0
                #print(f'Step 2 Discharge {day} {Hour}: {Electricity_Usage} {Electricity_Usage_B} {Electricity_Usage_C}')
            Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Max_Discharge_Capacity * (1 - Battery_efficiency))
            #Elec_Cost = SEG # Smart Export Guarantee (SEG)
        #if Battery_Status == "Hold": # Do nothing
        elif Battery_Status == "Charge":
            Battery_SoC = Battery_SoC + SolPV_prod # SolPV_prod includes efficiency losses
            if low_carbon_period:
                if Battery_SoC + Max_Charge_Capacity <= Max_Capacity: # full charge without exceeding the batteries SoC
                    Battery_SoC = Battery_SoC + (Max_Charge_Capacity * Battery_efficiency)
                    Electricity_Usage_C = Electricity_Usage + Max_Charge_Capacity - SolPV_prod # Adds live electricity usage
                    #Battery_Charge = Max_Charge_Capacity # This charge amount is added to next period's electricity usage
                    Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Max_Charge_Capacity * (1 - Battery_efficiency))
                elif Battery_SoC + Max_Charge_Capacity > Max_Capacity: # full charge which exceeds the batteries SoC and requires balancing
                    Battery_SoC = Battery_SoC + Max_Charge_Capacity # include efficiency loss in a second..
                    SoC_Excess = Battery_SoC - Max_Capacity # calculates the excess
                    Battery_Charge = Max_Charge_Capacity - SoC_Excess # calculates actual energy used to charge
                    Electricity_Usage_C = Electricity_Usage + (Battery_Charge / Battery_efficiency) - SolPV_prod # now take into account efficiency loss
                    Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Battery_Charge * (1 - Battery_efficiency))
                    Battery_SoC = Max_Capacity  # Resets the battery to full
                #print(f'Step 2 Charge {day} {Hour}: {Electricity_Usage} {Electricity_Usage_B} {Electricity_Usage_C}')
        elif Battery_Status == "Full" or "Hold":
            Electricity_Usage_C = Electricity_Usage - SolPV_prod  # solarPV+Battery mode only
            #print(f'Step 2 Full/Hold {day} {Hour}: {Electricity_Usage} {Electricity_Usage_B} {Electricity_Usage_C}')
        #
        # -------------
        # Building Model
        # -------------
        #
        tao = Temperature  # this is the link to formula.py so at some point I'll change Temperature to tao
        heat_loss = (house.sum_heat_loss(tai, tao) / 1000) # W to kW
        #print(f'Heat loss is {heat_loss}')
        #
        if display_info == 1:
            #print('Hour ' + Hour + ' has a temperature of ' + Temperature + ' and uses ' + Electricity + ' kWh of electricity' + ' costing ' + Elec_Cost +' p/kWh and emitting ' + CarInt + ' g/kWh. Solar PV production is ' + SolPV_prod + ' kWh')
            print(f"Hour {Hour} has a temperature of {Temperature} and uses {Electricity_Usage} kWh of electricity costing {'{0:.1f}'.format(Elec_Cost)} p/kWh, emits {Elec_CarInt} g/kWh. Solar PV produces {'{0:.2f}'.format(SolPV_prod)} kWh")
        # Formulas
        Elec_Cost_Scen_A =  (Elec_Cost * Electricity_Usage) / 100
        ElecCarEm_Scen_A = (Elec_CarInt * Electricity_Usage) / 1000
        Gas_Cost_Scen_A = (Gas_Cost * Gas) / 100
        Gas_CarEm_Scen_A = (Gas_CarInt * Gas) / 1000
        ElecCarEm_Scen_B = (Elec_CarInt * Electricity_Usage_B) / 1000
        if Electricity_Usage_B < 0:     # simulates the SEG export payment
            Elec_Cost_Scen_B = (SEG * Electricity_Usage_B) / 100
            #Electricity_Usage_B = 0
        else:
            Elec_Cost_Scen_B = (Elec_Cost * Electricity_Usage_B) / 100
        ElecCarEm_Scen_C = (Elec_CarInt * Electricity_Usage_C) / 1000
        if Electricity_Usage_C < 0:     # simulates the SEG export payment
            Elec_Cost_Scen_C = (SEG * Electricity_Usage_C) / 100
            #Electricity_Usage_C = 0
        else:
            Elec_Cost_Scen_C = (Elec_Cost * Electricity_Usage_C) / 100
        #print(f'Step 3 {day} {Hour}: {Electricity_Usage} {Electricity_Usage_B} {Electricity_Usage_C}')
        #
        # This corrects Scen_B overall energ consumption after solar production, but allows SEG and negative carbon emissions
        # if Excess_Solar_Production < 0:
        #     Electricity_Usage_B = 0
        # else:
        #     Electricity_Usage_B = Electricity_Usage - SolPV_prod
        #
        #day_dict[Hour] = day_list  # puts lists within the dictionary
        #
        # ------------------
        # Thermal Mass Model
        # ------------------
        # Setpoint temperatures
        HeatingSetpointTemp = get_setpoint(Hour,heating_setpoint_times, heating_setpoint_values)
        CoolingSetpointTemp = get_setpoint(Hour, cooling_setpoint_times, cooling_setpoint_values)
        # Thermal Output
        if Prev_THM_temp < HeatingSetpointTemp:
            Thermal_Status = 'Heating'
            HeatingOutput = HeatMax_kW * 1 # ramp this ratio down later with controls
            CoolingOutput = 0
        elif Prev_THM_temp > CoolingSetpointTemp:
            Thermal_Status = 'Cooling'
            CoolingOutput = CoolMax_kW * 1 # ramp this ratio down later with controls
            HeatingOutput = 0
        else:
            Thermal_Status = 'Comfort'
            HeatingOutput = 0
            CoolingOutput = 0
        # print(f'Heating setpoint: {get_setpoint(Hour,heating_setpoint_times, heating_setpoint_values)} at {Hour}')
        # print(f'Cooling setpoint: {get_setpoint(Hour, cooling_setpoint_times, cooling_setpoint_values)} at {Hour}')
        TotalHeatLoss = BuildingHeatLoss * (Prev_THM_temp - Temperature)
        THM_temp = THM_calc(Prev_THM_temp, internal_gains, HeatingOutput, CoolingOutput, TotalHeatLoss, THM_kWhpoK)
        # Error check for thermal mass temperature (Clearly not realistic - add error message later)
        if THM_temp < -20:
            THM_temp = -20
        elif THM_temp > 50:
            THM_temp = 50
        print(f'Thermal Mass temperature: {THM_temp}oC, TotalHeatLoss: {TotalHeatLoss}, THM_kWhpoK: {THM_kWhpoK}')
        Prev_THM_temp = THM_temp
        # --------------------------------
        day_dict[day] = {Hour: {'Week_Number': week_num,
                  'Day_of_week': day_of_week,
                  'Day_Week_Number': days_week_num,
                  'Temperature': Temperature,
                  'Electricity_Usage': Electricity_Usage,
                  'Carbon Intensity': Elec_CarInt,
                  'Electricity Cost': Elec_Cost,
                  'Solar_IR': Sol_IR,
                  'SolPV_prod': SolPV_prod,
                  'Gas': Gas,
                  'Heating': gas_heating,
                  'Hot Water': gas_dhw,
                  'Cooking': gas_cooking,
                  'Induction Cooking': ind_hob_cooking,
                  'Gas Heating Carbon Intensity': gas_heating_CarInt,
                  'Gas DHW Carbon Intensity': gas_dhw_CarInt,
                  'Gas Cooking Carbon Intensity': gas_cooking_CarInt,
                  'Induction Cooking Carbon Intensity': ind_hob_cooking_CarInt,
                  'Rank Ascending': rank_as,
                  'Rank Descending': rank_ds,
                  'Elec_Cost_Scen_A': Elec_Cost_Scen_A,
                  'ElecCarEm_Scen_A': ElecCarEm_Scen_A,
                  'Gas_Cost_Scen_A': Gas_Cost_Scen_A,
                  'Gas_CarEm_Scen_A': Gas_CarEm_Scen_A,
                  'Excess_Solar_Production': Excess_Solar_Production,
                  'Efficiency_Losses': Efficiency_Losses_Sum,
                  'Battery_efficiency': Battery_efficiency,
                  'Battery_SoC': Battery_SoC,
                  'Export_Electricity': Export_Electricity,
                  'Battery_Status': Battery_Status,
                  'Electricity_Usage_Scen_B': Electricity_Usage_B,
                  'Elec_Cost_Scen_B': Elec_Cost_Scen_B,
                  'ElecCarEm_Scen_B': ElecCarEm_Scen_B,
                  'Electricity_Usage_Scen_C': Electricity_Usage_C,
                  'Elec_Cost_Scen_C': Elec_Cost_Scen_C,
                  'ElecCarEm_Scen_C': ElecCarEm_Scen_C,
                  'Heat Loss': heat_loss,
                  'Occupancy': occupancy,
                  'Occupant Gains': occupancy_gains,
                  'Thermal Mass Temperature': THM_temp,
                  'Heating Setpoint': HeatingSetpointTemp,
                  'Cooling Setpoint': CoolingSetpointTemp,
                  'Thermal Status': Thermal_Status,
                  'Heating Output': HeatingOutput,
                  'Cooling Output': CoolingOutput,
                  }}

        Y[day][Hour] = {'Week_Number': week_num,
                  'Day_of_week': day_of_week,
                  'Day_Week_Number': days_week_num,
                  'Temperature': Temperature,
                  'Electricity_Usage': Electricity_Usage,
                  'Carbon Intensity': Elec_CarInt,
                  'Electricity Cost': Elec_Cost,
                  'Solar_IR': Sol_IR,
                  'SolPV_prod': SolPV_prod,
                  'Gas': Gas,
                  'Heating': gas_heating,
                  'Hot Water': gas_dhw,
                  'Cooking': gas_cooking,
                  'Induction Cooking': ind_hob_cooking,
                  'Gas Heating Carbon Intensity': gas_heating_CarInt,
                  'Gas DHW Carbon Intensity': gas_dhw_CarInt,
                  'Gas Cooking Carbon Intensity': gas_cooking_CarInt,
                  'Induction Cooking Carbon Intensity': ind_hob_cooking_CarInt,
                  'Rank Ascending': rank_as,
                  'Rank Descending': rank_ds,
                  'Elec_Cost_Scen_A': Elec_Cost_Scen_A,
                  'ElecCarEm_Scen_A': ElecCarEm_Scen_A,
                  'Gas_Cost_Scen_A': Gas_Cost_Scen_A,
                  'Gas_CarEm_Scen_A': Gas_CarEm_Scen_A,
                  'Excess_Solar_Production': Excess_Solar_Production,
                  'Efficiency_Losses': Efficiency_Losses_Sum,
                  'Battery_efficiency': Battery_efficiency,
                  'Battery_SoC': Battery_SoC,
                  'Export_Electricity': Export_Electricity,
                  'Battery_Status': Battery_Status,
                  'Electricity_Usage_Scen_B': Electricity_Usage_B,
                  'Elec_Cost_Scen_B': Elec_Cost_Scen_B,
                  'ElecCarEm_Scen_B': ElecCarEm_Scen_B,
                  'Electricity_Usage_Scen_C': Electricity_Usage_C,
                  'Elec_Cost_Scen_C': Elec_Cost_Scen_C,
                  'ElecCarEm_Scen_C': ElecCarEm_Scen_C,
                  'Heat Loss': heat_loss,
                  'Occupancy': occupancy,
                  'Occupant Gains': occupancy_gains,
                  'Thermal Mass Temperature': THM_temp,
                  'Heating Setpoint': HeatingSetpointTemp,
                  'Cooling Setpoint': CoolingSetpointTemp,
                  'Thermal Status': Thermal_Status,
                  'Heating Output': HeatingOutput,
                  'Cooling Output': CoolingOutput,
                  }
        sum_Electricity_Usage += Electricity_Usage
        sum_Electricity_Usage_B += Electricity_Usage_B
        sum_Electricity_Usage_C += Electricity_Usage_C

        # Writes the daily output CSV file
        #elec_dict[Hour] = Electricity_Usage
        Sol_IR_dict[Hour] = Sol_IR
        write_row_hourly()  # This triggers the write_row trigger to write all information to the CSV data file
        if hour_count > 24:
            list = Sol_IR_dict.values()
            # HTC
            if show_htc:
                print(f"Day {day}")
                print(f"Daily energy used for heating is {round(daily_energy, 2)} kWh")
                print(f"Average DOT is {round(statistics.mean(daily_temp), 2)} degrees C")
                print(f"Energy required to heat property from {dot}C to {tsp}C is {HTC_calc()} kW")
            hour_count = time_increment
            index = days_week.index(day_of_week)
            # Start a new week
            if days_week_num == 7: # end of week
                days_week_num = 1
            else:
                days_week_num = days_week_num + 1
            # Start a new week
            if index >= 6:
                day_of_week = 'Mon'
            else:
                day_of_week = days_week[index + 1]
            # Setup week number, starting from the day the year started with, rather than always a Monday - So all weeks are 7 days
            if days_week_num == year_start_day_num and day > 6:
                week_num = week_num + 1
            #Y[day] = D
            day = day + 1
            day_dict[day] = {}
            #if day < 60 or day > 335:  # temporary crude way of returning heating days, could also try heating amount
            htc_list.append(HTC_calc())
            # Clear HTC data
            daily_temp.clear()
            daily_energy = 0

print(f'# # # # #')
today = date.today()
print("Today's date is ", today, " which is a",days_week[today.weekday()])    # also use today.day, today.month, today.year
sum_htc = sum(htc_list)
print(f"Heating Season Energy: {round(sum_htc, 2)} kW")
print(f"{len(htc_list)} heating periods")
htc_final = sum(htc_list) / len(htc_list)
print(f"Average Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW")
print(f'# # # # #')
print(f"Calculation completed") # Next calculate mean of htc_list ie all HTC in heating season
with open(Output_Text, 'a') as f:
    f.write(f"Todays date is {today} which is a {days_week[today.weekday()]}{2*chr(10)}")
    f.write(f"The battery carried out {battery_events.event_hold} hold events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_full} full events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_charge} charge events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_discharge} discharge events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_export} export events{2*chr(10)}")
    f.write(f"Average Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW{2*chr(10)}")
    f.write(f"Scenario A Electricity Usage {round(sum_Electricity_Usage, 2)} kWh{chr(10)}")
    f.write(f"Scenario B Electricity Usage {round(sum_Electricity_Usage_B, 2)} kWh{chr(10)}")
    f.write(f"Scenario C Electricity Usage {round(sum_Electricity_Usage_C, 2)} kWh{chr(10)}")


# today.weekday 0 is monday, 6 is sunday
#print(day_of_week)

# Read the input CSV file
#input_file = f'{Building_Name}_Output Data.csv'
df = pd.read_csv(New_CSV)

print(f"Data saved to {New_CSV}.") # Not sure why this is different to output_file

# Define a dictionary with the number of days in each month (ignoring leap years)
month_days = {
    1: 31,  # January
    2: 28,  # February
    3: 31,  # March
    4: 30,  # April
    5: 31,  # May
    6: 30,  # June
    7: 31,  # July
    8: 31,  # August
    9: 30,  # September
    10: 31,  # October
    11: 30,  # November
    12: 31  # December
}

# Add new columns for day of the month and month
df['day_of_month'] = df['Day Number'] % 31
df['month'] = (df['Day Number']) // 31 + 1

# Group the data by day and calculate sum and average for each day
grouped_data = df.groupby('Day Number').agg({'day_of_month': 'first', 'month': 'first', 'Electricity Usage': 'sum', 'Electricity_Usage_Scen_B': 'sum', 'Electricity_Usage_Scen_C': 'sum', 'Ambient Temperature': 'mean'}).reset_index()

# Save the grouped data to a new CSV file
output_file = 'Annual Data.csv'
grouped_data.to_csv(output_file, index=False)

''' Machine Learning part that isn't finished
if ML:
    day_breakdown = day_breakdown + 1
    #print(f"Day {day_breakdown} of {year} is a {Y[day_breakdown][Hour]['Day_of_week']}")   # +1 day because it starts at 0
    #print(day_dict[day_breakdown])
    if display_info:
        print(f'# # # # #')
        print(f'Sorted carbon intensity on day {day_breakdown} is {car_int_as[day_breakdown]}')
    #print(day_list)
    if display_info:
        print(f'# # # # #')
        print(D)
    #for i in range(1,24):
    if display_info:
        print(f'# # # # #')
        print(Y[1])


    df_model = pd.read_csv("Output Data.csv")
    # Define the prediction task by naming the target column
    pdef = ProblemDefinition.from_dict(
        {
            "target": "Electricity Usage",  # column you want to predict
        }
    )

    # Generate JSON-AI code to model the problem
    json_ai = json_ai_from_problem(df_model, problem_definition=pdef)

    # OPTIONAL - see the JSON-AI syntax
    # print(json_ai.to_json())

    # Generate python code
    code = code_from_json_ai(json_ai)

    # OPTIONAL - see generated code
    # print(code)

    # Create a predictor from python code
    predictor = predictor_from_code(code)

    # Train a model end-to-end from raw data to a finalized predictor
    predictor.learn(df_model)

    # Make the train/test splits and show predictions for a few examples
    test_df = predictor.split(predictor.preprocess(df_model))["test"]
    preds = predictor.predict(test_df).iloc[:10]
    print(preds)
'''

# if __name__ == "__main__":
#     main()

# Load the CSV file
print(f'Analysing data output and graphing...')
data = pd.read_csv(New_CSV)

# Extract the relevant data
day_numbers = data['Day Number']
electricity_usage = data['Electricity Usage']
electricity_usage_scen_b = data['Electricity_Usage_Scen_B']
electricity_usage_scen_c = data['Electricity_Usage_Scen_C']
solar_production = data['Solar Production']  # Assuming this column exists
elec_carbon_emissions = data['Electricity Carbon Emissions Scen A']
elec_carbon_emissions_scen_b = data['ElecCarEm_Scen_B']
elec_carbon_emissions_scen_c = data['ElecCarEm_Scen_C']
elec_cost = data['Electricity Cost Scen A']
elec_cost_scen_b = data['Elec_Cost_Scen_B']
elec_cost_scen_c = data['Elec_Cost_Scen_C']

# Aggregate data by day
daily_electricity_usage = electricity_usage.groupby(np.arange(len(electricity_usage)) // 48).sum()
daily_electricity_usage_scen_b = electricity_usage_scen_b.groupby(np.arange(len(electricity_usage_scen_b)) // 48).sum()
daily_electricity_usage_scen_c = electricity_usage_scen_c.groupby(np.arange(len(electricity_usage_scen_c)) // 48).sum()
daily_solar_production = solar_production.groupby(np.arange(len(solar_production)) // 48).sum()
daily_elec_carbon_emissions = elec_carbon_emissions.groupby(np.arange(len(elec_carbon_emissions)) // 48).sum()
daily_elec_carbon_emissions_scen_b = elec_carbon_emissions_scen_b.groupby(np.arange(len(elec_carbon_emissions_scen_b)) // 48).sum()
daily_elec_carbon_emissions_scen_c = elec_carbon_emissions_scen_c.groupby(np.arange(len(elec_carbon_emissions_scen_c)) // 48).sum()
daily_elec_cost = elec_cost.groupby(np.arange(len(elec_cost)) // 48).sum()
daily_elec_cost_scen_b = elec_cost_scen_b.groupby(np.arange(len(elec_cost_scen_b)) // 48).sum()
daily_elec_cost_scen_c = elec_cost_scen_c.groupby(np.arange(len(elec_cost_scen_c)) // 48).sum()

# Function to convert day number to date, adjusting for non-leap year
def day_to_date(day_number, year=2023):
    """
    Convert a day number in a year to a date, adjusting for non-leap year.
    """
    if day_number > 365:
        day_number = 365  # Adjust to the last day of the year
    # Convert day_number to Python int
    day_number = int(day_number)
    date = datetime(year, 1, 1) + timedelta(days=day_number - 1)
    return date

# Convert day numbers to dates, excluding any beyond day 365
dates = day_numbers.unique()
date_labels = [day_to_date(day) for day in dates if day <= 365]

# Adjust the data arrays to match the date labels
adjusted_daily_electricity_usage = daily_electricity_usage[:len(date_labels)]
adjusted_daily_electricity_usage_scen_b = daily_electricity_usage_scen_b[:len(date_labels)]
adjusted_daily_electricity_usage_scen_c = daily_electricity_usage_scen_c[:len(date_labels)]
adjusted_daily_solar_production = daily_solar_production[:len(date_labels)]
adjusted_daily_elec_carbon_emissions = daily_elec_carbon_emissions[:len(date_labels)]
adjusted_daily_elec_carbon_emissions_scen_b = daily_elec_carbon_emissions_scen_b[:len(date_labels)]
adjusted_daily_elec_carbon_emissions_scen_c = daily_elec_carbon_emissions_scen_c[:len(date_labels)]
adjusted_daily_elec_cost = daily_elec_cost[:len(date_labels)]
adjusted_daily_elec_cost_scen_b = daily_elec_cost_scen_b[:len(date_labels)]
adjusted_daily_elec_cost_scen_c = daily_elec_cost_scen_c[:len(date_labels)]

# Sum of annual electricity consumption
annual_electricity_usage = adjusted_daily_electricity_usage.sum()
annual_electricity_usage_scen_b = adjusted_daily_electricity_usage_scen_b.sum()
annual_electricity_usage_scen_c = adjusted_daily_electricity_usage_scen_c.sum()
annual_elec_carbon_emissions = adjusted_daily_elec_carbon_emissions.sum()
annual_elec_carbon_emissions_scen_b = adjusted_daily_elec_carbon_emissions_scen_b.sum()
annual_elec_carbon_emissions_scen_c = adjusted_daily_elec_carbon_emissions_scen_c.sum()
annual_elec_cost = adjusted_daily_elec_cost.sum()
annual_elec_cost_scen_b = adjusted_daily_elec_cost_scen_b.sum()
annual_elec_cost_scen_c = adjusted_daily_elec_cost_scen_c.sum()

# Create the plot with dates and add text for annual consumption
plt.figure(figsize=(15, 6))

plt.plot(date_labels, adjusted_daily_electricity_usage, label=f'Electricity Usage (Historic) {annual_electricity_usage:.2f} kWh', color='grey')
plt.plot(date_labels, adjusted_daily_electricity_usage_scen_b, label=f'Electricity Usage (Scenario B) {annual_electricity_usage_scen_b:.2f} kWh', color='green')
plt.plot(date_labels, adjusted_daily_electricity_usage_scen_c, label=f'Electricity Usage (Scenario C) {annual_electricity_usage_scen_c:.2f} kWh', color='cornflowerblue')
# I've taken solar generation data off as it's a bit noisy
#plt.plot(date_labels, adjusted_daily_solar_production, label='Solar Production', color='orange')

plt.xlabel('Date')
plt.ylabel('Total Daily Consumption/Production')
plt.title('Daily Electricity Consumption and Solar Production Throughout the Year')

# Adding text for annual consumption
# plt.text(datetime(2023, 4, 1), min(adjusted_daily_electricity_usage.min(), adjusted_daily_electricity_usage_scen_b.min(), adjusted_daily_electricity_usage_scen_c.min()),
#          f'Annual Electricity Usage: {annual_electricity_usage:.2f}\nAnnual Electricity Usage (Scen B): {annual_electricity_usage_scen_b:.2f}\nAnnual Electricity Usage (Scen C): {annual_electricity_usage_scen_c:.2f}',
#          fontsize=10, bbox=dict(facecolor='white', alpha=0.5))

plt.text(datetime(2023, 5, 1), min(adjusted_daily_electricity_usage.min(), adjusted_daily_electricity_usage_scen_b.min(), adjusted_daily_electricity_usage_scen_c.min()),
         f'Annual Carbon Emissions: {annual_elec_carbon_emissions:.2f} kgCO2\nAnnual Carbon Emissions (Scen B): {annual_elec_carbon_emissions_scen_b:.2f} kgCO2\nAnnual Carbon Emissions (Scen C): {annual_elec_carbon_emissions_scen_c:.2f} kgCO2',
         fontsize=10, bbox=dict(facecolor='white', alpha=0.5))

plt.text(datetime(2023, 10, 1), min(adjusted_daily_electricity_usage.min(), adjusted_daily_electricity_usage_scen_b.min(), adjusted_daily_electricity_usage_scen_c.min()),
         f'Annual Electricity Cost: £{annual_elec_cost:,.2f}\nAnnual Electricity Cost (Scen B): £{annual_elec_cost_scen_b:,.2f}\nAnnual Electricity Cost (Scen C): £{annual_elec_cost_scen_c:,.2f}',
         fontsize=10, bbox=dict(facecolor='white', alpha=0.5))

plt.legend()
plt.grid(True)

# Formatting the date labels on the x-axis
plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%b-%d'))
plt.gca().xaxis.set_major_locator(mdates.MonthLocator())
plt.gcf().autofmt_xdate()  # Rotate date labels

print(f'Loading graph')
plt.show()
