from uagents.setup import fund_agent_if_low
from uagents import Agent, Context, Protocol, Model
import random
from pydantic import Field
#from ai_engine import UAgentResponse, UAgentResponseType
import sys

from enum import Enum
from typing import Optional, List, Literal

class UAgentResponseType(Enum):
    FINAL = "final"
    ERROR = "error"
    VALIDATION_ERROR = "validation_error"
    SELECT_FROM_OPTIONS = "select_from_options"
    FINAL_OPTIONS = "final_options"

class KeyValue(Model):
    key: str
    value: str

class UAgentResponse(Model):
    version: Literal["v1"] = "v1"
    type: UAgentResponseType
    request_id: Optional[str]
    agent_address: Optional[str]
    message: Optional[str]
    options: Optional[List[KeyValue]]
    verbose_message: Optional[str]
    verbose_options: Optional[List[KeyValue]]

dungeons = Agent(
    name="letsrolladice",
    port=6145,
    seed="SOME STRINGS",
    endpoint=["http://**************:6145/submit"],
)

fund_agent_if_low(dungeons.wallet.address())


@dungeons.on_event("startup")
async def hi(ctx: Context):
    ctx.logger.info(dungeons.address)


class Request(Model):
    dice_sides: int = Field(description="How many sides does your dice need?")


dice_roll_protocol = Protocol("letsrolladice")


@dice_roll_protocol.on_message(model=Request, replies={UAgentResponse})
async def roll_dice(ctx: Context, sender: str, msg: Request):
    result = str(random.randint(1, msg.dice_sides))
    message = f"Dice roll result: {result}"
    await ctx.send(
        sender, UAgentResponse(message=message, type=UAgentResponseType.FINAL)
    )


dungeons.include(dice_roll_protocol, publish_manifest=True)

dungeons.run()