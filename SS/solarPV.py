
import requests
import matplotlib.pyplot as plt
from PIL import Image
import matplotlib.cbook as cbook
from io import StringIO
import pandas as pd
import json

PARAMS = {
    'outputformat': 'json',
    'browser': 1
}
tool_name = 'PVcalc'
lat = 51.507
lon = -0.128
raddatabase = 'PVGIS-SARAH2' # default is PVGIS-SARAH2
peakpower = 8.0
loss = 14.0
pvtechchoice = 'crystSi'
angle = 35
aspect = 0 # 0=south, 90=west, -90=east

solarPV_url = f'https://re.jrc.ec.europa.eu/api/v5_2/{tool_name}?lat={lat}&lon={lon}&raddatabase={raddatabase}' \
              f'&peakpower={peakpower}&loss={loss}&pvtechchoice={pvtechchoice}&angle={angle}&aspect={aspect}'

#print(solarPV_url)

response = requests.get(url=solarPV_url, params=PARAMS)

if response.status_code == 200:
    print("Passed")
data = response.json()

#print(data)

#print(type(data))
#print(data['inputs']['location']['latitude'])
#print(f'Latitude is {data["inputs"]["location"]["latitude"]} degrees')
#print(data['outputs']['monthly']['fixed'][0]['E_m'])
for p_info in data['outputs']['monthly']['fixed']:
    print(p_info['E_m'])

print(f'Total annual generation is {data["outputs"]["totals"]["fixed"]["E_y"]} kWh')

# Extract monthly production data
monthly_production = data['outputs']['monthly']['fixed']

# Extracting the month names and the corresponding values
energy_production = [value['E_m'] for value in monthly_production]

# Data for each month
months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

# Load the image file
image_path = 'H:\\My Documents\\Ian\\Python\\Energy Model\\BEMSS\\Logo.png'  # Update this to the path of your image file
logo = Image.open(image_path)


# Creating the line graph
#plt.figure(figsize=(10, 6))
fig, main_plt = plt.subplots(figsize=(10, 6))
main_plt.plot(months, energy_production, marker='o', color='b', label='Monthly Energy Production (kWh)')

# Adding title and labels
main_plt.set_title('Monthly Solar PV Energy Production')
main_plt.set_xlabel('Month')
main_plt.set_ylabel('Energy Production (kWh)')
main_plt.grid(True)
main_plt.legend()

# Add an axes for the image (adjust the rect [left, bottom, width, height] as needed)
img_ax = fig.add_axes([0.1, 0.65, 0.25, 0.25], anchor='NE', zorder=1)  # Adjust the position and size
#img_ax.imshow(logo)
img_ax.axis('off')  # Turn off axis for the image
# Adding the image
# Update the extent and location as needed
imagebox = cbook.get_sample_data(image_path, asfileobj=False)
image = plt.imread(imagebox)
img_ax.imshow(image, aspect='auto', extent=[1.8, 2, 200, 400], alpha=0.5)  # Adjust extent for position and size

# Display the graph
plt.show()