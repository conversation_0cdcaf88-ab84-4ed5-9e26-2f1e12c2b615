# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
import csv
from datetime import date
from datetime import time
from datetime import datetime
from random import randrange




# Use upper case LETTERS for defining a constant that never changes

day = 0
#prevday = 0

# Figures setup
prevday_dict = {}
day_dict = {}           # list can have a mixture of types eg integers, strings
nextday_dict = {}       # order of dictionary is not important
# order of list is significant
# day_list[0] looks first item in list
day_list = []
daily_temp = [1]        # stores all daily temperatures (8760 floats)
elec = []
average_temp = [1]      # for some reason the list needed a value in here to append
average_elec = [1]
average_carint = [1]

# Days and calendar setup
year = 2019
days_week = ["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]
day_of_week = {}        #dictionary
day = 0

if year == 2019:        # This will change depending on the year. eg. 2019 started on a Tuesday
    year_start_day = "Mon" # This is day 0 (ie 31st, 2019 started on Tuesday. Bodged this due to unknown error on day_of_week
    index = days_week.index(year_start_day)
    prevday = days_week[index - 1]
elif year == 2018:
    year_start_day = "Tue"
    index = days_week.index(year_start_day) # This and below sets prevday to the previous week day in the days_week list
    prevday = days_week[index - 1]

# General variables
global_warming = 0.0
Hour = 0
Temperature = 0.0
Electricity = 0.0
CarInt = 0.0
Elec_Cost = 0.0
day_breakdown = randrange(364)     # This is the day we use to breakdown for more info eg. temp, elec etc
Battery_Num = 3          # Number of site batteries
Carbon_Int = [1]
Carbon_Int_dict = {}        # trying this to label high/low carbon intensity
Carbon_Int_sort = [1]    # this will be used to sort the daily carbon emissions
Carbon_Int_sortR = [1]   # this will be used as above from descending order
Temperature_list = [1]
Electricity_list = [1]

AmbTemp = [1] # pre corrected hourly ambient air temperature
AmbTempCor = [1]  # The corrected ambient air temperature to half hourly average difference from hourly data

SolarPV_size = 48.0      # size of solar PV array in kWp
Daily_SolPV_prod = [1]

field_names_w = [
    'Day Number',
    'Day of Week',
    'Average Daily Temperature',
    'Average Electrical Usage',
    'Average Carbon Intensity',
    'Solar Irradiance',
    'Solar Production'
]

field_names_temp_w = [
    'Hour',
    'Ambient Temperature'
]

New_CSV = 'H:\\My Documents\\Ian\\Python\\Energy Model\\Output Data.csv'
New_CSV_temp = 'H:\\My Documents\\Ian\\Python\\Energy Model\\Output temp Data.csv'

with open(New_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w)
  writer.writeheader()

def write_row():
    with open(New_CSV,'a', newline='') as csv_w_file: # 'a' is append, 'w' is write
        writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w)
        writer.writerow({
            'Day Number': day,
            'Day of Week': day_of_week[day],
            'Average Daily Temperature': average_temp[day],
            'Average Electrical Usage': average_elec[day],
            'Average Carbon Intensity': average_carint[day],
            'Solar Irradiance' : Daily_SolPV_prod[day],
            'Solar Production': (Daily_SolPV_prod[day] * SolarPV_size)
        })

def write_row_temp():
    with open(New_CSV_temp,'a', newline='') as csv_w_file: # 'a' is append, 'w' is write
        writer = csv.DictWriter(csv_w_file, fieldnames=field_names_temp_w)
        writer.writerow({
            'Hour': Hour,
            'Ambient Temperature': AmbTemp
        })

# This creates the function that calls an average over 24 hours
def average(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x):
    return (a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x) / 24

def sum(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x):
    return (a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x)

# Sort ambient air temperature by inserting half hourly data to the available hourly data
# with open(New_CSV_temp, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
#   writer = csv.DictWriter(csv_w_file, fieldnames=field_names_temp_w)
#   writer.writeheader()
#
# field_names_temp = ['Hour', 'Temperature']
# with open('H:\\My Documents\\Ian\\Python\\Energy Model\\Ambient Temperature data.csv') as csv_file:
#     csv_dict_reader_temp = csv.DictReader(csv_file, field_names_temp)
#     next(csv_dict_reader_temp)  # skips header
#     print("Sorting ambient air temperature data...")
#     for row in csv_dict_reader_temp:
#         Hour = (row['Hour'])
#         Temperature = float(row['Temperature'])    # changes integer on every cycle
#
#         #AmbTemp.append(['Temperature'])
#
#         AmbTemp = (Temperature)
#         AmbTemp.insert(-1,0)
#         #AmbTemp[-1] = #(float(AmbTemp[-2]) + float(AmbTemp[-1])) / 2
#         write_row_temp()
#     # for i in range(len(AmbTemp)):
#     #     if i == 'HH':
#     #         AmbTemp[i] = 99
#     #         print("HH changed")
#     #print(len(AmbTemp))
#     print("Ambient Temperate list is " + str(AmbTemp))
#     #for row in csv_dict_reader_temp:
#     #    write_row_temp()




display_info = 0 # change to 1 to print yearly data
field_names = ['Hour', 'Temperature', 'Electricity', 'Carbon_Intensity_2018', 'Carbon_Intensity_2019', 'Cost', 'SolarPV_Prod']
with open('H:\\My Documents\\Ian\\Python\\Energy Model\\Basic data.csv') as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names)
    next(csv_dict_reader)  # skips header
    for row in csv_dict_reader:
        Hour = row['Hour']
        Temperature = float(row['Temperature'])    # changes integer on every cycle
        Electricity = float(row['Electricity'])    # as above, cleans below line
        if year == 2019:
            CarInt = row['Carbon_Intensity_2019']
        else:
            CarInt = row['Carbon_Intensity_2018']
        Elec_Cost = float(row['Cost'])
        SolPV_prod = float(row['SolarPV_Prod']) #* SolarPV_size
        #SolPV_prod = float(SolPV_prod) * SolarPV_size
        if display_info == 1:
            #print('Hour ' + Hour + ' has a temperature of ' + Temperature + ' and uses ' + Electricity + ' kWh of electricity' + ' costing ' + Elec_Cost +' p/kWh and emitting ' + CarInt + ' g/kWh. Solar PV production is ' + SolPV_prod + ' kWh')
            print(f"Hour {Hour} has a temperature of {Temperature} and uses {Electricity} kWh of electricity costing {'{0:.1f}'.format(Elec_Cost)} p/kWh, emits {CarInt} g/kWh. Solar PV produces {'{0:.2f}'.format(SolPV_prod)} kWh")
        day_list = [Temperature, Electricity]
        #day_dict[Hour] = day_list  # puts lists within the dictionary
        day_dict[Hour] = {'Temperature' : Temperature, 'Electricity' : Electricity, 'Carbon Intensity' : CarInt, 'SolPV_prod' : SolPV_prod}
        #day_dict[Hour] = {"Temperature" : Temperature}, {"Electricity" : Electricity}
        #daily_temp.append(Temperature)      # this is making 8760 data fields currently
        Carbon_Int.append(CarInt)

        # This is the end of day trigger at hour 24
        if (row['Hour'] == str(24)):
            # This starts with the average temperature data, and also stores it for later use
            average_temp[day] = (average(float(day_dict['1']['Temperature']), float(day_dict['2']['Temperature']),
                          float(day_dict['3']['Temperature']), float(day_dict['4']['Temperature']),
                          float(day_dict['5']['Temperature']), float(day_dict['6']['Temperature']),
                          float(day_dict['7']['Temperature']), float(day_dict['8']['Temperature']),
                          float(day_dict['9']['Temperature']), float(day_dict['10']['Temperature']),
                          float(day_dict['11']['Temperature']), float(day_dict['12']['Temperature']),
                          float(day_dict['13']['Temperature']), float(day_dict['14']['Temperature']),
                          float(day_dict['15']['Temperature']), float(day_dict['16']['Temperature']),
                          float(day_dict['17']['Temperature']), float(day_dict['18']['Temperature']),
                          float(day_dict['19']['Temperature']), float(day_dict['20']['Temperature']),
                          float(day_dict['21']['Temperature']), float(day_dict['22']['Temperature']),
                          float(day_dict['23']['Temperature']), float(day_dict['24']['Temperature'])))
            average_temp.append(average_temp[day])

            # This stores the hourly temperature to each day
            Temperature_list[day] = (float(day_dict['1']['Temperature']), float(day_dict['2']['Temperature']),
                               float(day_dict['3']['Temperature']), float(day_dict['4']['Temperature']),
                               float(day_dict['5']['Temperature']), float(day_dict['6']['Temperature']),
                               float(day_dict['7']['Temperature']), float(day_dict['8']['Temperature']),
                               float(day_dict['9']['Temperature']), float(day_dict['10']['Temperature']),
                               float(day_dict['11']['Temperature']), float(day_dict['12']['Temperature']),
                               float(day_dict['13']['Temperature']), float(day_dict['14']['Temperature']),
                               float(day_dict['15']['Temperature']), float(day_dict['16']['Temperature']),
                               float(day_dict['17']['Temperature']), float(day_dict['18']['Temperature']),
                               float(day_dict['19']['Temperature']), float(day_dict['20']['Temperature']),
                               float(day_dict['21']['Temperature']), float(day_dict['22']['Temperature']),
                               float(day_dict['23']['Temperature']), float(day_dict['24']['Temperature']))
            Temperature_list.append(Temperature)

            # This starts with the average electricity usage of that day and stores for later use
            average_elec[day] = (average(float(day_dict['1']['Electricity']), float(day_dict['2']['Electricity']),
                                         float(day_dict['3']['Electricity']), float(day_dict['4']['Electricity']),
                                         float(day_dict['5']['Electricity']), float(day_dict['6']['Electricity']),
                                         float(day_dict['7']['Electricity']), float(day_dict['8']['Electricity']),
                                         float(day_dict['9']['Electricity']), float(day_dict['10']['Electricity']),
                                         float(day_dict['11']['Electricity']), float(day_dict['12']['Electricity']),
                                         float(day_dict['13']['Electricity']), float(day_dict['14']['Electricity']),
                                         float(day_dict['15']['Electricity']), float(day_dict['16']['Electricity']),
                                         float(day_dict['17']['Electricity']), float(day_dict['18']['Electricity']),
                                         float(day_dict['19']['Electricity']), float(day_dict['20']['Electricity']),
                                         float(day_dict['21']['Electricity']), float(day_dict['22']['Electricity']),
                                         float(day_dict['23']['Electricity']), float(day_dict['24']['Electricity'])))
            average_elec.append(average_elec[day])

            # This stores the hourly temperature to each day
            # for i in day_dict:
            #     Electricity_list[day] = float(day_dict[i]['Electricity'])
            #     Electricity_list.append(Electricity)
                #average_carint[day] = average(float(day_dict[i]['Carbon Intensity']))
                #average_carint.append(average_carint[day])
            Electricity_list[day] = (float(day_dict['1']['Electricity']), float(day_dict['2']['Electricity']),
                                     float(day_dict['3']['Electricity']), float(day_dict['4']['Electricity']),
                                     float(day_dict['5']['Electricity']), float(day_dict['6']['Electricity']),
                                     float(day_dict['7']['Electricity']), float(day_dict['8']['Electricity']),
                                     float(day_dict['9']['Electricity']), float(day_dict['10']['Electricity']),
                                     float(day_dict['11']['Electricity']), float(day_dict['12']['Electricity']),
                                     float(day_dict['13']['Electricity']), float(day_dict['14']['Electricity']),
                                     float(day_dict['15']['Electricity']), float(day_dict['16']['Electricity']),
                                     float(day_dict['17']['Electricity']), float(day_dict['18']['Electricity']),
                                     float(day_dict['19']['Electricity']), float(day_dict['20']['Electricity']),
                                     float(day_dict['21']['Electricity']), float(day_dict['22']['Electricity']),
                                     float(day_dict['23']['Electricity']), float(day_dict['24']['Electricity']))
            Electricity_list.append(Electricity)

            # Averages daily carbon intensity
            average_carint[day] = (average(float(day_dict['1']['Carbon Intensity']), float(day_dict['2']['Carbon Intensity']),
                                float(day_dict['3']['Carbon Intensity']), float(day_dict['4']['Carbon Intensity']),
                                float(day_dict['5']['Carbon Intensity']), float(day_dict['6']['Carbon Intensity']),
                                float(day_dict['7']['Carbon Intensity']), float(day_dict['8']['Carbon Intensity']),
                                float(day_dict['9']['Carbon Intensity']), float(day_dict['10']['Carbon Intensity']),
                                float(day_dict['11']['Carbon Intensity']), float(day_dict['12']['Carbon Intensity']),
                                float(day_dict['13']['Carbon Intensity']), float(day_dict['14']['Carbon Intensity']),
                                float(day_dict['15']['Carbon Intensity']), float(day_dict['16']['Carbon Intensity']),
                                float(day_dict['17']['Carbon Intensity']), float(day_dict['18']['Carbon Intensity']),
                                float(day_dict['19']['Carbon Intensity']), float(day_dict['20']['Carbon Intensity']),
                                float(day_dict['21']['Carbon Intensity']), float(day_dict['22']['Carbon Intensity']),
                                float(day_dict['23']['Carbon Intensity']), float(day_dict['24']['Carbon Intensity'])))
            average_carint.append(average_carint[day])

            # This stores the hourly carbon intensity to each day
            Carbon_Int[day] = (float(day_dict['1']['Carbon Intensity']), float(day_dict['2']['Carbon Intensity']),
                                float(day_dict['3']['Carbon Intensity']), float(day_dict['4']['Carbon Intensity']),
                                float(day_dict['5']['Carbon Intensity']), float(day_dict['6']['Carbon Intensity']),
                                float(day_dict['7']['Carbon Intensity']), float(day_dict['8']['Carbon Intensity']),
                                float(day_dict['9']['Carbon Intensity']), float(day_dict['10']['Carbon Intensity']),
                                float(day_dict['11']['Carbon Intensity']), float(day_dict['12']['Carbon Intensity']),
                                float(day_dict['13']['Carbon Intensity']), float(day_dict['14']['Carbon Intensity']),
                                float(day_dict['15']['Carbon Intensity']), float(day_dict['16']['Carbon Intensity']),
                                float(day_dict['17']['Carbon Intensity']), float(day_dict['18']['Carbon Intensity']),
                                float(day_dict['19']['Carbon Intensity']), float(day_dict['20']['Carbon Intensity']),
                                float(day_dict['21']['Carbon Intensity']), float(day_dict['22']['Carbon Intensity']),
                                float(day_dict['23']['Carbon Intensity']), float(day_dict['24']['Carbon Intensity']))

            # Sums the solar production
            Daily_SolPV_prod[day] = (
                sum(float(day_dict['1']['SolPV_prod']), float(day_dict['2']['SolPV_prod']),
                        float(day_dict['3']['SolPV_prod']), float(day_dict['4']['SolPV_prod']),
                        float(day_dict['5']['SolPV_prod']), float(day_dict['6']['SolPV_prod']),
                        float(day_dict['7']['SolPV_prod']), float(day_dict['8']['SolPV_prod']),
                        float(day_dict['9']['SolPV_prod']), float(day_dict['10']['SolPV_prod']),
                        float(day_dict['11']['SolPV_prod']), float(day_dict['12']['SolPV_prod']),
                        float(day_dict['13']['SolPV_prod']), float(day_dict['14']['SolPV_prod']),
                        float(day_dict['15']['SolPV_prod']), float(day_dict['16']['SolPV_prod']),
                        float(day_dict['17']['SolPV_prod']), float(day_dict['18']['SolPV_prod']),
                        float(day_dict['19']['SolPV_prod']), float(day_dict['20']['SolPV_prod']),
                        float(day_dict['21']['SolPV_prod']), float(day_dict['22']['SolPV_prod']),
                        float(day_dict['23']['SolPV_prod']), float(day_dict['24']['SolPV_prod'])))
            Daily_SolPV_prod.append(Daily_SolPV_prod[day])


            # This adds the day number after every 24 hour
            if day == 0:
                day_of_week[day] = year_start_day   # day_of_week["2"] looks up dict reference 2
            # elif day == 365: break      # breaks all the below, but didn't seem to have any effect. Possibly because it runs for each line in CSV file
            day = day + 1
            prevday = day - 1
            if day_of_week[prevday] == "Mon":
                day_of_week[day] = "Tue"
            elif day_of_week[prevday] == "Tue":
                day_of_week[day] = "Wed"
            elif day_of_week[prevday] == "Wed":
                day_of_week[day] = "Thu"
            elif day_of_week[prevday] == "Thu":
                day_of_week[day] = "Fri"
            elif day_of_week[prevday] == "Fri":
                day_of_week[day] = "Sat"
            elif day_of_week[prevday] == "Sat":
                day_of_week[day] = "Sun"
            elif day_of_week[prevday] == "Sun":
                day_of_week[day] = "Mon"



            #print('Day ' + str(day))    # prints the day number

            #print(day_dict)
            #global_warming = float(day_dict['2']['Temperature']) + 1.5
            #print('Global Warming temperature is ' + str(global_warming) + ' degrees C')

            # Writes the daily output CSV file
            write_row()  # This triggers the write_row trigger to write all information to the CSV data file
        else:               # elif
            day = day




#print(year_start_day)
today = date.today()
print("Today's date is ", today, " which is a",days_week[today.weekday()])    # also use today.day, today.month, today.year
# today.weekday 0 is monday, 6 is sunday
#print(day_of_week)


print(f"Day {day_breakdown + 1} of {year} is a {day_of_week[day_breakdown]}")   # +1 day because it starts at 0
print(f"Average daily temperature is {'{0:.2f}'.format(average_temp[day_breakdown])} degrees C")        #prints, which means the last field is duplicated
print(f"Average daily electricity usage is {'{0:.2f}'.format(average_elec[day_breakdown])} kWh")
print(Carbon_Int[day_breakdown])

# This group sorts the carbon intensity in the current day to the number of batteries
Carbon_Int_sort = sorted(Carbon_Int[day_breakdown])
Carbon_Int_sort = Carbon_Int_sort[0:Battery_Num]

Carbon_Int_dict = {""}

print(Carbon_Int_sort)          # sorts carbon intensity, sorted by lowest, for number of batteries

Carbon_Int_sortR = sorted(Carbon_Int[day_breakdown])
Carbon_Int_sortR.sort(reverse=True)
Carbon_Int_sortR = Carbon_Int_sortR[0:Battery_Num]
print(Carbon_Int_sortR)             # for some reason above two need to go after Carbon_Int_sort

#for i in range(0,Battery_Num):
    #Carbon_Int_dict[i] = 1
    #Carbon_Int_dict["Carbon_Int_sortR"] = 0
    #if Carbon_Int_sort[i] == Carbon_Int[i]:
    #    (Carbon_Int[i][i], "hmm")
print(Carbon_Int_sort[0])
print(Carbon_Int_dict)

print(Temperature_list[day_breakdown])      # prints 24 hourly temperature
print("Electricity is " + str(Electricity_list[day_breakdown]))

# print(day_of_week[200])     # returns the day of week for specified day number
#for i in range(1,24):
#    print(str(average_temp))
#for i in range(1,24):



            #print(type(day_dict['4'][0])) # returns class type
            #print((day_dict['4'][0]))  # returns a set position
            #new_value = new_value + float(day_dict['4'][0])
            #print('Daily temperatures ' + str(daily_temp))
            #print(max(daily_temp))
            #print(day_dict.keys())     # prints the keys eg 1-24
            #print(day_dict.values())   # prints the values

            #print(day_dict[day_list[1]])
