
# Total up kWh heating in a day, then divide by 24 ie kWh/hour
# Needs average outside air temperature

import statistics
import csv

path = 'H:\\My Documents\\Ian\Python\\Energy Model\\' # change this to your own file directory
input_CSV = path+'Basic data half hourly.csv'

field_names = ['Hour', # Will add dates to this too as currently no date time
               'Temperature', # This would be from individuals post codes or site details
               'Electricity_Usage',
               'Carbon_Intensity_2018', # generally some of this info is not used in this particular model
               'Carbon_Intensity_2019',
               'Cost',
               'Solar_IR',
               'Gas', # The three below sum to this
               'Heating',
               'Hot Water',
               'Cooking'
]
tsp = 21 # temperature set point internal
dot = -3 # design outside temperature
boiler_ef = 0.85 # Assumed

daily_temp = []
daily_energy = 0
htc_list = []
htc_final = 0.0

time_increment = 0.5 # my current modeling uses half-hourly as it is electrical, this is pretty easy to change
hour_count = time_increment # the above changes this, and the end of the row_writer trigger
row_num = 0
day = 1

def average(x):
    return round(statistics.mean(x), 2)

def HTC_calc():
    #day_energy = (total_energy - heating_energy) / 24 # provides hourly average energy used for heating (gas)
    day_energy = daily_energy / 24
    temp_dif = tsp - statistics.mean(daily_temp) # delta T of internal set point temperature and average daily outside air temperature
    htc = ((day_energy / temp_dif) * boiler_ef) * (tsp - dot) # Watts
    print(f"HTC calculation today is {round(day_energy, 2)} kWh / {round(temp_dif, 2)} oC * {boiler_ef}) * ({tsp} oC - {dot} oC)")
    return round(htc, 2)

# This opens the sample CSV file, layout and headers can be changed depending on what CSV file has in it
with open(input_CSV, 'r') as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names)
    next(csv_dict_reader)  # skips header
    for row in csv_dict_reader:
        #global row_num, hour_count, Battery_SoC, week_num, day_of_week, days_week_num, day, day_breakdown
        row_num = row_num + 1
        # Variable resets
        Hour = float(row['Hour']) # Will add datetime
        hour_count = hour_count + time_increment
        Temperature = float(row['Temperature'])    # changes integer on every cycle
        Electricity_Usage = float(row['Electricity_Usage'])
        Gas = float(row['Gas'])
        gas_heating = float(row['Heating'])
        gas_dhw = float(row['Hot Water'])
        gas_cooking = float(row['Cooking'])

        daily_temp.append(Temperature)
        daily_energy = daily_energy + gas_heating

        if hour_count > 24: # Runs at the end of each day
            #print(daily_temp)
            print(f"Day {day}")
            print(f"Daily energy used for heating is {round(daily_energy, 2)} kWh")
            print(f"Average DOT is {round(statistics.mean(daily_temp), 2)} degrees C")
            print(f"Energy required to heat property from {dot}C to {tsp}C is {HTC_calc()} kW")
            day = day + 1
            if day < 60 or day > 335: # temporary crude way of returning heating days
                htc_list.append(HTC_calc())
            # Clear data
            daily_temp.clear()
            daily_energy = 0
            hour_count = time_increment

print(f"---------------------")
print(f"Calculation completed") # Next calculate mean of htc_list ie all HTC in heating season
print(f"{(sum(htc_list))} kW")
print(f"{len(htc_list)} heating periods")
htc_final = sum(htc_list) / len(htc_list)
print(f"Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW")