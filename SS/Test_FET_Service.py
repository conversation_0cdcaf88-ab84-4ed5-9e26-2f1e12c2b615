import requests
API = 'rt:eyJjaXBoZXJ0ZXh0IjoidzJOd3dNOUgyb1hTMUNIdF8weVlOWDhOdXhabW45QUZhR29oODVWTHVGSHBicGZCcHVCdDd5RVhMSGpEUXZDOUoyVHBhdjk5d2JORW03dnVtVXJ1X0ExNnZzRkg1ckQ4TWVDTWY1eGJHSXFFRHBGQ3ZJaUItMWp0eWszUGwtdDhYNmg4emkzcno3R2VMR2swX3laLW9yTEdUZTlTQ2t6a0FmczhOLU1wWm80OVI3MXZrVGVKYnRQbm1HR215OHBwNDFraFNCR3p2Y2t1TkdTYlI3OU5RRmt5N3plX1plMHdjVlFkSzdIRVpURzJHdDJ1Rzh2ZDNPUFo3M213YVl3UVhFcWR2N2NlM3dQZTdhbE9ya2hkSFFnTy1EMjBqdFkxNFh0TjBZODJaSHgzbHdFRzdDLXNpdjFXVWpaUUktendKMW44d1J3OS15dnBiTDhYZElEdkZQQnoxbjJYLTJSMlg2dXVqUzR4U2YwejR3M1VHS3hFd1h5cmg2NkVzU2lPUXoySmItZlRWaWJpQmczQ2dxR3hMdTJ2d1l4Mnd6dC1TVC1WcFdJMFdlUlZLVU16UnpQU1JjU2NzOEd5N29makR5TkJJT0dzR3I0cGlubnVXemdfNE9rbjA4QmVuN0F2UXNHcGlZZnlOelhqb1JaZEtaVV9KRjJzVjkzTUw5bDBIMlloTzEydUxUOUxKd3g4ajJka01rUUozTHk0Smo1SU10bURla1NOVWl1WHgxVkwwSzNYU0pBdnJucXNfcWNiNkxlOC1TSk1LWmtaZm9pNUgtTV9LQkwtaXk5ejV1UFJwZmVMSW5MelJoRV9USWUyQVZYZC14ZW1rQ3pfTHRNWkZRWWNaRXB5elMtZjF3OVVCWk85c1hVSVRVV1B5a2luSjdrTUZFSXdhZ0lPdzhlc0cxRU5aV2t0UzUzTlhCSV82aDJodFRxamRpVXBSVzFlaXZLZjlwTTV0TjU2TWx6OER4MVFZZXhWVVVWMTA2SnV0b3ZQLWd2dHVyNW94NlZXIiwiZW5jcnlwdGVkX2tleSI6IkVUVjZXbFJzVFBaSGlHb0hScURPZFl6bUZuTmdHaUExVmJuQmFHZDIzdDFhbW5kLTFFZGgwel9CWkZKVzB4SFBQLTBic0lZYnJId2hzWkZZZ1Qyd2hvVlRWNmp0VWNhV3F1bko4aks5alFYNDJqdUVLWFFlMGkwbks5Umk3SUZsM0xJMzFTWXdVT3g1cFl0VlE5N05WaUUxdGg2QVJUbFdRN1hNWGx5OVNCbTVaWExjSGZTUVBGOWxVcGsyRnNjYjVGVkRhU3ZTUHRfQTRLMlRxZG00SjZybGlwRVVSc1NJY1pKdkRaSzIwSlBVUU4tR0VOenYycHRTS3Zxb3hnSHZiUHlLOUNaTl84bEM4Q1FlNUpBS2lHTWxUaTRoRXAtQTN6MU1CRkN2RXNVaXRUYU54cGZKUWxSRjZYWWFWejNCelp1akJPV2RGczNkVU44YWR5N05QUSIsIml2IjoiVjNfa1hTSzRIUUs0cE5GZkpzbzZWdyIsInByb3RlY3RlZCI6ImV5SmhiR2NpT2lKU1UwRXRUMEZGVUMweU5UWWlMQ0psYm1NaU9pSkJNalUyUTBKRExVaFROVEV5SWl3aWEybGtJam9pZFVJMGMxWTFXVXMzTFV0NVZXSnpPRWh0T0RJdFZYVlRjWEJoT0hReFRVbEZWM0pZVFhKdE1USldRU0lzSW5SNWNDSTZJa3BYUlNKOSIsInRhZyI6IkdzbmVBQW1LaHR6YlRsU0pNNTFneFh3UnlRUUF1Ylp5MnR0X2Fqckd0WTQifQ==:*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

data = {
    "agent": "agent1q0mau8vkmg78xx0sh8cyl4tpl4ktx94pqp2e94cylu6haugt2hd7j9vequ7",
    "name": "Python script type",
    "description": "Something about python and snake jokes.",
    "protocolDigest": "proto:05c3504f6d6486792e8410c9d1c6d626991fc8e0007bae960acb2ab1dd83072a",
    "modelDigest": "model:4ec922f7f18419cf7c6742f96eb4543a7ed5c131af077ca2b267607f65e62eb2",
    # model:66841ea279697fd62a029c37b7297e4097966361407a2cc49cd1e7defb924685
    "modelName": "PythonTest/PythonProtocol",
    "fields": [
        {
            "name": "symbol",
            "required": True,
            "field_type": "string",
            "description": "Something about coding."
        }
    ],
    "taskType": "task"
}



requests.post("https://agentverse.ai/v1beta1/services", json=data, headers={
    "Authorization": API # bearer
})