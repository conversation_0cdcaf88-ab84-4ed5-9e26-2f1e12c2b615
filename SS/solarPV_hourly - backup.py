
import csv
import pandas as pd
import requests
from io import StringIO


def dict_to_csv(dictionary, filename):
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        write_dict_to_csv(writer, dictionary)

def write_dict_to_csv(writer, dictionary, parent_key=''):
    for key, value in dictionary.items():
        if isinstance(value, dict):
            new_parent_key = f"{parent_key}{key}_" if parent_key else key
            write_dict_to_csv(writer, value, parent_key=new_parent_key)
        elif isinstance(value, list):
            write_list_to_csv(writer, value, parent_key=key)
        else:
            writer.writerow([f"{parent_key}{key}", value])

def write_list_to_csv(writer, lst, parent_key=''):
    for index, item in enumerate(lst):
        if isinstance(item, dict):
            new_parent_key = f"{parent_key}_{index}_" if parent_key else f"{index}_"
            write_dict_to_csv(writer, item, parent_key=new_parent_key)
        elif isinstance(item, list):
            write_list_to_csv(writer, item, parent_key=f"{parent_key}{index}_")
        else:
            writer.writerow([f"{parent_key}{index}", item])


PARAMS = {
    'outputformat': 'json',
    'browser': 1
}
#tool_name = 'PVcalc'
lat = 51.507
lon = -0.128
raddatabase = 'PVGIS-SARAH2' # default is PVGIS-SARAH2
raddatabasehourly ='PVGIS-SARAH2'
peakpower = 4.0
loss = 14.0
pvtechchoice = 'crystSi'
angle = 35
aspect = 0 # 0=south, 90=west, -90=east
startyear = 2015
endyear = 2015

#solarPV_url = 'https://re.jrc.ec.europa.eu/api/v5_1/tool_name?param1=value1&param2=value2&...'
#solarPV_url = 'https://re.jrc.ec.europa.eu/api/PVcalc?lat=45&lon=8&peakpower=1&loss=14'

data_type = 'hourly' # hourly or monthly

if data_type == 'monthly':
    tool_name = 'v5_2/PVcalc'
    solarPV_url = f'https://re.jrc.ec.europa.eu/api/{tool_name}?lat={lat}&lon={lon}&raddatabase={raddatabase}' \
                  f'&peakpower={peakpower}&loss={loss}&pvtechchoice={pvtechchoice}&angle={angle}&aspect={aspect}'

elif data_type == 'hourly':
    tool_name = 'seriescalc'
    solarPV_url = f'https://re.jrc.ec.europa.eu/api/{tool_name}?lat={lat}&lon={lon}&pvcalculation=1' \
                  f'&peakpower={peakpower}&loss={loss}&startyear={startyear}&endyear={endyear}&raddatabase={raddatabasehourly}' \
                  #f'&raddatabase={raddatabase}' #&pvtechchoice={pvtechchoice}' \
                  #f'&angle={angle}&aspect={aspect}'

''' {"inputs": {"location": {"latitude": 52.875, "longitude": 8.58, "elevation": 39.0},
 "meteo_data": {"radiation_db": "PVGIS-SARAH2", "meteo_db": "ERA5", "year_min": 2020, "year_max": 2020, "use_horizon": true, "horizon_db": null, "horizon_data": "DEM-calculated"},
  "mounting_system": {"fixed": {"slope": {"value": 15, "optimal": false}, "azimuth": {"value": 0, "optimal": false},
   "type": "free-standing"}}, "pv_module": {"technology": "c-Si", "peak_power": 4.0, "system_loss": 14.0}}
'''

print(solarPV_url)

response = requests.get(url=solarPV_url, params=PARAMS)

if response.status_code == 200:
    print("Passed")
if PARAMS['outputformat'] == 'json':
    data = response.json()
    data = pd.DataFrame.from_dict[response.json()]
    # Load CSV data into a pandas DataFrame
    df = pd.read_csv(StringIO(response.content.decode('utf-8')))
else:
    data = response.json()
    if isinstance(response, list) and len(response) > 0:
        keys = response[0].keys()
        with open('solarPV_data.csv', 'w', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=keys)
            writer.writeheader()
            writer.writerows(response)
        print(f"Response saved as solarPV_data.csv")
    else:
        print("Invalid response format. Expected a list of dictionaries.")

# Load CSV data into a pandas DataFrame
#df = pd.read_csv(StringIO(response.content.decode('utf-8')))
#df = pd.read_json(data)
#df.to_csv (r'solarPV.csv', index = None)

#print(data)

# with open(data, encoding='utf-8') as inputfile:
#     df = pd.read_json(inputfile)
#print(df)
# df.to_csv('csvfile.csv', encoding='utf-8', index=False)

print(type(data))
#print(data['inputs']['location']['latitude'])
if data_type == 'monthly':
    print(f'Latitude is {data["inputs"]["location"]["latitude"]} degrees')
    for p_info in data['outputs']['monthly']['fixed']:
        print(p_info['E_m'])
    print(f'Total annual generation is {data["outputs"]["totals"]["fixed"]["E_y"]} kWh')
elif data_type == 'hourly':
    print(data)

'''
field_names = []

with open('solarPV_data.csv') as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names)
    next(csv_dict_reader)  # skips header
    for row in csv_dict_reader:
        csv_file.writerow(row)

'''

#print(data['outputs']['monthly']['fixed'][0]['E_m'])

dict_to_csv(data, 'solarPV_data.csv')


