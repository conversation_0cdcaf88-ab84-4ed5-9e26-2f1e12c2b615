from uagents import Agent, Context, Model
from uagents.setup import fund_agent_if_low

rahn = Agent(name="rahn",
             port=8000,
             seed="chick run dear remix",
             endpoint={
                 "http://127.0.0.1:8000/submit": {"weight": 2},
                 "http://127.0.0.1:8001/submit": {}, # weight value = 1
             },
)

class EnergyStatus(Model):
    energy_stored: float
    energy_used: float
    energy_generated: float

print("uAgent address: ", rahn.address)
print("Fetch network address: ", rahn.wallet.address())

fund_agent_if_low(rahn.wallet.address())

# build powergrid
#powergrid.include()

Energy = {
    1: EnergyStatus(energy_stored=7.5, energy_used=2.5, energy_generated=1.5),
    2: EnergyStatus(energy_stored=12.0, energy_used=4.0, energy_generated=5.5)
}



# set energy information
# for (number, status) in Energy.items():
#     rahn.storage.set(number, status.dict())

# rahn._storage.set("1", 7.5)
#
#rahn._storage.get(1)

@rahn.on_interval(period=5.0)
async def say_hello(ctx: Context):
    ctx.logger.info(f'hello, my name is {ctx.name}')

if __name__ == "__main__":
    rahn.run()