import math

def refrigerant(cycle_name, <PERSON>, Tevap, Tevap_cool):
    # cool 7 kg.s-1 liquid from 21oC to 5oC, in ambient temp of 50oC
    cp_water = 4.18 # specific heat capacity of water
    # read from graph
    h1 = 410.279 #405 # kJ.kg-1
    h2 = 426.818 #455 # kJ.kg-1
    h3 = 271.623 #285 #kJ.kg-1
    h4 = h3

    effI = 0.65  # assume isentropic efficiency of compressor (%)
    # effI = (h2 - h1) / (h2l - h1)
    h2s = ((h2 - h1) / effI) + h1

    # 5k super heat, 5k super cool
    mass_flow_rate_fluid = 34 # kg.s-1 of fluid
    #Tao = 50
    Tcond = Tao + 5 # condensor 5k higher
    #Tevap = 21
    #Tevap_cool = 5
    Tevap_delta = Tevap - Tevap_cool

    Qe = mass_flow_rate_fluid * cp_water * Tevap_delta # kW

    mass_flow_rate_re = (Qe / (h1 - h4))

    Wc = mass_flow_rate_re * (h2s - h1) # kW

    Qc = mass_flow_rate_re * (h2 - h3)

    COP_cool = round(Qe / Wc,2)

    COP_heat = round(Qc / Wc,2)

    COP_hc = (Qe + Qc) / Wc

    COP = (h2 - h3) / (h2 - h1)

    # Compressor
    LMTDc = ((Tevap - Tevap_cool) / math.log(Tevap/Tevap_cool))
    LMTDe = (Tevap - Tevap_cool) / math.log(Tevap/Tevap_cool)
    UAc = round(Qc / LMTDc,2)
    UAe = round(Qe / LMTDe,2)

    comp_dis = round(mass_flow_rate_re * 0.072 * 3600,1) # compressor displacent
    # refrigerant flow rate * volumetric flow rate * 3600

    print(f'--- {cycle_name} ---')
    print(f'COP Heat: {COP_heat}\nCOP Cool: {COP_cool}\nCOP: {COP}')
    print(f'UAc: {UAc} kW/K\nUAe: {UAe} kW/K\nCompressor Displacement: {comp_dis} m3/hr')

    return COP_heat, COP_cool, COP, UAc, UAe, comp_dis


refrigerant("Example 1",16, 16, 5)




