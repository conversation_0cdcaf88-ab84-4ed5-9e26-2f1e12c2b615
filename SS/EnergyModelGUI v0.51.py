import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk

bg_colour = "#3d6466"

def validate_input(content, input_type):
    if input_type == "numbers":
        if content.isdigit():
            value = int(content)
            if 1 <= value <= 999:
                return True
            else:
                messagebox.showerror("Invalid input", "Value must be between 4 and 10.")
                return False
        elif content == "":
            return True  # Allow empty input to clear the box
        else:
            messagebox.showerror("Invalid input", "Please enter a number.")
            return False
    elif input_type == "letters":
        if content.isalpha() or content == "":
            return True
        else:
            messagebox.showerror("Invalid input", "Please enter letters only.")
            return False
    elif input_type == "orientation":
        if content in ["N", "E", "S", "W", ""]:
            return True
        else:
            messagebox.showerror("Invalid input", "Please enter a valid compass direction (N, E, S, W).")
            return False
    else:
        return False

def get_entry_value(entry, value_variable, valmin, valmax):
    value = entry.get()
    if value.isdigit() and valmin <= int(value) <= valmax:
        value_variable.set(int(value))
        #messagebox.showinfo("Success", f"Value entered: {user_value.get()}")
        print(f'User has chosen {value_variable.get()}')
    else:
        messagebox.showerror("Invalid input", f'Please enter a valid number between {valmin} and {valmax}')

def get_entry_values(entries, variables, input_types):
    try:
        for entry, value_variable, input_type in zip(entries, variables, input_types):
            value = entry.get()
            if validate_input(value, input_type):
                if input_type == "numbers":
                    value_variable.set(int(value))
                elif input_type == "orientation": # This converts the user's string direction into a number
                    compass_to_angle = {"N": 180, "E": -90, "S": 0, "W": 90}
                    value_variable.set(compass_to_angle.get(value, 0))
                else:
                    value_variable.set(value)
            else:
                return
        messagebox.showinfo("Success", "All values entered successfully!")
    except Exception as e:
        messagebox.showerror("Error", str(e))

def use_user_value(value_variable):
    value = value_variable.get()
    messagebox.showinfo("User Value", f"The stored user value is: {value}")

def clear_widgets(frame):
    for widget in frame.winfo_children():
        widget.destroy()

def run_energy_model():
    floor_area = building_values[0].get()
    building_height = building_values[1].get()
    solarPV_size = solarPV_values[0].get() # is still wiping the value
    orientation = solarPV_values[1].get()
    bat_num = battery_values[0].get()
    bat_capacity = battery_values[1].get()
    print(f'User has chosen a {floor_area} floor area')
    print(f'User has chosen a {building_height} floor area')
    print(f'User has chosen a {solarPV_size} kWp solar PV system')
    print(f'Solar PV orientation is {orientation} degrees from South')
    print(f'User has chosen {bat_num} batteries')
    print(f'Each with a {bat_capacity} kWh capacity')

def load_main_frame():
    clear_widgets(frame4)  # destroy other frames

    frame1.tkraise()  # place at each frame to stack
    frame1.pack_propagate(False)

    # Frame 1 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/J&H Logo (circular).png", logo_width, logo_height)
    logo_widget = tk.Label(frame1, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.pack()

    tk.Label(frame1,
             text="Energy System",
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).pack()

    # button widget
    tk.Button(
        frame1,
        text="Browse Energy Systems",
        font=("TKHeadingFont", 20),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=load_frame2
    ).pack(pady=20)  # pady adds gaps between bits

    tk.Button(
        frame1,
        text="Run Energy Model",
        font=("TKHeadingFont", 14),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=run_energy_model
    ).pack(pady=14)  # pady adds gaps between bits

def load_frame2():
    clear_widgets(frame1)  # destroy other frames
    frame2.tkraise()  # place at each frame to stack

    container = tk.Frame(frame2, bg=bg_colour)
    container.grid(row=0, column=0, padx=20, pady=20)

    # Frame 2 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/building.jpg", logo_width, logo_height)
    logo_widget = tk.Label(container, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.grid(row=0, column=0, columnspan=2, pady=20)

    tk.Label(container,
             text="Building Details",  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKHeadingFont", 20)
             ).grid(row=1, column=0, columnspan=2, pady=25)

    # Clear previous entries and user values
    entries.clear()
    building_values.clear()

    # Entry widgets with validation for multiple values
    inputs = [
        {"label": "Floor Area", "type": "numbers"},
        {"label": "Height", "type": "numbers"},
    ]
    input_types = []
    for i, input_info in enumerate(inputs):  # Change the range for more entry widgets
        vcmd = (root.register(validate_input), '%P', input_info["type"])
        entry = tk.Entry(container, validate='key', validatecommand=vcmd)
        entry.grid(row=i + 3, column=0, padx=10, pady=10)
        # entry.pack(pady=10)  # Add some padding

        entries.append(entry)
        building_values.append(tk.IntVar())
        input_types.append(input_info["type"])

        # Create and place the corresponding label to the right of the entry widget
        label = tk.Label(container, text=f'{input_info["label"]}', bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        # label = tk.Label(container, text=f"Value {i + 1}", bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        label.grid(row=i + 3, column=1, padx=10, pady=10)

    # Single button to get all entry values
    submit_button = tk.Button(container, text="Submit All Values",
                              command=lambda: get_entry_values(entries, building_values, input_types))
    submit_button.grid(row=len(entries) + 3, column=0, columnspan=2, pady=20)  # pack(pady=20)

    # Button to go back to the first frame
    back_button = tk.Button(container, text="NEXT", font=("TKHeadingFont", 18), bg="#28393a", fg="white",
                            cursor="hand2",
                            activebackground="#badee2", activeforeground="black", command=load_frame3)
    back_button.grid(row=len(entries) + 4, column=0, columnspan=2,
                     pady=20)  # pack(pady=20)  # pady adds gaps between bits

    frame2.grid_rowconfigure(0, weight=1)
    frame2.grid_columnconfigure(0, weight=1)
    container.grid_rowconfigure(0, weight=1)
    container.grid_columnconfigure(0, weight=1)

def load_frame3():
    clear_widgets(frame2)  # destroy other frames
    frame3.tkraise()  # place at each frame to stack

    container = tk.Frame(frame3, bg=bg_colour)
    container.grid(row=0, column=0, padx=20, pady=20)

    # Frame 2 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/solar_panels.jpg", logo_width, logo_height)
    logo_widget = tk.Label(container, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.grid(row=0, column=0, columnspan=2, pady=20)
    #logo_widget.pack(pady=20)

    tk.Label(container,
             text="Solar Panels",  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKHeadingFont", 20)
             ).grid(row=1, column=0, columnspan=2, pady=25)#pack(pady=25)
    tk.Label(container,
             text=f'System Details',  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).grid(row=2, column=0, columnspan=2, pady=20)#pack(pady=15)

    # Clear previous entries and user values
    entries.clear()
    solarPV_values.clear()

    # Entry widgets with validation for multiple values
    inputs = [
        {"label": "Array Size (kW)", "type": "numbers"},
        {"label": "Orientation from South", "type": "orientation"},
    ]
    input_types = []
    for i, input_info in enumerate(inputs):  # Change the range for more entry widgets
        vcmd = (root.register(validate_input), '%P', input_info["type"])
        entry = tk.Entry(container, validate='key', validatecommand=vcmd)
        entry.grid(row=i + 3, column=0, padx=10, pady=10)
        #entry.pack(pady=10)  # Add some padding
        entries.append(entry)
        solarPV_values.append(tk.IntVar())
        input_types.append(input_info["type"])

        # Create and place the corresponding label to the right of the entry widget
        label = tk.Label(container, text=f'{input_info["label"]}', bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        #label = tk.Label(container, text=f"Value {i + 1}", bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        label.grid(row=i + 3, column=1, padx=10, pady=10)

    # Single button to get all entry values
    submit_button = tk.Button(container, text="Submit All Values", command=lambda: get_entry_values(entries, solarPV_values, input_types))
    submit_button.grid(row=len(entries)+3, column=0, columnspan=2, pady=20)#pack(pady=20)

    # Button to go back to the first frame
    back_button = tk.Button(container, text="NEXT", font=("TKHeadingFont", 18), bg="#28393a", fg="white", cursor="hand2",
                            activebackground="#badee2", activeforeground="black", command=load_frame4)
    back_button.grid(row=len(entries)+4, column=0, columnspan=2, pady=20)#pack(pady=20)  # pady adds gaps between bits

    frame3.grid_rowconfigure(0, weight=1)
    frame3.grid_columnconfigure(0, weight=1)
    container.grid_rowconfigure(0, weight=1)
    container.grid_columnconfigure(0, weight=1)

def load_frame4():
    clear_widgets(frame3)  # destroy other frames
    frame4.tkraise()  # place at each frame to stack

    container = tk.Frame(frame4, bg=bg_colour)
    container.grid(row=0, column=0, padx=20, pady=20)

    # Frame 2 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/battery_powerwall.jpg", logo_width, logo_height)
    logo_widget = tk.Label(container, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.grid(row=0, column=0, columnspan=2, pady=20)
    #logo_widget.pack(pady=20)

    tk.Label(container,
             text="Battery Storage",  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKHeadingFont", 20)
             ).grid(row=1, column=0, columnspan=2, pady=25)#pack(pady=25)
    tk.Label(container,
             text=f'Battery details',  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).grid(row=2, column=0, columnspan=2, pady=20)#pack(pady=15)


    # Clear previous entries and user values
    entries.clear()
    battery_values.clear()

    inputs = [
        {"label": "Number", "type": "numbers"},
        {"label": "Capacity (kWh)", "type": "numbers"},
    ]
    # Entry widgets with validation for multiple values
    input_types = []
    for i, input_info in enumerate(inputs):  # Change the range for more entry widgets
        vcmd = (root.register(validate_input), '%P', input_info["type"])
        entry = tk.Entry(container, validate='key', validatecommand=vcmd)
        entry.grid(row=i + 3, column=0, padx=10, pady=10)
        #entry.pack(pady=10)  # Add some padding
        entries.append(entry)
        battery_values.append(tk.IntVar())
        input_types.append(input_info["type"])

        # Create and place the corresponding label to the right of the entry widget
        label = tk.Label(container, text=f'{input_info["label"]}', bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        #label = tk.Label(container, text=f"Value {i + 1}", bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        label.grid(row=i + 3, column=1, padx=10, pady=10)

    # Single button to get all entry values
    submit_button = tk.Button(container, text="Submit All Values", command=lambda: get_entry_values(entries, battery_values, input_types))
    submit_button.grid(row=len(entries)+3, column=0, columnspan=2, pady=20)#pack(pady=20)

    # Button to go back to the first frame
    back_button = tk.Button(container, text="NEXT", font=("TKHeadingFont", 18), bg="#28393a", fg="white", cursor="hand2",
                            activebackground="#badee2", activeforeground="black", command=load_main_frame)
    back_button.grid(row=len(entries)+4, column=0, columnspan=2, pady=20)#pack(pady=20)  # pady adds gaps between bits

    frame4.grid_rowconfigure(0, weight=1)
    frame4.grid_columnconfigure(0, weight=1)
    container.grid_rowconfigure(0, weight=1)
    container.grid_columnconfigure(0, weight=1)

def resize_image(image_path, width, height):
    image = Image.open(image_path)
    resized_image = image.resize((width, height), Image.LANCZOS)
    return ImageTk.PhotoImage(resized_image)

# Initialize app
root = tk.Tk()
root.title("Energy Model")  # Title

root.eval("tk::PlaceWindow . center")  # Centers on screen

frame1 = tk.Frame(root, width=500, height=600, bg=bg_colour)  # Window size and background teal
frame2 = tk.Frame(root, bg=bg_colour)  # size here isn't limited by removing size
frame3 = tk.Frame(root, bg=bg_colour)  # size here isn't limited by removing size
frame4 = tk.Frame(root, bg=bg_colour)  # size here isn't limited by removing size

for frame in (frame1, frame2, frame3, frame4):
    frame.grid(row=0, column=0, sticky="nesw")  # Default values, define anyway. sticky stops white corners

# container = tk.Frame(frame3, bg=bg_colour)
# container.grid(row=0, column=0, padx=20, pady=20)

# Variable to store entry value
floor_area = tk.IntVar()
building_height = tk.IntVar()
solarPV_size = tk.IntVar()
solarPV_orientation = tk.IntVar()
bat_num = tk.IntVar()
bat_capacity = tk.IntVar()

building_values = []
solarPV_values = []
battery_values = []
entries = []

load_main_frame()

# Run app
root.mainloop()
