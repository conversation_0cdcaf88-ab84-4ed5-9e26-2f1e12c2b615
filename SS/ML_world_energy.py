import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor
import matplotlib.pyplot as plt
import numpy as np

''' Load Data '''
df = pd.read_csv('H:\\My Documents\\Ian\\Python\\Machine Learning\\Datasets\\World Energy Consumption\\World Energy Consumption.csv')
df = df.dropna()
''' Data Preparation '''
y = df['population']
result = 'Population'
#X = df.drop('logS', axis=1)
#X = df[['country','year','gdp']]
#
df_encoded = pd.get_dummies(df, columns=['country'], drop_first=True)

# Define X and y
X = df_encoded[['year', 'gdp'] + [col for col in df_encoded.columns if col.startswith('country_')]]
# Combine X and y into a single DataFrame to handle missing values
#df_combined = pd.concat([X, y], axis=1)

# Drop rows with missing values in the combined DataFrame
#df_combined = df_combined.dropna()

# Separate X and y after handling missing values
#X = df_combined.drop(columns=['country'])
#y = df_combined['population']

# typical training set is 80% of data, test 20% remaining on the model you've just trained (ie unknown data)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=100)

''' Model Building '''
# Linear Regression
lr = LinearRegression()
lr.fit(X_train, y_train)

#LinearRegression()

y_lr_train_pred = lr.predict(X_train)
y_lr_test_pred = lr.predict(X_test)

#print(y_lr_train_pred, y_lr_test_pred)

''' Evaluate Model Performance '''
lr_train_mse = mean_squared_error(y_train, y_lr_train_pred)
lr_train_r2 = r2_score(y_train, y_lr_train_pred)

lr_test_mse = mean_squared_error(y_test, y_lr_test_pred)
lr_test_r2 = r2_score(y_test, y_lr_test_pred)

''' LR MSE (Train):'''
# print(f'LR MSE (Train): {lr_train_mse}')
# print(f'LR R2 (Train): {lr_train_r2}')
# print(f'LR MSE (Test): {lr_test_mse}')
# print(f'LR R2 (Test): {lr_test_r2}')

lr_results = pd.DataFrame(['Linear regression', lr_train_mse, lr_train_r2, lr_test_mse, lr_test_r2]).transpose()
lr_results.columns = ['Method', 'Training MSE', 'Training R2', 'Test MSE', 'Test R2']
print(lr_results)

''' Random Forest '''
# Regression for quantitive

rf = RandomForestRegressor(n_estimators=100, max_depth=2, random_state=100)
rf.fit(X_train, y_train)

''' Apply to make a prediction '''
y_rf_train_pred = rf.predict(X_train)
y_rf_test_pred = rf.predict(X_test)

''' Evaluate Model Performance '''
rf_train_mse = mean_squared_error(y_train, y_rf_train_pred)
rf_train_r2 = r2_score(y_train, y_rf_train_pred)

rf_test_mse = mean_squared_error(y_test, y_rf_test_pred)
rf_test_r2 = r2_score(y_test, y_rf_test_pred)

rf_results = pd.DataFrame(['Random Forest', rf_train_mse, rf_train_r2, rf_test_mse, rf_test_r2]).transpose()
rf_results.columns = ['Method', 'Training MSE', 'Training R2', 'Test MSE', 'Test R2']
print(rf_results)

''' Model Comparison '''
df_models = pd.concat([lr_results, rf_results], axis=0)
df_models.reset_index(drop=True)
print(df_models)

''' Data Visulisation '''
plt.figure(figsize=(5,5))
plt.scatter(x=y_train, y=y_lr_train_pred, c="#7CAE00", alpha=0.3)

# Trend line
z = np.polyfit(y_train, y_lr_train_pred, 1)
p = np.poly1d(z)

plt.plot(y_train, p(y_train), '#F8766D')
plt.ylabel(f'Predict {result}')
plt.xlabel(f'Experimental {result}')

plt.show()

# Feature Importances
feature_importances = rf.feature_importances_
features = X.columns

# Plot Feature Importances
plt.figure(figsize=(10, 6))
plt.barh(features, feature_importances, align='center')
plt.xlabel('Feature Importance')
plt.ylabel('Feature')
plt.title('Feature Importances from Random Forest')
plt.show()

# Plot Predicted vs Actual Values
plt.figure(figsize=(10, 6))
plt.scatter(y_train, y_rf_train_pred, alpha=0.5, label='Train')
plt.scatter(y_test, y_rf_test_pred, alpha=0.5, label='Test')
plt.plot([y.min(), y.max()], [y.min(), y.max()], 'k--', lw=2)
plt.xlabel('Actual Values')
plt.ylabel('Predicted Values')
plt.title('Predicted vs. Actual Values')
plt.legend()
plt.show()

# Plot Residuals Distribution
residuals_train = y_train - y_rf_train_pred
residuals_test = y_test - y_rf_test_pred

plt.figure(figsize=(10, 6))
plt.hist(residuals_train, bins=30, alpha=0.5, label='Train')
plt.hist(residuals_test, bins=30, alpha=0.5, label='Test')
plt.xlabel('Residuals')
plt.ylabel('Frequency')
plt.title('Residuals Distribution')
plt.legend()
plt.show()