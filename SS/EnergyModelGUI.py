import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk

bg_colour = "#3d6466"

def validate_input(content):
    if content.isdigit():
        value = int(content)
        if 1 <= value <= 9999:
            return True
        else:
            messagebox.showerror("Invalid input", "Value must be valid.")
            return False
    elif content == "":
        return True  # Allow empty input to clear the box
    else:
        messagebox.showerror("Invalid input", "Please enter a number.")
        return False

def get_entry_value(entry, value_variable, valmin, valmax):
    value = entry.get()
    if value.isdigit() and valmin <= int(value) <= valmax:
        value_variable.set(int(value))
        #messagebox.showinfo("Success", f"Value entered: {user_value.get()}")
        print(f'User has chosen {value_variable.get()}')
    else:
        messagebox.showerror("Invalid input", f'Please enter a valid number between {valmin} and {valmax}')

def get_entry_values(entries, variables, valmin, valmax):
    try:
        for entry, value_variable in zip(entries, variables):
            value = entry.get()
            if value.isdigit() and valmin <= int(value) <= valmax:
                value_variable.set(int(value))
            else:
                messagebox.showerror("Invalid input", f'Please enter a valid number between {valmin} and {valmax}')
                return
        print(f'Success, All values entered successfully')
        #messagebox.showinfo("Success", "All values entered successfully!")
    except Exception as e:
        messagebox.showerror("Error", str(e))

def use_user_value(value_variable):
    value = value_variable.get()
    messagebox.showinfo("User Value", f"The stored user value is: {value}")

def clear_widgets(frame):
    for widget in frame.winfo_children():
        widget.destroy()

def run_energy_model():
    print(f'User has chosen a {solarPV_size.get()} kWp solar PV system')
    #print(f'User has chosen a {bat_capacity.get()} kWh battery storage')
    print(f'User has chosen {battery_values[0].get()} batteries')
    print(f'Each with a {battery_values[1].get()} kWh capacity')

def load_main_frame():
    clear_widgets(frame3)  # destroy other frames
    frame1.tkraise()  # place at each frame to stack
    frame1.pack_propagate(False)

    # Frame 1 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/J&H Logo (circular).png", logo_width, logo_height)
    logo_widget = tk.Label(frame1, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.pack()

    tk.Label(frame1,
             text="Energy System",
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).pack()

    # button widget
    tk.Button(
        frame1,
        text="Browse Energy Systems",
        font=("TKHeadingFont", 20),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=load_frame2
    ).pack(pady=20)  # pady adds gaps between bits

    tk.Button(
        frame1,
        text="Run Energy Model",
        font=("TKHeadingFont", 14),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=run_energy_model
    ).pack(pady=14)  # pady adds gaps between bits

def load_frame2():
    clear_widgets(frame1)  # destroy other frames
    frame2.tkraise()  # place at each frame to stack

    # Frame 2 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/solar_panels.jpg", logo_width, logo_height)
    logo_widget = tk.Label(frame2, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.pack(pady=20)

    tk.Label(frame2,
             text="Solar Panels",  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKHeadingFont", 20)
             ).pack(pady=25)
    tk.Label(frame2,
             text=f'Peak Output (kW) between {4} and {10}',  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).pack(pady=15)

    # Entry widget with validation
    vcmd = (root.register(validate_input), '%P')  # '%P' passes the new value of the entry widget to the validation function
    entry = tk.Entry(frame2, validate='key', validatecommand=vcmd)
    entry.pack(pady=15)  # Add some padding

    # Button to get entry value
    submit_button = tk.Button(frame2, text="Submit", command=lambda: get_entry_value(entry, solarPV_size, 4, 10))
    submit_button.pack(pady=10)

    # Button to go back to the first frame
    back_button = tk.Button(frame2, text="NEXT", font=("TKHeadingFont", 18), bg="#28393a", fg="white", cursor="hand2",
                            activebackground="#badee2", activeforeground="black", command=load_frame3)
    back_button.pack(pady=20)  # pady adds gaps between bits

def load_frame3():
    clear_widgets(frame2)  # destroy other frames
    frame3.tkraise()  # place at each frame to stack

    container = tk.Frame(frame3, bg=bg_colour)
    container.grid(row=0, column=0, padx=20, pady=20)

    # Frame 2 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/battery_powerwall.jpg", logo_width, logo_height)
    logo_widget = tk.Label(container, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.grid(row=0, column=0, columnspan=2, pady=20)
    #logo_widget.pack(pady=20)

    tk.Label(container,
             text="Battery Storage",  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKHeadingFont", 20)
             ).grid(row=1, column=0, columnspan=2, pady=25)#pack(pady=25)
    tk.Label(container,
             text=f'Battery details',  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).grid(row=2, column=0, columnspan=2, pady=20)#pack(pady=15)

    # Entry widgets with validation for multiple values
    for i in range(2):  # Change the range for more entry widgets
        vcmd = (root.register(validate_input), '%P')
        entry = tk.Entry(container, validate='key', validatecommand=vcmd)
        entry.grid(row=i + 3, column=0, padx=10, pady=10)
        #entry.pack(pady=10)  # Add some padding
        entries.append(entry)
        battery_values.append(tk.IntVar())

        # Create and place the corresponding label to the right of the entry widget
        label = tk.Label(container, text=f"Value {i + 1}", bg=bg_colour, fg="white", font=("TKHeadingFont", 14))
        label.grid(row=i + 3, column=1, padx=10, pady=10)

    # Single button to get all entry values
    submit_button = tk.Button(container, text="Submit All Values", command=lambda: get_entry_values(entries, battery_values, 1, 99))
    submit_button.grid(row=len(entries)+3, column=0, columnspan=2, pady=20)#pack(pady=20)

    # Button to go back to the first frame
    back_button = tk.Button(container, text="NEXT", font=("TKHeadingFont", 18), bg="#28393a", fg="white", cursor="hand2",
                            activebackground="#badee2", activeforeground="black", command=load_main_frame)
    back_button.grid(row=len(entries)+4, column=0, columnspan=2, pady=20)#pack(pady=20)  # pady adds gaps between bits

    frame3.grid_rowconfigure(0, weight=1)
    frame3.grid_columnconfigure(0, weight=1)
    container.grid_rowconfigure(0, weight=1)
    container.grid_columnconfigure(0, weight=1)

def resize_image(image_path, width, height):
    image = Image.open(image_path)
    resized_image = image.resize((width, height), Image.LANCZOS)
    return ImageTk.PhotoImage(resized_image)

# Initialize app
root = tk.Tk()
root.title("Energy Model")  # Title

root.eval("tk::PlaceWindow . center")  # Centers on screen

frame1 = tk.Frame(root, width=500, height=600, bg=bg_colour)  # Window size and background teal
frame2 = tk.Frame(root, bg=bg_colour)  # size here isn't limited by removing size
frame3 = tk.Frame(root, bg=bg_colour)  # size here isn't limited by removing size

for frame in (frame1, frame2, frame3):
    frame.grid(row=0, column=0, sticky="nesw")  # Default values, define anyway. sticky stops white corners

# Variable to store entry value
solarPV_size = tk.IntVar()
bat_num = tk.IntVar()
bat_capacity = tk.IntVar()

battery_values = []
entries = []

load_main_frame()

# Run app
root.mainloop()
