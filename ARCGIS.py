#!/usr/bin/env python3
'''
takeley_pyqgis_overlay.py
Standalone PyQGIS script to:
  - Geocode a large area (e.g. Essex) via Nominatim
  - Tile the bounding box into smaller chunks to respect API limits
  - Fetch OSM data (roads, green spaces, buildings) via Overpass API for each tile
  - Stitch all tiles together into one GeoJSON
  - Extract categorized layers and save as shapefiles
  - Load layers into a QGIS project
  - Create a print layout with legend & scale bar, export as PDF
  - Provide CLI parameters for flexibility

Before running:
  • Use the OSGeo4W Shell (Windows) or configure your environment so that Python can import 'qgis'.
  • If running outside OSGeo4W, set the QGIS_PREFIX_PATH env var to your QGIS install path.
'''

import os
import sys

# --- QGIS Environment Setup ---
# If you see 'ModuleNotFoundError: No module named "qgis"', ensure you're running
# within the QGIS/OSGeo4W Python environment. Alternatively, uncomment and adjust:
os.environ['QGIS_PREFIX_PATH'] = r"C:\OSGeo4W\apps\qgis"
qgis_prefix = os.environ['QGIS_PREFIX_PATH']
#sys.path.append(os.path.join(qgis_prefix, "python"))
#sys.path.append(os.path.join(qgis_prefix, "python", "plugins"))

import json
import time
import math
import logging
import argparse
import requests
from geopy.geocoders import Nominatim
from geopy.extra.rate_limiter import RateLimiter
from qgis.core import (
    QgsApplication, QgsVectorLayer, QgsProject,
    QgsFeature, QgsFields, QgsField, QgsGeometry,
    QgsPointXY, QgsRectangle,
    QgsLayoutItemMap, QgsLayoutItemLegend,
    QgsLayoutItemScaleBar, QgsPrintLayout,
    QgsLayoutExporter
)
from qgis.analysis import QgsNativeAlgorithms
import processing
from PyQt5.QtCore import QVariant

# simple float range generator
def frange(start, stop, step):
    val = start
    while val < stop:
        yield val
        val += step


def main():
    parser = argparse.ArgumentParser(
        description='PyQGIS Overlay covering a large area via tiling.')
    parser.add_argument('--area', default='Essex, UK',
                        help='Area to cover (e.g. county or region)')
    parser.add_argument('--tile-size', type=float, default=5000,
                        help='Tile size in meters to avoid API limits')
    parser.add_argument('--out', default=os.path.join(os.getcwd(), 'Essex_QGIS'),
                        help='Output folder')
    args = parser.parse_args()

    logging.basicConfig(level=logging.INFO,
                        format='%(levelname)s: %(message)s')

    # Initialize QGIS
    qgs = QgsApplication([], False)
    qgs.initQgis()
    QgsApplication.processingRegistry().addProvider(QgsNativeAlgorithms())

    os.makedirs(args.out, exist_ok=True)

    # Geocode area to bounding box
    geolocator = Nominatim(user_agent='pyqgis_overlay')
    geocode = RateLimiter(geolocator.geocode, min_delay_seconds=1)
    location = geocode(args.area)
    if not location:
        logging.error(f'Failed to geocode {args.area}')
        sys.exit(1)
    logging.info(f'Geocoded "{args.area}" to ({location.latitude}, {location.longitude})')

    bb = list(map(float, location.raw['boundingbox']))
    lat_min, lat_max, lon_min, lon_max = bb[0], bb[1], bb[2], bb[3]

    # Create a boundary layer for the area
    uri = 'Polygon?crs=EPSG:4326'
    area_layer = QgsVectorLayer(uri, 'area_boundary', 'memory')
    prov = area_layer.dataProvider()
    prov.addAttributes([QgsField('id', QVariant.Int)])
    area_layer.updateFields()
    coords = [
        QgsPointXY(lon_min, lat_min),
        QgsPointXY(lon_min, lat_max),
        QgsPointXY(lon_max, lat_max),
        QgsPointXY(lon_max, lat_min),
        QgsPointXY(lon_min, lat_min)
    ]
    feat = QgsFeature(area_layer.fields())
    feat.setGeometry(QgsGeometry.fromPolygonXY([coords]))
    feat['id'] = 1
    prov.addFeature(feat)
    area_layer.updateExtents()
    QgsProject.instance().addMapLayer(area_layer)

    # Calculate degree steps for tiling
    mean_lat = math.radians((lat_min + lat_max) / 2)
    lat_deg = args.tile_size / 111000
    lon_deg = args.tile_size / (111000 * math.cos(mean_lat))

    # Fetch and stitch OSM tiles
    features = []
    overpass_url = 'https://overpass-api.de/api/interpreter'
    for lat in frange(lat_min, lat_max, lat_deg):
        for lon in frange(lon_min, lon_max, lon_deg):
            lat2 = min(lat + lat_deg, lat_max)
            lon2 = min(lon + lon_deg, lon_max)
            tile_bbox = f'{lat},{lon},{lat2},{lon2}'
            query = f'''[out:json][timeout:25];
(
  way["highway"]({tile_bbox});
  way["landuse"="grass"]({tile_bbox});
  way["leisure"="park"]({tile_bbox});
  way["building"]({tile_bbox});
);
out body;
>;
out skel qt;'''
            logging.info(f'Fetching tile {tile_bbox}')
            resp = requests.get(overpass_url, params={'data': query})
            if resp.status_code == 200:
                data = resp.json()
                features.extend(data.get('features', []))
            else:
                logging.warning(f'HTTP {resp.status_code} on {tile_bbox}')
            time.sleep(1)

    combined = {'type': 'FeatureCollection', 'features': features}
    geojson_file = os.path.join(args.out, 'combined_osm.geojson')
    with open(geojson_file, 'w') as f:
        json.dump(combined, f)
    logging.info(f'Saved combined OSM data to {geojson_file}')

    # Load combined layer
    combined_layer = QgsVectorLayer(geojson_file, 'combined_osm', 'ogr')
    QgsProject.instance().addMapLayer(combined_layer)

    # Define and extract categories
    feature_queries = {
        'roads': '"highway" IS NOT NULL',
        'green_spaces': "\"landuse\" = 'grass' OR \"leisure\" = 'park'"
    }
    building_queries = {
        'buildings_residential': "\"building\" = 'residential'",
        'buildings_commercial':  "\"building\" = 'commercial'",
        'buildings_industrial':  "\"building\" = 'industrial'",
        'buildings_retail':      "\"building\" = 'retail'",
        'buildings_public':      "\"building\" IN ('school','hospital','university','townhall')'",
        'buildings_religious':   "\"building\" IN ('church','chapel','mosque','temple')'",
        'buildings_other':       "\"building\" = 'yes'"
    }
    feature_queries.update(building_queries)

    for name, expr in feature_queries.items():
        out_shp = os.path.join(args.out, f'{name}.shp')
        processing.run('native:extractbyexpression', {
            'INPUT': combined_layer,
            'EXPRESSION': expr,
            'OUTPUT': out_shp
        })
        layer = QgsVectorLayer(out_shp, name, 'ogr')
        QgsProject.instance().addMapLayer(layer)
        logging.info(f'Extracted {name} to {out_shp}')

    # Create print layout
    project = QgsProject.instance()
    layout = QgsPrintLayout(project)
    layout.initializeDefaults()
    layout.setName('Essex_Layout')
    project.layoutManager().addLayout(layout)

    map_item = QgsLayoutItemMap(layout)
    map_item.setRect(20, 20, 200, 200)
    map_item.setExtent(area_layer.extent())
    layout.addLayoutItem(map_item)

    legend = QgsLayoutItemLegend(layout)
    legend.setLinkedMap(map_item)
    legend.setTitle('Legend')
    legend.attemptMove(QgsLayoutItemLegend.Point(230, 20))
    layout.addLayoutItem(legend)

    scalebar = QgsLayoutItemScaleBar(layout)
    scalebar.setLinkedMap(map_item)
    scalebar.applyDefaultSize()
    scalebar.attemptMove(QgsLayoutItemScaleBar.Point(230, 80))
    layout.addLayoutItem(scalebar)

    pdf_out = os.path.join(args.out, 'EssexMap.pdf')
    exporter = QgsLayoutExporter(layout)
    exporter.exportToPdf(pdf_out, QgsLayoutExporter.PdfExportSettings())
    logging.info(f'Exported map to {pdf_out}')

    # Exit QGIS
    qgs.exitQgis()
    logging.info('Script completed successfully.')

if __name__ == '__main__':
    main()
