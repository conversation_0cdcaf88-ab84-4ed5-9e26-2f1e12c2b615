from random import randrange
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import math
import os
from building.building import *

#day = 0
#prevday = 0

row_num = 0
# Figures setup
prevday_dict = {}
day_dict = {}# list can have a mixture of types eg integers, strings
nextday_dict = {}       # order of dictionary is not important
# order of list is significant
# day_list[0] looks first item in list
day_list = []
daily_temp = [1]        # stores all daily temperatures (8760 floats)
elec = []
average_temp = [1]      # for some reason the list needed a value in here to append
average_elec = [1]
average_carint = [1]

# Days and calendar setup
year = 2023
days_week = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun']
week_num = 1
day = 1
#prevday = 0
time_increment = 0.5 # change this depending on half-hourly or hourly data
hour_count = time_increment # the above changes this, and the end of the row_writer trigger
hour_increment = 0.5

Time_periods = np.arange(0.5, 24.5, 0.5).tolist()  # This line replaced a long manual list above. Lovely.

Stats = {'Day Number': '',
        'Week Number': '',
        'Day of Week': '',
        'Day of Week Number': '',
        'Date':'',
        'Hour': '',
        'Ambient Temperature': '',
        'Electricity Usage': '',
        'Carbon Intensity': '',
        'Electricity Cost': '',
        'Solar Irradiance' : '',
        'Solar Production': '',
        'Gas': '',
        'Heating': '',
        'Hot Water': '',
        'Cooking': '',
        'Rank Ascending': '',
        'Rank Descending': '',
        'Electricity Cost Scen A': '',
        'Electricity Carbon Emissions Scen A': '',
        'Gas Cost Scen A': '',
        'Gas Carbon Emissions Scen A': '',
        'Excess Solar Production': '',
        'Efficiency Losses': '',
        'Battery SoC': '',
        'Export Electricity': '',
        'Battery Status': ''
}

D = dict.fromkeys(Time_periods, Stats)
Year_days = list(range(1, 366)) # Not sure why, but 365 here errors out
Y = dict.fromkeys(Year_days, D)
car_int_as = dict.fromkeys(Year_days, D)
car_int_ds = dict.fromkeys(Year_days, D)

if year == 2019:  # This will change depending on the year. eg. 2019 started on a Tuesday
    year_start_day = 'Tue'
    #day_of_week = 'Tue'
    year_start_day_num = 2
    GHG_elec = 207
    #days_week_num = 2
elif year == 2018:
    year_start_day = 'Mon'
    #day_of_week = 'Mon'
    year_start_day_num = 1
    GHG_elec = 207
    #days_week_num = 1
elif year == 2021:
    year_start_day = 'Fri'
    year_start_day_num = 5
    GHG_elec = 207
elif year == 2020:
    year_start_day = 'Wed'
    year_start_day_num = 3
    GHG_elec = 207
elif year == 2022:
    year_start_day = 'Sat'
    year_start_day_num = 6
    GHG_elec = 207
elif year == 2023:
    year_start_day = 'Sun'
    year_start_day_num = 7
    GHG_elec = 207
elif year == 2024:
    year_start_day = 'Mon'
    year_start_day_num = 1
    GHG_elec = 207

day_of_week = year_start_day
days_week_num = year_start_day_num

# show information
show_htc = False

# Ventilation figures
AHU_FreshAirReq = 10 # litres per second per person

# Physics variables
SHC_Air = 1.01 # specific heat capacity of air, kJ/kg.K
SHC_Water = 4.18 # specific heat capacity of water kJ/kg.K
Density_Air = 1.2 # kg/m3
External_CO2 = 400 # PPM
Person_CO2 = (20/3600) # l/s
Comfort_CO2 = ((Person_CO2 / AHU_FreshAirReq) * 1000000) + External_CO2 # ~ 955 PPM achieved with 10 l/s
AHU_MassFlowRate = Density_Air * AHU_SupplyVolume

# General variables
global_warming = 0.0
Hour = 0
Year_NG = 0
Temperature = 0.0
Electricity_Usage = 0.0
Electricity_Usage_B = 0.0
Electricity_Usage_C = 0.0
Excess_Solar_Production = 0.0
Gas = 0.0
Sol_IR = 0.0
SolPV_prod = 0.0
#Elec_CarInt = 0.0
Elec_Cost = 0.0
Elec_Cost_Batt = 0.0
day_breakdown = randrange(364)     # This is the day we use to breakdown for more info eg. temp, elec etc
Carbon_Int = [1]
Carbon_Int_dict = {}        # trying this to label high/low carbon intensity
Carbon_Int_sort = [1]    # this will be used to sort the daily carbon emissions
Carbon_Int_sortR = [1]   # this will be used as above from descending order
Temperature_list = [1]
Electricity_list = [1]

sum_Electricity_Usage = 0.0
sum_Electricity_Usage_B = 0.0
sum_Electricity_Usage_C = 0.0

AmbTemp = [1] # pre corrected hourly ambient air temperature
AmbTempCor = [1]  # The corrected ambient air temperature to half hourly average difference from hourly data

# Induction hob
ind_hob_cooking = 0.0
ind_hob_efficiency = 0.8
gas_hob_efficiency = 0.4

# Heat Transfer Coefficients
tsp = 21 # temperature set point internal
dot = -3 # design outside temperature
boiler_ef = 0.85 # Assumed

THM_temp = 15 # thermal mass temperature at start
Prev_THM_temp = 15

daily_temp = []
daily_energy = 0
htc_list = []
htc_final = 0.0

# Battery figures
Battery_Charge = 0.0
# bat_num = 1          # Number of site batteries
# bat_capacity = 10 # kWh
# Max_Capacity = bat_num * bat_capacity
car_int = 1          # carbon intensity period for ranking method, currently 1 but will be adjusted for energy used
car_int_list = []   # carbon intensity list used for ranking method
# Max_Charge = 5 # kW
# Max_Charge_Capacity = bat_num * Max_Charge
# Max_Discharge = Max_Charge
# Max_Discharge_Capacity = Max_Charge_Capacity
Battery_efficiency = 1 # This will change based on ambient temperature
Battery_efficiency_fixed = False
# Battery_SoC = Max_Capacity # Start batteries full.
SoC_Excess = 0.0
Battery_Status = "None"
Export_Electricity = 0.0 # Electricity exported if excess battery charge or solar production

# Battery boolean checks
bat_SOC_full = False
solprod_exceeds_usage = False
low_carbon_period = False
high_carbon_period = False

# Solar PV
Daily_SolPV_prod = [1]
Gas_Convert = 11.1868 # m3 to kWh converter
Gas_CarInt = 230 # grams CO2 per kWh equivalent - Check source here
Gas_Cost = 4.0 # pence per kWh

# Building Model
heat_loss = 0.0

# Smart Export Guarantee (SEG)
# Will this fixed, or variable?
SEG = 5.5 # currently fixed at 5.5 p/kWh

array_as = np.array([])

# Efficiency values
SolarPV_efficiency = 0.3
SolartoBattery_efficiency = 0.9
Efficiency_Losses = 0.0 # Used between calculations to allow losses to sum
Efficiency_Losses_Sum = 0.0 # Used to sum losses using the above after the calculation
solar_loss = 14.0 # used with the EU PVGIS system as default

# Daily dictionaires
elec_dict = {}
sum_elec_data = {}
Sol_IR_dict = {}

# field_names = ['Hour',
#                'Temperature',
#                'Electricity_Usage',
#                'Carbon_Intensity_2018',
#                'Carbon_Intensity_2019',
#                'Cost',
#                'Solar_IR',
#                'Gas', # The three below sum to this
#                'Heating',
#                'Hot Water',
#                'Cooking'
# ]

field_names = [#'Year',
               #'Month',
               #'Day',
               #'Hour',
               #'Minute',
               'forecast',
               'actual',
               'index'
]

field_names_solar = ['SolarPVgeneration']

field_names_w = [
    'Day Number',
    'Day of Week',
    'Average Daily Temperature',
    'Average Electrical Usage',
    'Average Carbon Intensity',
    'Solar Irradiance',
    'Solar Production'
]

field_names_combined = [#'',
                        'Hour',
                        'Temperature',
                        'Electricity_Usage',
                        'Cost',
                        'Solar_IR',
                        'Gas',
                        'Heating',
                        'Hot Water',
                        'Cooking',
                        'forecast',
                        'actual',
                        'index',
                        'Rank Ascending',
                        'Rank Descending',
                        'SolarPVgeneration']
# Blank field above in the first column accounts for the numpy array creation later on

field_names_w_hourly = [
            'Day Number',
            'Week Number',
            'Day of Week',
            'Day of Week Number',
            'Hour',
            'Ambient Temperature',
            'Electricity Usage',
            'Carbon Intensity',
            'Electricity Cost',
            'Solar Irradiance',
            'Solar Production',
            'Gas',
            'Heating',
            'Hot Water',
            'Cooking',
            'Induction Cooking',
            'Gas Heating Carbon Intensity',
            'Gas DHW Carbon Intensity',
            'Gas Cooking Carbon Intensity',
            'Induction Cooking Carbon Intensity',
            'Rank Ascending',
            'Rank Descending',
            'Electricity Cost Scen A',
            'Electricity Carbon Emissions Scen A',
            'Gas Cost Scen A',
            'Gas Carbon Emissions Scen A',
            'Excess Solar Production',
            'Battery Efficiency',
            'Efficiency Losses',
            'Battery SoC',
            'Export Electricity',
            'Battery Status',
            'Electricity_Usage_Scen_B',
            'Elec_Cost_Scen_B',
            'ElecCarEm_Scen_B',
            'Electricity_Usage_Scen_C',
            'Elec_Cost_Scen_C',
            'ElecCarEm_Scen_C',
            'Heat Loss',
            'Occupancy',
            'Occupant Gains',
            'Thermal Mass Temperature',
            'Heating Setpoint',
            'Cooling Setpoint',
            'Thermal Status',
            'Heating Output',
            'Cooling Output',
            'CO2_PPM_Value',
]

field_names_w_annual = [
            'Day',
            'Week_Number',
            'Day_Week_Number',
            'Temperature',
            'Electricity_Usage',
            'Carbon Intensity',
            'Electricity Cost',
            'Solar_IR',
            'SolPV_prod',
            'Gas',
            'Heating',
            'Hot Water',
            'Cooking',
            'Induction Cooking',
            'Gas Heating Carbon Intensity',
            'Gas DHW Carbon Intensity',
            'Gas Cooking Carbon Intensity',
            'Induction Cooking Carbon Intensity',
            'Rank Ascending',
            'Rank Descending',
            'Elec_Cost_Scen_A',
            'ElecCarEm_Scen_A',
            'Gas_Cost_Scen_A',
            'Gas_CarEm_Scen_A',
            'Excess_Solar_Production',
            'Battery_efficiency',
            'Efficiency_Losses',
            'Battery_SoC',
            'Export_Electricity',
            'Electricity_Usage_Scen_B',
            'Elec_Cost_Scen_B',
            'ElecCarEm_Scen_B',
            'Electricity_Usage_Scen_C',
            'Elec_Cost_Scen_C',
            'ElecCarEm_Scen_C',
            'Heat Loss',
            'Occupancy',
            'Occupant Gains',
            'Thermal Mass Temperature',
            'Heating Setpoint',
            'Cooling Setpoint',
            'Thermal Status',
            'Heating Output',
            'Cooling Output',
]

field_names_temp_w = [
    'Hour',
    'Ambient Temperature'
]

field_names_car_int = [
    #'Hour',
    'Rank Ascending',
    'Rank Descending'
]

field_names_car_int_as = ['Rank Ascending']
field_names_car_int_ds = ['Rank Descending']

# Define base paths
import os

# Get the project root directory (parent of the directory containing this script)
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Define paths relative to project root
input_CSV = os.path.join('inputs', 'Basic data half hourly.csv')
New_CSV = os.path.join('calculations', 'Output Data.csv')
combined_CSV = os.path.join('calculations', 'Combined Data.csv')
New_CSV_temp = os.path.join('calculations', 'Output temp Data.csv')
Car_Int_as_CSV = os.path.join('calculations', 'Carbon Intensity Ranked Output as.csv')
Car_Int_ds_CSV = os.path.join('calculations', 'Carbon Intensity Ranked Output ds.csv')
Output_Text = os.path.join(project_root, 'report.txt')
#NG_CSV = os.path.join(project_root, 'national_grid_2023.csv')  # Assuming year is 2023
SolarPV_gen = os.path.join('calculations', 'SolarPV_generation.csv')

# Function to get simulation combined CSV path
def get_sim_combined_csv_path(simulation_number):
    return os.path.join('inputs', f'Sim Combined Data {simulation_number}.csv')

# Initialize other variables
Simulation_Number = 1  # Default value

# Plots
plot = 1
x = []
y = []
y_mean = []
def save_plot():
    global plot
    plt.savefig('EnergyModel'+str(plot)+'.png')
    plot = plot + 1

# Plot 1 - Electricity Usage over time # I've turned this off for now
# x = np.array([1, 2, 3])
# y = np.array([1, 2, 3])
# plt.title("Annual Electricity Usage")
# plt.xlabel("Time")
# plt.ylabel("Electricity Usage (kWh)")

