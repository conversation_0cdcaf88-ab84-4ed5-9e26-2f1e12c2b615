
''' Currently summing all heat loss including internal surfaces as tai and tao are designated outside the class'''

tai = 20.0
tao = -4.0
t_delta = tai - tao

floor_height = 2.32

class surface():
    AU = 0
    heat_loss = 0

    def __init__(self, name, area, U_value, t_delta):
        self.name = name
        self.area = area
        self.U_value = U_value
        self.t_delta = t_delta

    def calc_t_delta(self, tai, tao):
        t_delta = tai - tao
        return t_delta

    def calc_AU(self, area, U_value):
        AU = area * U_value
        return AU

    def sum_heat_loss(self, tai, tao):
        heat_loss = (self.area * self.U_value * (tai - tao))
        #print(f'{heat_loss} W')
        return heat_loss

    def area(self):
        name = self.name
        if name == 'Floor' or 'Ceiling':
            return floor_height * self.area
        else:
            print('Not floor or ceiling')
            return 0

class room():

    def __init__(self, room_name, surfaces, ach): # surfaces=None
        self.room_name = room_name
        self.surfaces = surfaces
        self.ach = ach # air changes per hour

    def sum_heat_loss(self, tai, tao, called_from_class=True):
        total_heat_loss = 0
        for surface in self.surfaces:
            #surface.calc_heat_loss(surface.area, surface.U_value, tai, tao)
            total_heat_loss += surface.sum_heat_loss(tai, tao)
        if called_from_class == True: # means this only prints if the room called it (Not the building)
            total_heat_loss = round(total_heat_loss,2)
            #print(f'Total heat loss {total_heat_loss} W') # commented out since currently this is called from room
        return total_heat_loss

    def list_surfaces(self):
        surfaces = self.surfaces
        #print(f'--{room.room_name}--')
        for surface in self.surfaces:
            print(surface.name)

    # current problem with this possibly is that a surface MUST be named 'Floor' to calculate volume
    def calc_volume(self):
        floor_area = None
        for floor in self.surfaces:
            if isinstance(floor, surface) and floor.name == 'Floor':
                floor_area = floor.area
                break
        return floor_area * floor_height

    def calc_infiltration(self, tai, tao):
        ach = self.ach
        floor_area = None
        for floor in self.surfaces:
            if isinstance(floor, surface) and floor.name == 'Floor':
                floor_area = floor.area
                break
        infiltration = round(floor_area * floor_height * ach * (tai - tao),2)
        return infiltration

class building():
    def __init__(self, building_name, rooms):
        self.building_name = building_name
        self.rooms = rooms

    def sum_heat_loss(self, tai, tao):
        building_heat_loss = 0
        for room in self.rooms: # called_from_class means this won't print room level heat loss
            building_heat_loss += room.sum_heat_loss(tai, tao, called_from_class=False)
        print(f'Building heat loss {round(building_heat_loss,2)} W')
        return building_heat_loss

    def list_surfaces(self):
        surfaces = self.surfaces
        #print(f'--{room.room_name}--')
        for surface in self.surfaces:
            print(surface.name)

    def sum_volume(self):
        building_volume = 0
        for room in self.rooms:
            building_volume += room.calc_volume()
            #print(f'{room.room_name} has volume {room.calc_volume()} m^3')
        print(f'Building total volume {round(building_volume,2)} m^3')
        return building_volume

# R-value in m2.K/W, l/lambda in W/m.K
# U-value in W/m2.K

material_external_wall = {
    'rsi': 0.13, # internal heat transfer, constant
    'rse': 0.04, # external heat transfer, constant
    'outerleaf_brick': {'thickness': '0.105', 'lambda': '0.84'},
    'cavity': {'thickness': '0.05', 'lambda': '0.18'},
    'aerated_concrete_block': {'thickness': '0.15', 'lambda': '0.24'},
    'dense_plaster': {'thickness': '0.013', 'lambda': '0.57'},
}


def calculate_u_value(materials):
    u_value = materials['rsi'] + materials['rse']

    for material in materials:
        if material not in ['rsi', 'rse']:
            thickness = float(materials[material]['thickness'])
            thermal_conductivity = float(materials[material]['lambda'])
            u_value += thickness / thermal_conductivity

    return 1 / u_value

# u_value = calculate_u_value(material_external_wall)
# print("U-value: {:.2f}".format(u_value)) # prints the U-value of the external wall above

# --- Bedroom 1 ---
external_wall_1 = surface('External Wall 1', 6.4, 0.89, (tai - tao))
external_wall_2 = surface('External Wall 2', 6.4, 0.89, (tai - tao))
internal_wall_1 = surface('Internal Wall 1', 8.9, 0.89, 0.0)
external_wall_3 = surface('External Wall 3', 7.9, 0.89, (tai - tao))
window_1 = surface('Window 1', 1.0, 2.5, (tai - tao))
ceiling = surface('Ceiling', 10.5, 0.42, (tai - tao))
floor = surface('Floor', 10.5, 2.35, 0.0)

bedroom_1 = room('Bedroom 1',[external_wall_1, external_wall_2, internal_wall_1, external_wall_3, window_1, ceiling, floor], 1)

# --- Bedroom 2 ---
external_wall_4 = surface('External Wall 4', 4.9, 0.89, (tai - tao))
external_wall_5 = surface('External Wall 5', 4.9, 0.89, (tai - tao))
internal_wall_2 = surface('Internal Wall 2', 7.0, 0.89, 0.0)
external_wall_6 = surface('External Wall 6', 6.0, 0.89, (tai - tao))
window_2 = surface('Window 1', 1.0, 2.5, (tai - tao))
ceiling_2 = surface('Ceiling', 6.3, 0.42, (tai - tao))
floor_2 = surface('Floor', 6.3, 2.35, 0.0)

bedroom_2 = room('Bedroom 2',[external_wall_4, external_wall_5, internal_wall_2, external_wall_6, window_2, ceiling_2, floor_2], 1)

# --- Bathroom ---
external_wall_7 = surface('External Wall 7', 4.4, 0.89, (tai - tao))
internal_wall_3 = surface('Internal Wall 3', 4.4, 0.89, (tai - tao))
internal_wall_4 = surface('Internal Wall 4', 4.1, 0.89, 0.0)
internal_wall_5 = surface('Internal Wall 5', 4.1, 0.89, (tai - tao))
ceiling_3 = surface('Ceiling', 3.3, 0.42, (tai - tao))
floor_3 = surface('Floor', 3.3, 2.35, 0.0)

bathroom = room('Bathroom',[external_wall_7, internal_wall_3, internal_wall_4, internal_wall_5, ceiling_3, floor_3], 2)

# --- Stairwell ---
external_wall_8 = surface('External Wall 8', 18.4, 0.89, (tai - tao))
internal_wall_6 = surface('Internal Wall 6', 4.6, 0.89, (tai - tao))
internal_wall_7 = surface('Internal Wall 7', 4.4, 0.89, 0.0)
internal_wall_8 = surface('Internal Wall 8', 2.7, 0.89, (tai - tao))
window_3 = surface('Window 3', 0.6, 2.5, (tai - tao))
ceiling_4 = surface('Ceiling', 5.5, 0.42, (tai - tao))
floor_4 = surface('Floor', 5.5, 2.35, 0.0)

stairwell = room('Stairwell',[external_wall_8, internal_wall_6, internal_wall_7, internal_wall_8, window_3, ceiling_4, floor_4], 2)

# --- Kitchen ---
external_wall_9 = surface('External Wall 9', 7.0, 0.89, (tai - tao))
internal_wall_9 = surface('Internal Wall 9', 7.0, 0.89, (tai - tao))
internal_wall_10 = surface('Internal Wall 10', 3.1, 0.89, 0.0)
internal_wall_11 = surface('Internal Wall 11', 3.7, 0.89, (tai - tao))
window_4 = surface('Window 4', 0.6, 2.5, (tai - tao))
ceiling_5 = surface('Ceiling', 4.8, 0.42, (tai - tao))
floor_5 = surface('Floor', 4.8, 2.35, 0.0)

kitchen = room('Kitchen',[external_wall_9, internal_wall_9, internal_wall_10, internal_wall_11, window_4, ceiling_5, floor_5], 1)

# --- Lounge/Diner ---
external_wall_10 = surface('External Wall 10', 16.1, 0.89, (tai - tao))
external_wall_11 = surface('External Wall 11', 7.3, 0.89, (tai - tao))
external_wall_12 = surface('External Wall 12', 2.5, 0.89, (tai - tao))
external_wall_13 = surface('External Wall 13', 1.9, 0.89, (tai - tao))
internal_wall_12 = surface('Internal Wall 12', 3.9, 0.89, (tai - tao))
internal_wall_13 = surface('Internal Wall 13', 7.2, 0.89, 0.0)
external_wall_14 = surface('External Wall 14', 2.2, 0.89, (tai - tao))
external_wall_15 = surface('External Wall 15', 0.4, 0.89, (tai - tao))
external_wall_16 = surface('External Wall 16', 2.0, 0.89, (tai - tao))
window_5 = surface('Window 5', 1.6, 2.5, (tai - tao))
door_1 = surface('Front Door', 1.8, 1.8, (tai - tao))
door_2 = surface('Patio Door', 2.4, 2.4, (tai - tao))
ceiling_6 = surface('Ceiling', 21.7, 0.42, (tai - tao))
floor_6 = surface('Floor', 21.7, 2.35, 0.0)

lounge_diner = room('Open Lounge Diner',[external_wall_10, external_wall_11, external_wall_12, external_wall_13,
                              internal_wall_12, internal_wall_13, external_wall_14, external_wall_15,
                              external_wall_15, window_5, door_1, door_2, ceiling_6, floor_6],
                    2
                 )

# print(f'-- {bedroom_1.room_name} --')
# bedroom_1.list_surfaces()
# bedroom_1.sum_heat_loss(tai, tao)
#
# print(f'-- {bedroom_2.room_name} --')
# bedroom_2.list_surfaces()
# bedroom_2.sum_heat_loss(tai, tao)



house = building('House', [bedroom_1, bedroom_2, bathroom, stairwell, kitchen, lounge_diner])
for room in house.rooms:
    print(f'{room.room_name} heat loss: {room.sum_heat_loss(tai, tao)} W, Volume: {round(lounge_diner.calc_volume(),2)} m^3')
#house.sum_heat_loss(tai, tao)
print(f'Total building heat loss: {house.sum_heat_loss(tai, tao)} W')
#print(f'Volume {round(lounge_diner.calc_volume(),2)} m^3')
house.sum_volume()
print(f'Infiltration losses are {lounge_diner.calc_infiltration(tai, tao)} W')
