inputslocation_latitude,51.507
inputslocation_longitude,-0.128
inputslocation_elevation,19.0
inputsmeteo_data_radiation_db,PVGIS-SARAH2
inputsmeteo_data_meteo_db,ERA5
inputsmeteo_data_year_min,2005
inputsmeteo_data_year_max,2020
inputsmeteo_data_use_horizon,True
inputsmeteo_data_horizon_db,DEM-calculated
inputsmounting_system_fixed_slope_value,35
inputsmounting_system_fixed_slope_optimal,False
inputsmounting_system_fixed_azimuth_value,0
inputsmounting_system_fixed_azimuth_optimal,False
inputsmounting_system_fixed_type,free-standing
inputspv_module_technology,c-Si
inputspv_module_peak_power,4.0
inputspv_module_system_loss,14.0
inputseconomic_data_system_cost,
inputseconomic_data_interest,
inputseconomic_data_lifetime,
fixed_0_month,1
fixed_0_E_d,5.22
fixed_0_E_m,161.84
fixed_0_H(i)_d,1.52
fixed_0_H(i)_m,47.13
fixed_0_SD_m,23.31
fixed_1_month,2
fixed_1_E_d,7.55
fixed_1_E_m,211.39
fixed_1_H(i)_d,2.22
fixed_1_H(i)_m,62.27
fixed_1_SD_m,54.89
fixed_2_month,3
fixed_2_E_d,11.39
fixed_2_E_m,353.08
fixed_2_H(i)_d,3.39
fixed_2_H(i)_m,104.95
fixed_2_SD_m,67.03
fixed_3_month,4
fixed_3_E_d,15.38
fixed_3_E_m,461.37
fixed_3_H(i)_d,4.72
fixed_3_H(i)_m,141.71
fixed_3_SD_m,65.06
fixed_4_month,5
fixed_4_E_d,15.75
fixed_4_E_m,488.29
fixed_4_H(i)_d,4.91
fixed_4_H(i)_m,152.35
fixed_4_SD_m,73.05
fixed_5_month,6
fixed_5_E_d,16.37
fixed_5_E_m,491.23
fixed_5_H(i)_d,5.19
fixed_5_H(i)_m,155.62
fixed_5_SD_m,52.53
fixed_6_month,7
fixed_6_E_d,16.11
fixed_6_E_m,499.39
fixed_6_H(i)_d,5.18
fixed_6_H(i)_m,160.57
fixed_6_SD_m,47.76
fixed_7_month,8
fixed_7_E_d,14.21
fixed_7_E_m,440.66
fixed_7_H(i)_d,4.53
fixed_7_H(i)_m,140.35
fixed_7_SD_m,47.05
fixed_8_month,9
fixed_8_E_d,12.89
fixed_8_E_m,386.56
fixed_8_H(i)_d,4.0
fixed_8_H(i)_m,120.14
fixed_8_SD_m,33.51
fixed_9_month,10
fixed_9_E_d,8.74
fixed_9_E_m,271.07
fixed_9_H(i)_d,2.65
fixed_9_H(i)_m,82.21
fixed_9_SD_m,38.82
fixed_10_month,11
fixed_10_E_d,6.4
fixed_10_E_m,191.89
fixed_10_H(i)_d,1.89
fixed_10_H(i)_m,56.77
fixed_10_SD_m,32.38
fixed_11_month,12
fixed_11_E_d,4.84
fixed_11_E_m,149.96
fixed_11_H(i)_d,1.41
fixed_11_H(i)_m,43.77
fixed_11_SD_m,25.07
outputstotals_fixed_E_d,11.25
outputstotals_fixed_E_m,342.23
outputstotals_fixed_E_y,4106.73
outputstotals_fixed_H(i)_d,3.47
outputstotals_fixed_H(i)_m,105.65
outputstotals_fixed_H(i)_y,1267.83
outputstotals_fixed_SD_m,13.3
outputstotals_fixed_SD_y,159.57
outputstotals_fixed_l_aoi,-3.15
outputstotals_fixed_l_spec,1.92
outputstotals_fixed_l_tg,-4.6
outputstotals_fixed_l_total,-19.02
metainputs_location_description,Selected location
metainputs_location_variables_latitude_description,Latitude
metainputs_location_variables_latitude_units,decimal degree
metainputs_location_variables_longitude_description,Longitude
metainputs_location_variables_longitude_units,decimal degree
metainputs_location_variables_elevation_description,Elevation
metainputs_location_variables_elevation_units,m
metainputs_meteo_data_description,Sources of meteorological data
metainputs_meteo_data_variables_radiation_db_description,Solar radiation database
metainputs_meteo_data_variables_meteo_db_description,Database used for meteorological variables other than solar radiation
metainputs_meteo_data_variables_year_min_description,First year of the calculations
metainputs_meteo_data_variables_year_max_description,Last year of the calculations
metainputs_meteo_data_variables_use_horizon_description,Include horizon shadows
metainputs_meteo_data_variables_horizon_db_description,Source of horizon data
metainputs_mounting_system_description,Mounting system
metainputs_mounting_system_choices,"fixed, vertical_axis, inclined_axis, two_axis"
metainputs_mounting_system_fields_slope_description,Inclination angle from the horizontal plane
metainputs_mounting_system_fields_slope_units,degree
metainputs_mounting_system_fields_azimuth_description,"Orientation (azimuth) angle of the (fixed) PV system (0 = S, 90 = W, -90 = E)"
metainputs_mounting_system_fields_azimuth_units,degree
metainputs_pv_module_description,PV module parameters
metainputs_pv_module_variables_technology_description,PV technology
metainputs_pv_module_variables_peak_power_description,Nominal (peak) power of the PV module
metainputs_pv_module_variables_peak_power_units,kW
metainputs_pv_module_variables_system_loss_description,Sum of system losses
metainputs_pv_module_variables_system_loss_units,%
metainputs_economic_data_description,Economic inputs
metainputs_economic_data_variables_system_cost_description,Total cost of the PV system
metainputs_economic_data_variables_system_cost_units,user-defined currency
metainputs_economic_data_variables_interest_description,Annual interest
metainputs_economic_data_variables_interest_units,%/y
metainputs_economic_data_variables_lifetime_description,Expected lifetime of the PV system
metainputs_economic_data_variables_lifetime_units,y
metaoutputs_monthly_type,time series
metaoutputs_monthly_timestamp,monthly averages
metaoutputs_monthly_variables_E_d_description,Average daily energy production from the given system
metaoutputs_monthly_variables_E_d_units,kWh/d
metaoutputs_monthly_variables_E_m_description,Average monthly energy production from the given system
metaoutputs_monthly_variables_E_m_units,kWh/mo
metaoutputs_monthly_variables_H(i)_d_description,Average daily sum of global irradiation per square meter received by the modules of the given system
metaoutputs_monthly_variables_H(i)_d_units,kWh/m2/d
metaoutputs_monthly_variables_H(i)_m_description,Average monthly sum of global irradiation per square meter received by the modules of the given system
metaoutputs_monthly_variables_H(i)_m_units,kWh/m2/mo
metaoutputs_monthly_variables_SD_m_description,Standard deviation of the monthly energy production due to year-to-year variation
metaoutputs_monthly_variables_SD_m_units,kWh
metaoutputs_totals_type,time series totals
metaoutputs_totals_variables_E_d_description,Average daily energy production from the given system
metaoutputs_totals_variables_E_d_units,kWh/d
metaoutputs_totals_variables_E_m_description,Average monthly energy production from the given system
metaoutputs_totals_variables_E_m_units,kWh/mo
metaoutputs_totals_variables_E_y_description,Average annual energy production from the given system
metaoutputs_totals_variables_E_y_units,kWh/y
metaoutputs_totals_variables_H(i)_d_description,Average daily sum of global irradiation per square meter received by the modules of the given system
metaoutputs_totals_variables_H(i)_d_units,kWh/m2/d
metaoutputs_totals_variables_H(i)_m_description,Average monthly sum of global irradiation per square meter received by the modules of the given system
metaoutputs_totals_variables_H(i)_m_units,kWh/m2/mo
metaoutputs_totals_variables_H(i)_y_description,Average annual sum of global irradiation per square meter received by the modules of the given system
metaoutputs_totals_variables_H(i)_y_units,kWh/m2/y
metaoutputs_totals_variables_SD_m_description,Standard deviation of the monthly energy production due to year-to-year variation
metaoutputs_totals_variables_SD_m_units,kWh
metaoutputs_totals_variables_SD_y_description,Standard deviation of the annual energy production due to year-to-year variation
metaoutputs_totals_variables_SD_y_units,kWh
metaoutputs_totals_variables_l_aoi_description,Angle of incidence loss
metaoutputs_totals_variables_l_aoi_units,%
metaoutputs_totals_variables_l_spec_description,Spectral loss
metaoutputs_totals_variables_l_spec_units,%
metaoutputs_totals_variables_l_tg_description,Temperature and irradiance loss
metaoutputs_totals_variables_l_tg_units,%
metaoutputs_totals_variables_l_total_description,Total loss
metaoutputs_totals_variables_l_total_units,%
