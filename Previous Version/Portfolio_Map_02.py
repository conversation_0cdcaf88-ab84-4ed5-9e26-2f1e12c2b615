'''
This is a streamlit file, so requires 'streamlit run Portfolio_Map_02.py' to use
Streamlit seems a bit slow, and is hosted so may have issues
There are also errors in this code, so it doesn't currently work very well

'''

import streamlit as st
import folium
import pandas as pd
import trimesh
import plotly.graph_objects as go
import random
import os
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from geopy.geocoders import Nominatim

# Streamlit UI
st.set_page_config(layout="wide")

# Initialize geocoder
geolocator = Nominatim(user_agent="building_locator")

# Sample Buildings with Postcodes Instead of Lat/Lon
buildings = [
    {"name": "HQ - London Central", "postcode": "SW1A 1AA", "model_file": "hq_model.obj", "texture_file": "hq_texture.jpg"},
    {"name": "Factory A", "postcode": "E1 6AN", "model_file": "factory_a.gltf", "texture_file": "factory_a_texture.png"},
    {"name": "Office B", "postcode": "WC2N 5DU", "model_file": "office_b.stl", "texture_file": "office_b_texture.jpg"},
    {"name": "Retail Space C", "postcode": "N1 9GU", "model_file": "retail_c.stl", "texture_file": "retail_c_texture.png"},
]

# Function to convert postcodes to latitude & longitude
def get_lat_lon(postcode):
    try:
        location = geolocator.geocode(postcode)
        if location:
            return location.latitude, location.longitude
    except Exception as e:
        st.error(f"Geolocation error: {e}")
    return None, None

# Add latitude & longitude to each building
for building in buildings:
    lat, lon = get_lat_lon(building["postcode"])
    building["lat"], building["lon"] = lat, lon

# Generate Synthetic Half-Hourly Energy Data for a Year
def generate_half_hourly_data():
    dates = pd.date_range(start="2024-01-01", periods=48*365, freq="30min")
    electricity = np.random.randint(50, 200, len(dates))
    gas = np.random.randint(30, 150, len(dates))
    return pd.DataFrame({"Timestamp": dates, "Electricity (kWh)": electricity, "Gas (kWh)": gas})

# Assign each building historical energy data
for building in buildings:
    building["energy_data"] = generate_half_hourly_data()

st.title("Building Portfolio with Postcode Geolocation")

# Folium Map
hq = buildings[0]  # HQ as reference
m = folium.Map(location=[hq["lat"], hq["lon"]], zoom_start=12)

# Add Buildings to Map
for building in buildings:
    if building["lat"] and building["lon"]:
        popup_html = f"""
        <b>{building['name']}</b><br>
        Postcode: {building['postcode']}<br>
        Latitude: {building['lat']}<br>
        Longitude: {building['lon']}
        """
        folium.Marker(
            location=[building["lat"], building["lon"]],
            popup=popup_html,
            icon=folium.Icon(color="red" if building["name"] == "HQ - London Central" else "blue"),
        ).add_to(m)

        # Draw lines from HQ to each building
        if building["name"] != "HQ - London Central":
            folium.PolyLine([(hq["lat"], hq["lon"]), (building["lat"], building["lon"])], color="gray", weight=2).add_to(m)

# Show Map in Streamlit
st.components.v1.html(m._repr_html_(), height=500)

# 3D Model Viewer
st.subheader("3D Building Models")

selected_building_3d = st.selectbox("Select a Building to View 3D Model", [b["name"] for b in buildings])

# Get Selected Model File
selected_model_file = next((b["model_file"] for b in buildings if b["name"] == selected_building_3d), None)

def load_3d_model(file_path):
    try:
        mesh = trimesh.load_mesh(file_path)
        return mesh
    except Exception as e:
        st.error(f"Failed to load 3D file: {e}")
        return None

if selected_model_file and os.path.exists(selected_model_file):
    mesh = load_3d_model(selected_model_file)

    if mesh:
        fig = go.Figure(data=[go.Mesh3d(
            x=mesh.vertices[:, 0],
            y=mesh.vertices[:, 1],
            z=mesh.vertices[:, 2],
            i=mesh.faces[:, 0],
            j=mesh.faces[:, 1],
            k=mesh.faces[:, 2],
            opacity=0.5,
            color='blue'
        )])

        fig.update_layout(
            scene=dict(
                xaxis=dict(visible=True),
                yaxis=dict(visible=True),
                zaxis=dict(visible=True),
                camera=dict(eye=dict(x=1.5, y=1.5, z=1.5)),  # Camera controls
            ),
            width=700,
            height=700
        )

        st.plotly_chart(fig)
    else:
        st.warning("No 3D model available for this building.")
else:
    st.warning("Selected model file not found.")

# WebGL Viewer for GLTF Models
if selected_model_file and selected_model_file.endswith('.gltf'):
    st.markdown(f"""
    <iframe src="https://gltf-viewer.donmccurdy.com/?model={selected_model_file}"
    width="700" height="500"></iframe>
    """, unsafe_allow_html=True)

# Energy Consumption Analysis
st.subheader("Energy Consumption Data")

# Select Time Granularity
time_granularity = st.radio(
    "Select Data View",
    ["Half-Hourly", "Daily", "Weekly", "Monthly"]
)

# Filter Data
energy_data = selected_data["energy_data"]

if time_granularity == "Daily":
    energy_data = energy_data.resample("D", on="Timestamp").sum()
elif time_granularity == "Weekly":
    energy_data = energy_data.resample("W", on="Timestamp").sum()
elif time_granularity == "Monthly":
    energy_data = energy_data.resample("M", on="Timestamp").sum()

# Select Energy Type
energy_type = st.radio("Energy Type", ["Electricity (kWh)", "Gas (kWh)"])

# Plot Energy Consumption
st.subheader(f"{selected_building} - {energy_type} ({time_granularity} View)")
plt.figure(figsize=(10, 4))
plt.plot(energy_data["Timestamp"], energy_data[energy_type], marker='o', linestyle='-', color="blue")
plt.xlabel("Time")
plt.ylabel(energy_type)
plt.title(f"{selected_building} - {energy_type} Consumption ({time_granularity})")
plt.xticks(rotation=45)
plt.grid(True)
st.pyplot(plt)

# Data Download Option
st.download_button(
    "Download Data",
    data=energy_data.to_csv().encode("utf-8"),
    file_name=f"{selected_building}_energy_{time_granularity}.csv",
    mime="text/csv"
)
