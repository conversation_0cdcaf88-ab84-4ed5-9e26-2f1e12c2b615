from variables import *
import statistics

def average(x):
    return round(statistics.mean(x), 2)

def sum_nested_dict_values(nested_dict):
    """
    Recursively sum the values in the last nest of a nested dictionary.
    """
    if not isinstance(nested_dict, dict):
        # Base case: not a dictionary, return 0.
        return 0
    elif all(isinstance(value, int) for value in nested_dict.values()):
        # Base case: all values are integers, return the sum.
        return sum(nested_dict.values())
    else:
        # Recursive case: call the function on each value in the dictionary.
        return sum(sum_nested_dict_values(value) for value in nested_dict.values())

def interpolate(x, x1, y1, x2, y2):
    """Linear interpolation between two points."""
    return y1 + ((x - x1) * (y2 - y1)) / (x2 - x1)

# def ranked(sort):
#     for i in range(0, bat_num):
#         result = np.where(sort == i)
#         # print(result)
#         # print(float(a[result]))
#         x = float(a[result] * car_int)
#         print(f'Carbon intensity: {x} gCO2')

def get_occupancy_ratio(hour):
    if hour in occupancy_series.index:
        return occupancy_series[hour]
    else:
        print(f"Occupancy data for {hour} not found. Defaulting to 0.0")
        return 0.0

def get_setpoint(Hour, setpoint_times, setpoint_values):
    # heating_setpoint_times = [0, 6.5, 7, 17, 17.5, 24]
    # heating_setpoint_values = [16, 16, 21, 21, 16, 16]

    # Find the nearest temperature values
    lower_hour = max(t for t in setpoint_times if t <= Hour)
    upper_hour = min(t for t in setpoint_times if t >= Hour)

    # Check if lower and upper temperature values are equal
    if lower_hour == upper_hour:
        setpoint = setpoint_values[setpoint_times.index(lower_hour)]
    else:
        # Find the corresponding efficiency values
        lower_setpoint = setpoint_values[setpoint_times.index(lower_hour)]
        upper_setpoint = setpoint_values[setpoint_times.index(upper_hour)]

        # Interpolate the efficiency for the given temperature
        setpoint = interpolate(Hour, lower_hour, lower_setpoint, upper_hour, upper_setpoint)

    return setpoint