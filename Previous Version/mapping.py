import folium
import pandas as pd
import re
import numpy as np
import branca.colormap as cm

# URL of the Wikipedia page
url = "https://en.wikipedia.org/wiki/List_of_countries_by_carbon_dioxide_emissions_per_capita"

# Read the tables from the Wikipedia page
tables = pd.read_html(url)

# Assuming the table of interest is the first one
carbon_footprint_table = tables[0]

# Display the first few rows of the table to verify
print(carbon_footprint_table.head())

# Save the table to a CSV file
carbon_footprint_table.to_csv("carbon_footprints_per_capita.csv", index=False)

print("Data has been successfully downloaded and saved to 'carbon_footprints_per_capita.csv'")

# eco_footprints = pd.read_csv("footprint.csv")
eco_footprints = pd.read_csv("carbon_footprints_per_capita.csv", skiprows=1)

# Display the first few rows to verify the structure
print("DataFrame after skipping the first row:")
print(eco_footprints.head())
selected_column = eco_footprints[['Country / territory', '2022']]
selected_column.columns = ['Country', '2022 Per capita CO2 emissions (tCO2/cap/year)']
print("\nSelected DataFrame:")
print(selected_column.head())

''' Some country names need fixing and filtering square brackets '''
def clean_country_name(name):
    # Remove square brackets and their contents
    name = re.sub(r'\[.*?\]', '', name)
    # Remove any strange symbols, keep alphanumeric characters and common punctuation
    name = re.sub(r'[^\w\s,.]', '', name)
    # Remove extra spaces
    name = ' '.join(name.split())
    return name

selected_column.loc[:, 'Country'] = selected_column['Country'].apply(clean_country_name)

name_corrections = {
    'Democratic Republic of the Congo': 'Congo, Democratic Republic of the',
    'Republic of the Congo': 'Congo, Republic of the',
    'Cape Verde': 'Cabo Verde',
    'GuineaBissau': 'Guinea-Bissau',
    'South Korea': 'Korea, South',
    'North Korea': 'Korea, North',
    'São Tomé and Príncipe': 'Sao Tome and Principe',
    'TimorLeste': 'Timor-Leste',
    'RÃ©union': 'Reunion',
    'Congo, Democratic Republic of the': 'Democratic Republic of the Congo',
    'Congo, Republic of the': 'Republic of the Congo',
    'Czech Republic': 'Czechia',
    'Korea, North': 'North Korea',
    'Korea, South': 'South Korea'
}

selected_column.loc[:, 'Country'] = selected_column['Country'].replace(name_corrections)

selected_column.to_csv("filtered_carbon_footprints_per_capita.csv", index=False)

print("Filtered data has been saved to 'filtered_carbon_footprints_per_capita.csv'")
eco_footprints = pd.read_csv("filtered_carbon_footprints_per_capita.csv")

# Determine appropriate bins using quantiles
quantiles = eco_footprints["2022 Per capita CO2 emissions (tCO2/cap/year)"].quantile([0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.75, 0.85, 0.9, 0.96, 0.98, 0.99, 1.0])
bins = list(quantiles.unique())
bins[-1] = eco_footprints["2022 Per capita CO2 emissions (tCO2/cap/year)"].max()  # Ensure the max value is included

# Create a color map
colormap = cm.linear.YlOrRd_09.scale(eco_footprints["2022 Per capita CO2 emissions (tCO2/cap/year)"].min(),
                                      eco_footprints["2022 Per capita CO2 emissions (tCO2/cap/year)"].max())


#max_eco_footprint = eco_footprints["2022 Per capita CO2 emissions (tCO2/cap/year)"].max()
''' https://geojson.org/ '''
political_countries_url = ("http://geojson.xyz/naturalearth-3.3.0/ne_50m_admin_0_countries.geojson")

# Folium using Leaflet

m = folium.Map(location=(45.5236, -122.6750), zoom_start=3, tiles="cartodb positron")
#folium.GeoJson(political_countries_url).add_to(m)
folium.Choropleth(
    geo_data=political_countries_url,
    data=eco_footprints,
    columns=["Country", "2022 Per capita CO2 emissions (tCO2/cap/year)"],
    key_on="feature.properties.name",
    bins=bins, #[0, 1, 1.5, 2, 3, 4, 5, 6, 7, 8, max_eco_footprint],
    fill_color="RdYlGn_r",
    fill_opacity=0.8,
    line_opacity=0.3,
    nan_fill_color="white",
    legend_name="Per capita CO2 emissions",
    name="Countries by CO2 emissions per capita",
).add_to(m)

#colormap.caption = "Per capita CO2 emissions"
#colormap.add_to(m)



folium.LayerControl().add_to(m)


m.save("map.html")