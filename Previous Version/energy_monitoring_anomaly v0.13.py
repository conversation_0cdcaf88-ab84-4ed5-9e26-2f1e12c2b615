'''
v0.13
NVIDI<PERSON> Cuda for GPU acceleration
Isolation Forest for anomaly detection
Random Forest Regression for forecasting
Gemma-3-1B for LLM response

'''

import os
import time
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer, BitsAndBytesConfig, Gemma3ForCausalLM
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.compose import ColumnTransformer
import joblib
import subprocess  # To automate Hugging Face login
from datetime import datetime
import plotly.express as px

# ----- Configuration -----
TEST_MODE = True  # When True, only the first anomaly gets an LLM explanation (for testing)
CSV_FILE = "HH_data_small.csv"  # Input CSV file
MODEL_DIR = r"N:\local_llm_model"  # Local directory for caching the LLM model
MODEL_NAME_FILE = os.path.join(MODEL_DIR, "model_name.txt")  # To store the downloaded model name
ANOMALY_DIR = r"E:\Python\Energy Model\BEMMS\Anomaly_Detection"  # Directory for saving anomaly outputs
answers = 0
answers_max = 3

model_name = "google/gemma-3-1b-it"

# Ensure the anomaly output directory exists
if not os.path.exists(ANOMALY_DIR):
    os.makedirs(ANOMALY_DIR)


# ----- Utility Functions -----
def check_cuda():
    """Checks if CUDA is available and prints the GPU device name if so."""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"✅ CUDA is available. Running on GPU: {gpu_name}")
    else:
        print("❌ CUDA is not available. Running on CPU.")


def ensure_huggingface_login():
    """Ensure user is logged in to Hugging Face."""
    try:
        subprocess.run(["huggingface-cli", "whoami"], check=True, stdout=subprocess.DEVNULL)
        print("✅ Hugging Face login detected.")
    except subprocess.CalledProcessError:
        print("🔐 Please log in to Hugging Face to speed up downloads.")
        subprocess.run(["huggingface-cli", "login"], check=True)


# ----- Model Loading Function -----
def load_model(model_name=model_name):
    """
    Loads the Gemma3ForCausalLM model with 8-bit quantization.
    If a cached model exists in MODEL_DIR and matches model_name, it loads from cache.
    Otherwise, it downloads the model and saves it for future use.

    Note: For 8-bit bitsandbytes models, there's no need to call .to(device) as they're
    already set to the correct device and dtype.
    """
    if not os.path.exists(MODEL_DIR):
        os.makedirs(MODEL_DIR)
    use_cached = False
    if os.path.exists(MODEL_NAME_FILE) and os.path.exists(os.path.join(MODEL_DIR, "config.json")):
        with open(MODEL_NAME_FILE, "r") as f:
            cached_model_name = f.read().strip()
        if cached_model_name == model_name:
            use_cached = True
            print("✅ Loading specified model from local cache.")
        else:
            print("⚠️ Cached model does not match the specified model. Downloading the correct model.")
    else:
        print("No cached model found. Downloading the specified model.")

    # For Gemma3, use 8-bit quantization.
    quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    if use_cached:
        tokenizer = AutoTokenizer.from_pretrained(MODEL_DIR)
        model = Gemma3ForCausalLM.from_pretrained(MODEL_DIR, quantization_config=quantization_config)
    else:
        print(f"⏳ Downloading {model_name} with 8-bit quantization for the first time. This may take a while...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=MODEL_DIR)
        model = Gemma3ForCausalLM.from_pretrained(model_name, cache_dir=MODEL_DIR,
                                                  quantization_config=quantization_config)
        tokenizer.save_pretrained(MODEL_DIR)
        model.save_pretrained(MODEL_DIR)
        with open(MODEL_NAME_FILE, "w") as f:
            f.write(model_name)
        print(f"✅ Model saved to {MODEL_DIR} for future use.")
    # Do not call model.to(device) for 8-bit models
    return model, tokenizer


# ----- Data Preprocessing and Anomaly Detection Functions -----
def preprocess_data(csv_file):
    """Loads CSV data and handles both long and wide formats."""
    df = pd.read_csv(csv_file)
    first_col = df.columns[0]
    df[first_col] = pd.to_datetime(df[first_col], dayfirst=True)
    # Check if already in long format (has "Timestamp" and a consumption column ending with _kWh)
    if "Timestamp" in df.columns and any(col.endswith("_kWh") for col in df.columns):
        print("✅ Detected long format (Timestamp-based).")
        return handle_missing_values(df)
    else:
        df = convert_wide_to_long(df)
        return handle_missing_values(df)


def convert_wide_to_long(df):
    """Converts wide-format data (one row per day, half-hourly columns) to long format."""
    df = df.rename(columns={df.columns[0]: "Date"})
    time_cols = [col for col in df.columns if ":" in col]
    if not time_cols:
        raise ValueError("❌ No half-hourly time columns detected in wide format.")
    print("🔄 Converting wide format to long format.")
    df_long = df.melt(id_vars=["Date"], value_vars=time_cols, var_name="Time", value_name="Electricity_kWh")
    df_long["Timestamp"] = pd.to_datetime(df_long["Date"].astype(str) + " " + df_long["Time"])
    df_long = df_long.drop(columns=["Date", "Time"])
    return df_long


def handle_missing_values(df):
    """Fills missing values using interpolation and backfill."""
    if df.isnull().sum().sum() > 0:
        print("⚠️ Missing values detected. Filling missing data...")
        df = df.interpolate().fillna(method="bfill")
    return df


def detect_anomalies(df):
    """Detects anomalies in the dataset using CPU-based Isolation Forest."""
    df['Hour'] = df['Timestamp'].dt.hour
    df['DayOfWeek'] = df['Timestamp'].dt.dayofweek
    features = ['Electricity_kWh', 'Hour', 'DayOfWeek']
    preprocessor = ColumnTransformer(transformers=[('num', StandardScaler(), features)]).set_output(transform="pandas")
    X_scaled = preprocessor.fit_transform(df)
    iso_model = IsolationForest(n_estimators=250, contamination=0.025, random_state=42)
    df['Anomaly'] = iso_model.fit_predict(X_scaled)
    return df, iso_model, preprocessor


def save_model(model, preprocessor, filename="isolation_forest_model.pkl"):
    """Saves the Isolation Forest model and preprocessor to a file."""
    joblib.dump({"model": model, "preprocessor": preprocessor}, filename)
    print(f"✅ Model and preprocessor saved as {filename}")


def clean_explanation(raw_text: str) -> str:
    """
    Removes any unwanted lines from the raw LLM response, such as the prompt
    or lines containing 'Timestamp: 20'. You can also remove other prompt lines
    if you wish.
    """
    lines = raw_text.splitlines()
    cleaned_lines = []
    for line in lines:
        # Skip lines containing the prompt header:
        if "Energy Consumption Analysis:" in line:
            continue
        # Skip the exact line with 'Timestamp: 20'
        if "Timestamp: 20" in line:
            continue

        # Add all other lines
        cleaned_lines.append(line)

    # Join the remaining lines back together
    cleaned_text = "\n".join(cleaned_lines).strip()
    return cleaned_text

def extract_answer(response: str) -> str:
    marker = "Answer:"
    if marker in response:
        return response.split(marker, 1)[1].strip()
    else:
        return response.strip()

# ----- Aggregated Data Functions -----
def aggregate_weekly(df, working_days=7):
    """
    Aggregates data by week and half-hour period.
    For working_days == 5, only Monday-Friday are used;
    For working_days == 7, all days are used.
    Returns an aggregated DataFrame with a 'Period' column.
    """
    if working_days == 5:
        df_filtered = df[df['Timestamp'].dt.dayofweek < 5].copy()
    else:
        df_filtered = df.copy()
    df_filtered['Week'] = df_filtered['Timestamp'].dt.isocalendar().week
    df_filtered['TimeOfDay'] = df_filtered['Timestamp'].dt.strftime("%H:%M")
    agg_df = df_filtered.groupby(['Week', 'TimeOfDay'], as_index=False)['Electricity_kWh'].mean()
    agg_df['Period'] = "Week " + agg_df['Week'].astype(str) + " - " + agg_df['TimeOfDay']
    agg_df = agg_df.sort_values(by=['Week', 'TimeOfDay'])
    return agg_df


def aggregated_anomaly_detection(agg_df):
    """
    Runs Isolation Forest on aggregated data using Electricity consumption and order index.
    """
    agg_df = agg_df.reset_index(drop=True)
    agg_df['Index'] = agg_df.index
    features = ['Electricity_kWh', 'Index']
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(agg_df[features])
    iso_model = IsolationForest(n_estimators=250, contamination=0.025, random_state=42)
    agg_df['Anomaly'] = iso_model.fit_predict(X_scaled)
    anomalies = agg_df[agg_df['Anomaly'] == -1]
    return agg_df, anomalies


def plot_aggregated_data(agg_df, scenario):
    """
    Creates an interactive Plotly line graph for the aggregated data.
    Anomalies are overlaid as red markers.
    """
    fig = px.line(agg_df, x='Period', y='Electricity_kWh',
                  title=f"Aggregated Average Consumption - {scenario} Scenario",
                  category_orders={'Period': agg_df['Period'].tolist()})
    anomalies = agg_df[agg_df['Anomaly'] == -1]
    if not anomalies.empty:
        fig.add_scatter(x=anomalies['Period'], y=anomalies['Electricity_kWh'], mode='markers',
                        marker=dict(color='red'), name='Anomalies')
    filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_{scenario}_aggregated.html")
    fig.write_html(filename)
    print(f"✅ Interactive graph for {scenario} scenario saved to {filename}")


def plot_original_data_interactive(df, anomalies):
    """
    Creates an interactive Plotly line graph for the original dataset.
    Anomalies are overlaid as red markers.
    """
    df_sorted = df.sort_values(by="Timestamp")
    anomalies_sorted = anomalies.sort_values(by="Timestamp")
    fig = px.line(df_sorted, x='Timestamp', y='Electricity_kWh', title="Original Electricity Consumption")
    if not anomalies_sorted.empty:
        fig.add_scatter(x=anomalies_sorted['Timestamp'], y=anomalies_sorted['Electricity_kWh'],
                        mode='markers', marker=dict(color='red'), name='Anomalies')
    filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_original_interactive.html")
    fig.write_html(filename)
    print(f"✅ Interactive chart for original data saved to {filename}")


# ----- LLM Explanation Function -----
def explain_anomalies_llm(anomalies, model, tokenizer):
    global answers_max
    """Generates LLM explanations for anomalies using the Gemma-3 model."""
    explanations = []
    total = len(anomalies)
    for idx, (_, row) in enumerate(anomalies.iterrows(), start=1):
        print(f"Processing anomaly {idx}/{total}", flush=True)
        prompt = f"""Energy Consumption Analysis:

- Timestamp: {row['Timestamp']}
- Electricity Usage: {row['Electricity_kWh']} kWh
- Time of Day: {row['Hour']}:00
- Day of Week: {row['DayOfWeek']} (0=Monday, 6=Sunday)

This energy usage has been detected as an anomaly. Explain why this could be happening but keep it short and concise.
Consider the Time of Day {row['Hour']}:00, and Day of Week {row['DayOfWeek']}, and whether the building would be open at this time.
For example, small usage might indicate lighting while larger usage could be due to HVAC systems.
Answer:"""
        inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
        # Move inputs to the model's device if applicable
        inputs = {k: v.to(model.device) for k, v in inputs.items()} if hasattr(model, "device") else inputs
        with torch.inference_mode():
            outputs = model.generate(**inputs, max_new_tokens=100)
        # Decode
        raw_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        answer = extract_answer(raw_response)

        print(f"Response: {answer}\n", flush=True)
        explanations.append((row['Timestamp'], row['Electricity_kWh'], answer))
        if TEST_MODE and idx > answers_max:
            print("Test mode enabled: processed only a set number of anomalies.\n", flush=True)
            break
    return explanations


# ----- Future Consumption Prediction (Random Forest) -----
def predict_future_consumption_rf(df, steps=10):
    """
    Trains a random forest regressor on historical consumption data, using time-based features,
    then predicts for 'steps' future intervals (half-hourly by default).

    Steps:
    1) Sort df by Timestamp.
    2) Create features: Unix timestamp, hour, day of week, etc.
    3) Train RandomForestRegressor.
    4) Generate future timestamps, build same features, predict consumption.
    5) Return a DataFrame of future predictions.
    """
    df_sorted = df.sort_values(by="Timestamp").copy()

    # Create time-based features
    df_sorted["Timestamp_numeric"] = df_sorted["Timestamp"].view("int64") // 10 ** 9
    df_sorted["Hour"] = df_sorted["Timestamp"].dt.hour
    df_sorted["DayOfWeek"] = df_sorted["Timestamp"].dt.dayofweek
    # Optionally add day of year or month
    df_sorted["DayOfYear"] = df_sorted["Timestamp"].dt.dayofyear

    # Prepare X, y
    features = ["Timestamp_numeric", "Hour", "DayOfWeek", "DayOfYear"]
    X = df_sorted[features]
    y = df_sorted["Electricity_kWh"]

    # Train Random Forest
    rf = RandomForestRegressor(n_estimators=250, random_state=42)
    rf.fit(X, y)

    # Identify the typical time step: assume half-hour if data is half-hourly
    # We'll guess it from the median difference between consecutive timestamps.
    time_diffs = df_sorted["Timestamp_numeric"].diff().dropna()
    if not time_diffs.empty:
        time_step = int(time_diffs.median())
    else:
        # default to 30 minutes
        time_step = 1800  # 30*60

    # Build future timestamps
    last_ts_numeric = df_sorted["Timestamp_numeric"].iloc[-1]
    future_ts_numeric = [last_ts_numeric + (i + 1) * time_step for i in range(steps)]
    future_timestamps = [pd.to_datetime(ts, unit="s") for ts in future_ts_numeric]

    # Create features for future timestamps
    future_data = pd.DataFrame({
        "Timestamp_numeric": future_ts_numeric,
        "Timestamp": future_timestamps
    })
    future_data["Hour"] = future_data["Timestamp"].dt.hour
    future_data["DayOfWeek"] = future_data["Timestamp"].dt.dayofweek
    future_data["DayOfYear"] = future_data["Timestamp"].dt.dayofyear

    # Predict
    X_future = future_data[features]
    y_pred = rf.predict(X_future)

    future_data["Predicted_Electricity_kWh"] = y_pred
    return future_data


def plot_future_predictions_rf(df, future_df):
    """
    Creates an interactive Plotly line graph showing historical data plus RandomForest predictions.
    """
    df_sorted = df.sort_values(by="Timestamp")
    fig = px.line(df_sorted, x='Timestamp', y='Electricity_kWh',
                  title="Historical & Future Consumption (Random Forest)")
    fig.add_scatter(x=future_df["Timestamp"], y=future_df["Predicted_Electricity_kWh"],
                    mode='lines+markers', name='RF Predictions', line=dict(color='red'))
    filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_future_predictions_rf.html")
    fig.write_html(filename)
    print(f"✅ Random Forest future predictions plot saved to {filename}")


# ----- Main Execution Flow -----
if __name__ == "__main__":
    overall_start = time.time()

    # Step 0: Ensure Hugging Face login, check CUDA, and load the Gemma-3 LLM model
    start = time.time()
    ensure_huggingface_login()
    check_cuda()
    the_model, tokenizer = load_model(model_name)
    step0_time = time.time() - start

    # Step 1: Preprocess raw data and detect original anomalies
    start = time.time()
    df = preprocess_data(CSV_FILE)
    df_with_anomalies, iso_model, preprocessor = detect_anomalies(df)
    save_model(iso_model, preprocessor)
    original_anomalies = df_with_anomalies[df_with_anomalies['Anomaly'] == -1]
    # Format the Timestamp column as dd/mm/yyyy for CSV output
    original_anomalies_formatted = original_anomalies.copy()
    original_anomalies_formatted["Timestamp"] = original_anomalies_formatted["Timestamp"].dt.strftime('%d/%m/%Y')
    orig_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_original_anomalies.csv")
    original_anomalies_formatted.to_csv(orig_filename, index=False)
    print(f"✅ Original anomalies saved to {orig_filename}")
    step1_time = time.time() - start

    # Step 2: Aggregated Data Analysis
    start = time.time()
    # Scenario a: 5-day working (Monday-Friday)
    agg_5d = aggregate_weekly(df, working_days=5)
    agg_5d, anomalies_5d = aggregated_anomaly_detection(agg_5d)
    plot_aggregated_data(agg_5d, "5-day")
    agg_5d_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_5day_aggregated.csv")
    agg_5d.to_csv(agg_5d_filename, index=False)
    print(f"✅ Aggregated 5-day data saved to {agg_5d_filename}")
    # Scenario b: 7-day (all days)
    agg_7d = aggregate_weekly(df, working_days=7)
    agg_7d, anomalies_7d = aggregated_anomaly_detection(agg_7d)
    plot_aggregated_data(agg_7d, "7-day")
    agg_7d_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_7day_aggregated.csv")
    agg_7d.to_csv(agg_7d_filename, index=False)
    print(f"✅ Aggregated 7-day data saved to {agg_7d_filename}")
    # Plot the original full dataset interactively
    plot_original_data_interactive(df_with_anomalies, original_anomalies)
    step2_time = time.time() - start

    # Step 3: Wait for user confirmation before proceeding with LLM explanations
    start = time.time()
    if TEST_MODE:
        user_input = input(
            "Aggregated anomaly detection finished. Type 'y' and press enter to continue with explanation finder: ").strip().lower()
        if user_input != 'y':
            print("Exiting script as per user request.")
            exit()
    step3_time = time.time() - start

    # Step 4: Run LLM explanation finder on the original anomalies (limited by TEST_MODE if True)
    start = time.time()
    if not original_anomalies.empty:
        explanations = explain_anomalies_llm(original_anomalies, the_model, tokenizer)
        explanation_df = pd.DataFrame(explanations, columns=["Timestamp", "Electricity_kWh", "Explanation"])
        merged_df = pd.merge(df_with_anomalies, explanation_df, on="Timestamp", how="left")
        # Format the Timestamp column as dd/mm/yyyy before saving the final CSV
        merged_df_copy = merged_df.copy()
        merged_df_copy["Timestamp"] = merged_df_copy["Timestamp"].dt.strftime('%d/%m/%Y')
        output_filename = os.path.join(ANOMALY_DIR,
                                       f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_anomaly_explanations.csv")
        merged_df_copy.to_csv(output_filename, index=False)
        # output_filename = os.path.join(ANOMALY_DIR,
        #                                f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_anomaly_explanations.csv")
        # explanation_df.to_csv(output_filename, index=False)
        print(f"✅ Anomalies and explanations saved to {output_filename}")
    step4_time = time.time() - start

    # Step 5: Forecast Future Consumption with Random Forest
    start = time.time()
    future_df = predict_future_consumption_rf(df_with_anomalies, steps=500)
    plot_future_predictions_rf(df_with_anomalies, future_df)
    print("\nFuture Predictions (Random Forest):")
    print(future_df[["Timestamp", "Predicted_Electricity_kWh"]])
    step5_time = time.time() - start

    overall_time = time.time() - overall_start

    print("\n--- Timing Summary ---")
    print(f"Step 0 (Login, CUDA check, model load): {step0_time:.2f} seconds")
    print(f"Step 1 (Preprocessing & original anomaly detection): {step1_time:.2f} seconds")
    print(f"Step 2 (Aggregated data analysis & plots): {step2_time:.2f} seconds")
    print(f"Step 3 (User confirmation): {step3_time:.2f} seconds")
    print(f"Step 4 (LLM explanation finder): {step4_time:.2f} seconds")
    print(f"Step 5 (Random Forest future consumption prediction): {step5_time:.2f} seconds")
    print(f"Total execution time: {overall_time:.2f} seconds")
