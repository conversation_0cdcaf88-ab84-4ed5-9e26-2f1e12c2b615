""""
Version History
--- working ---

line 558 breaks for year 2021, but not 2018 or 2019 - even though header 'year' doesn't match..?
import formulas such as U-values and thermal capacity

v0.##
HTC_calc is imported and running

--- Description ---
This is a python based energy building model, developed further from my Master's degree in which I developed an excel based model
The Excel model takes historic hourly electricity and gas usage, combines with this hourly solar radiance and ambient air temperature
 and carbon intensity from the National Grid.
The model then balances this electricity consumption against theoretical solar PV arrays and battery storage to balance across a time-of-use
 tariff and carbon intensity to reduce annual energy costs and carbon emissions.
The model also included a basic thermal model of heat loss based on U-values and thermal capacity, heating and cooling loads, AHU,
 DHW, internal gains such as occupants, equipment and solar gains, and electric vehicle charging profiles.

"""

import csv
from datetime import date
from datetime import time
from datetime import datetime
from random import randrange
import statistics
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import statistics
import requests
from io import StringIO

print(f'Setting variables...')

# Use upper case LETTERS for defining a constant that never changes
from typing import List, Any

#day = 0
#prevday = 0

row_num = 0
# Figures setup
prevday_dict = {}
day_dict = {}# list can have a mixture of types eg integers, strings
nextday_dict = {}       # order of dictionary is not important
# order of list is significant
# day_list[0] looks first item in list
day_list = []
daily_temp = [1]        # stores all daily temperatures (8760 floats)
elec = []
average_temp = [1]      # for some reason the list needed a value in here to append
average_elec = [1]
average_carint = [1]

# Days and calendar setup
year = 2019
days_week = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun']
week_num = 1
day = 1
#prevday = 0
time_increment = 0.5 # change this depending on half-hourly or hourly data
hour_count = time_increment # the above changes this, and the end of the row_writer trigger
hour_increment = 0.5

Time_periods = np.arange(0.5, 24.5, 0.5).tolist()  # This line replaced a long manual list above. Lovely.

Stats = {'Day Number': '',
        'Week Number': '',
        'Day of Week': '',
        'Day of Week Number': '',
        'Date':'',
        'Hour': '',
        'Ambient Temperature': '',
        'Electricity Usage': '',
        'Carbon Intensity': '',
        'Electricity Cost': '',
        'Solar Irradiance' : '',
        'Solar Production': '',
        'Gas': '',
        'Heating': '',
        'Hot Water': '',
        'Cooking': '',
        'Rank Ascending': '',
        'Rank Descending': '',
        'Electricity Cost Scen A': '',
        'Electricity Carbon Emissions Scen A': '',
        'Gas Cost Scen A': '',
        'Gas Carbon Emissions Scen A': '',
        'Excess Solar Production': '',
        'Efficiency Losses': '',
        'Battery SoC': '',
        'Export Electricity': '',
        'Battery Status': ''
}

D = dict.fromkeys(Time_periods, Stats)
Year_days = list(range(1, 366)) # Not sure why, but 365 here errors out
Y = dict.fromkeys(Year_days, D)
car_int_as = dict.fromkeys(Year_days, D)
car_int_ds = dict.fromkeys(Year_days, D)

if year == 2019:  # This will change depending on the year. eg. 2019 started on a Tuesday
    year_start_day = 'Tue'
    #day_of_week = 'Tue'
    year_start_day_num = 2
    #days_week_num = 2
elif year == 2018:
    year_start_day = 'Mon'
    #day_of_week = 'Mon'
    year_start_day_num = 1
    #days_week_num = 1
elif year == 2021:
    year_start_day = 'Fri'
    year_start_day_num = 5
elif year == 2020:
    year_start_day = 'Wed'
    year_start_day_num = 3

day_of_week = year_start_day
days_week_num = year_start_day_num

# show information
show_htc = False

# General variables
global_warming = 0.0
Hour = 0
Year_NG = 0
Temperature = 0.0
Electricity_Usage = 0.0
Electricity_Usage_B = 0.0
Excess_Solar_Production = 0.0
Gas = 0.0
Sol_IR = 0.0
SolPV_prod = 0.0
Elec_CarInt = 0.0
Elec_Cost = 0.0
day_breakdown = randrange(364)     # This is the day we use to breakdown for more info eg. temp, elec etc
Carbon_Int = [1]
Carbon_Int_dict = {}        # trying this to label high/low carbon intensity
Carbon_Int_sort = [1]    # this will be used to sort the daily carbon emissions
Carbon_Int_sortR = [1]   # this will be used as above from descending order
Temperature_list = [1]
Electricity_list = [1]

sum_Electricity_Usage = 0.0
sum_Electricity_Usage_B = 0.0

AmbTemp = [1] # pre corrected hourly ambient air temperature
AmbTempCor = [1]  # The corrected ambient air temperature to half hourly average difference from hourly data

# Induction hob
ind_hob_cooking = 0.0
ind_hob_efficiency = 0.8
gas_hob_efficiency = 0.4

# Heat Transfer Coefficients
tsp = 21 # temperature set point internal
dot = -3 # design outside temperature
boiler_ef = 0.85 # Assumed

daily_temp = []
daily_energy = 0
htc_list = []
htc_final = 0.0

# Solar PV
SolarPV_size = 4.0      # size of solar PV array in kWp
Daily_SolPV_prod = [1]
Gas_Convert = 11.1868 # m3 to kWh converter
Gas_CarInt = 230 # grams CO2 per kWh equivalent - Check source here
Gas_Cost = 4.0 # pence per kWh

# Battery figures
Battery_Charge = 0.0
bat_num = 3          # Number of site batteries
bat_capacity = 10 # kWh
Max_Capacity = bat_num * bat_capacity
car_int = 1          # carbon intensity period for ranking method, currently 1 but will be adjusted for energy used
car_int_list = []   # carbon intensity list used for ranking method
Max_Charge = 5 # kW
Max_Charge_Capacity = bat_num * Max_Charge
Max_Discharge = Max_Charge
Max_Discharge_Capacity = Max_Charge_Capacity
Battery_efficiency = 0.99 # This will change based on ambient temperature
Battery_SoC = 0.0 # Start batteries empty. Set to Max_Charge_Capacity to start filled
SoC_Excess = 0.0
Battery_Status = "None"
Export_Electricity = 0.0 # Electricity exported if excess battery charge or solar production

# Battery boolean checks
bat_SOC_full = False
solprod_exceeds_usage = False
low_carbon_period = False
high_carbon_period = False

# Smart Export Guarantee (SEG)
# Will this fixed, or variable?
SEG = 5.5 # currently fixed at 5.5 p/kWh

array_as = np.array([])

# Efficiency values
SolarPV_efficiency = 0.3
SolartoBattery_efficiency = 0.9
Efficiency_Losses = 0.0 # Used between calculations to allow losses to sum
Efficiency_Losses_Sum = 0.0 # Used to sum losses using the above after the calculation

# Daily dictionaires
elec_dict = {}
sum_elec_data = {}
Sol_IR_dict = {}

# field_names = ['Hour',
#                'Temperature',
#                'Electricity_Usage',
#                'Carbon_Intensity_2018',
#                'Carbon_Intensity_2019',
#                'Cost',
#                'Solar_IR',
#                'Gas', # The three below sum to this
#                'Heating',
#                'Hot Water',
#                'Cooking'
# ]

field_names = ['Year',
               'Month',
               'Day',
               'Hour',
               'Minute',
               'forecast',
               'actual',
               'index'
]

field_names_w = [
    'Day Number',
    'Day of Week',
    'Average Daily Temperature',
    'Average Electrical Usage',
    'Average Carbon Intensity',
    'Solar Irradiance',
    'Solar Production'
]

field_names_combined = ['',
                        'Hour',
                        'Temperature',
                        'Electricity_Usage',
                        'Cost',
                        'Solar_IR',
                        'Gas',
                        'Heating',
                        'Hot Water',
                        'Cooking',
                        'forecast',
                        'actual',
                        'index',
                        'Rank Ascending',
                        'Rank Descending']
# Blank field above in the first column accounts for the numpy array creation later on

field_names_w_hourly = [
            'Day Number',
            'Week Number',
            'Day of Week',
            'Day of Week Number',
            'Hour',
            'Ambient Temperature',
            'Electricity Usage',
            'Carbon Intensity',
            'Electricity Cost',
            'Solar Irradiance',
            'Solar Production',
            'Gas',
            'Heating',
            'Hot Water',
            'Cooking',
            'Induction Cooking',
            'Gas Heating Carbon Intensity',
            'Gas DHW Carbon Intensity',
            'Gas Cooking Carbon Intensity',
            'Induction Cooking Carbon Intensity',
            'Rank Ascending',
            'Rank Descending',
            'Electricity Cost Scen A',
            'Electricity Carbon Emissions Scen A',
            'Gas Cost Scen A',
            'Gas Carbon Emissions Scen A',
            'Excess Solar Production',
            'Efficiency Losses',
            'Battery SoC',
            'Export Electricity',
            'Battery Status',
            'Electricity_Usage_Scen_B',
            'Elec_Cost_Scen_B',
            'ElecCarEm_Scen_B',
]

field_names_temp_w = [
    'Hour',
    'Ambient Temperature'
]

field_names_car_int = [
    #'Hour',
    'Rank Ascending',
    'Rank Descending'
]

field_names_car_int_as = ['Rank Ascending']
field_names_car_int_ds = ['Rank Descending']

New_CSV = 'Output Data.csv'
combined_CSV = 'Combined Data.csv'
input_CSV = 'Basic data half hourly.csv'
New_CSV_temp = 'Output temp Data.csv'
Car_Int_as_CSV = 'Carbon Intensity Ranked Output as.csv'
Car_Int_ds_CSV = 'Carbon Intensity Ranked Output ds.csv'
Output_Text = 'report.txt'

with open(Output_Text, 'w') as f:
    f.write(f'Building Energy Modelling Simulation v0.1{2*chr(10)}') # {chr(10)} adds a new line, 2* does two lines


with open(New_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w_hourly)
  writer.writeheader()

with open(Car_Int_as_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)
  writer.writeheader()

with open(Car_Int_ds_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)
  writer.writeheader()

with open(combined_CSV, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
  writer = csv.DictWriter(csv_w_file, fieldnames=field_names_combined)
  writer.writeheader()

def write_row_hourly():
    with open(New_CSV,'a', newline='') as csv_w_file: # 'a' is append, 'w' is write
        writer = csv.DictWriter(csv_w_file, fieldnames=field_names_w_hourly)
        writer.writerow({
            'Day Number': day,
            'Week Number': (day_dict[day][Hour]['Week_Number']),
            'Day of Week': (day_dict[day][Hour]['Day_of_week']),
            'Day of Week Number': (day_dict[day][Hour]['Day_Week_Number']),
            'Hour': Hour,
            'Ambient Temperature': (day_dict[day][Hour]['Temperature']),
            'Electricity Usage': (day_dict[day][Hour]['Electricity_Usage']),
            'Carbon Intensity': (day_dict[day][Hour]['Carbon Intensity']),
            'Electricity Cost': (day_dict[day][Hour]['Electricity Cost']),
            'Solar Irradiance' : (day_dict[day][Hour]['Solar_IR']),
            'Solar Production': (day_dict[day][Hour]['SolPV_prod']),
            'Gas': (day_dict[day][Hour]['Gas']),
            'Heating': (day_dict[day][Hour]['Heating']),
            'Hot Water': (day_dict[day][Hour]['Hot Water']),
            'Cooking': (day_dict[day][Hour]['Cooking']),
            'Induction Cooking': (day_dict[day][Hour]['Induction Cooking']),
            'Gas Heating Carbon Intensity': (day_dict[day][Hour]['Gas Heating Carbon Intensity']),
            'Gas DHW Carbon Intensity': (day_dict[day][Hour]['Gas DHW Carbon Intensity']),
            'Gas Cooking Carbon Intensity': (day_dict[day][Hour]['Gas Cooking Carbon Intensity']),
            'Induction Cooking Carbon Intensity': (day_dict[day][Hour]['Induction Cooking Carbon Intensity']),
            'Rank Ascending': (day_dict[day][Hour]['Rank Ascending']),
            'Rank Descending': (day_dict[day][Hour]['Rank Descending']),
            'Electricity Cost Scen A': (day_dict[day][Hour]['Elec_Cost_Scen_A']),
            'Electricity Carbon Emissions Scen A': (day_dict[day][Hour]['ElecCarEm_Scen_A']),
            'Gas Cost Scen A': (day_dict[day][Hour]['Gas_Cost_Scen_A']),
            'Gas Carbon Emissions Scen A': (day_dict[day][Hour]['Gas_CarEm_Scen_A']),
            'Excess Solar Production': (day_dict[day][Hour]['Excess_Solar_Production']),
            'Efficiency Losses': (day_dict[day][Hour]['Efficiency_Losses']),
            'Battery SoC': (day_dict[day][Hour]['Battery_SoC']),
            'Export Electricity': (day_dict[day][Hour]['Export_Electricity']),
            'Battery Status': (day_dict[day][Hour]['Battery_Status']),
            'Electricity_Usage_Scen_B': (day_dict[day][Hour]['Electricity_Usage_Scen_B']),
            'Elec_Cost_Scen_B': (day_dict[day][Hour]['Elec_Cost_Scen_B']),
            'ElecCarEm_Scen_B': (day_dict[day][Hour]['ElecCarEm_Scen_B']),
        })

# This creates the function that calls an average over 24 hours
# def average(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x):
#     return (a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x) / 24

def average(x):
    return round(statistics.mean(x), 2)

def HTC_calc():
    #day_energy = (total_energy - heating_energy) / 24 # provides hourly average energy used for heating (gas)
    day_energy = daily_energy / 24
    temp_dif = tsp - statistics.mean(daily_temp) # delta T of internal set point temperature and average daily outside air temperature
    htc = ((day_energy / temp_dif) * boiler_ef) * (tsp - dot) # Watts
    if show_htc:
        print(f"HTC calculation today is {round(day_energy, 2)} kWh / {round(temp_dif, 2)} oC * {boiler_ef}) * ({tsp} oC - {dot} oC)")
    return round(htc, 2)

def sum_nested_dict_values(nested_dict):
    """
    Recursively sum the values in the last nest of a nested dictionary.
    """
    if not isinstance(nested_dict, dict):
        # Base case: not a dictionary, return 0.
        return 0
    elif all(isinstance(value, int) for value in nested_dict.values()):
        # Base case: all values are integers, return the sum.
        return sum(nested_dict.values())
    else:
        # Recursive case: call the function on each value in the dictionary.
        return sum(sum_nested_dict_values(value) for value in nested_dict.values())


# def sum(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x):
#     return (a + b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x)

class Events:
    def __init__(self, event_hold, event_full, event_charge, event_discharge, event_export):
        self.event_hold = event_hold
        self.event_full = event_full
        self.event_charge = event_charge
        self.event_discharge = event_discharge
        self.event_export = event_export

    def battery_event_hold(self):
        self.event_hold += 1

    def battery_event_full(self):
        self.event_full += 1

    def battery_event_charge(self):
        self.event_charge += 1

    def battery_event_discharge(self):
        self.event_discharge += 1

    def battery_event_export(self):
        self.event_export += 1

battery_events = Events(0, 0, 0, 0, 0)

def ranked(sort):
    for i in range(0, bat_num):
        result = np.where(sort == i)
        # print(result)
        # print(float(a[result]))
        x = float(a[result] * car_int)
        print(f'Carbon intensity: {x} gCO2')

# for i in time_periods:
#     # day_list = [{i: {'Day_of_week': day_of_week,
#     i = {'Day_of_week': day_of_week,
#          'Temperature': Temperature,
#          'Electricity_Usage': Electricity_Usage,
#          'Carbon Intensity': CarInt,
#          'Electricity Cost': Elec_Cost,
#          'Solar_IR': Sol_IR,
#          'SolPV_prod': SolPV_prod,
#          'Gas': Gas,
#          'Excess_Solar_Production': Excess_Solar_Production,
#          'Efficiency_Losses': Efficiency_Losses_Sum,
#          'Battery_SoC': Battery_SoC,
#          'Export_Electricity': Export_Electricity,
#          'Battery_Status': Battery_Status
#          }

# Sort ambient air temperature by inserting half hourly data to the available hourly data
# with open(New_CSV_temp, 'w', newline='') as csv_w_file:  # 'a' is append, 'w' is write
#   writer = csv.DictWriter(csv_w_file, fieldnames=field_names_temp_w)
#   writer.writeheader()
#
# field_names_temp = ['Hour', 'Temperature']
# with open('H:\\My Documents\\Ian\\Python\\Energy Model\\Ambient Temperature data.csv') as csv_file:
#     csv_dict_reader_temp = csv.DictReader(csv_file, field_names_temp)
#     next(csv_dict_reader_temp)  # skips header
#     print("Sorting ambient air temperature data...")
#     for row in csv_dict_reader_temp:
#         Hour = (row['Hour'])
#         Temperature = float(row['Temperature'])    # changes integer on every cycle
#
#         #AmbTemp.append(['Temperature'])
#
#         AmbTemp = (Temperature)
#         AmbTemp.insert(-1,0)
#         #AmbTemp[-1] = #(float(AmbTemp[-2]) + float(AmbTemp[-1])) / 2
#         write_row_temp()
#     # for i in range(len(AmbTemp)):
#     #     if i == 'HH':
#     #         AmbTemp[i] = 99
#     #         print("HH changed")
#     #print(len(AmbTemp))
#     print("Ambient Temperate list is " + str(AmbTemp))
#     #for row in csv_dict_reader_temp:
#     #    write_row_temp()

# Plots
plot = 1
x = []
y = []
y_mean = []
def save_plot():
    global plot
    plt.savefig('EnergyModel'+str(plot)+'.png')
    plot = plot + 1

# Plot 1 - Electricity Usage over time
# x = np.array([1, 2, 3])
# y = np.array([1, 2, 3])
plt.title("Annual Electricity Usage")
plt.xlabel("Time")
plt.ylabel("Electricity Usage (kWh)")

# Information
print(f'# # # # #')
print(f'Chosen year for data is {year}.')
print(f'Site has {SolarPV_size} kW of solar PV, {bat_num} batteries each with {bat_capacity} kWh of storage.')

''' National Grid download data '''

NG_CSV = 'national_grid_'+str(year)+'.csv'
if os.path.isfile(NG_CSV):
    print(f'National Grid Carbon Intensity data already downloaded')
else:
    NG_url = 'https://data.nationalgrideso.com/backend/dataset/f406810a-1a36-48d2-b542-1dfb1348096e/resource/0e5fde43-2de7-4fb4-833d-c7bca3b658b0/download/gb_carbon_intensity.csv'
    response = requests.get(NG_url)
    # Load CSV data into a pandas DataFrame
    df = pd.read_csv(StringIO(response.content.decode('utf-8')))

    # Split datetime column into separate date and time columns
    df['Date'] = pd.to_datetime(df['datetime'], format='%Y-%m-%dT%H:%M:%S')
    df['Day'] = df['Date'].dt.day
    df['Month'] = df['Date'].dt.month
    df['Year'] = df['Date'].dt.year
    df['Hour'] = df['Date'].dt.hour
    df['Minute'] = df['Date'].dt.minute

    # Reorder columns and save each year's data to a separate CSV file
    for ng_year in df['Year'].unique():
        filename = f'national_grid_{ng_year}.csv'
        df_year = df[df['Year'] == ng_year]
        #df_year = df_year[['Year', 'Month', 'Day', 'Hour', 'Minute', 'forecast', 'actual', 'index']]
        df_year = df_year[['forecast', 'actual', 'index']]
        df_year.to_csv(filename, index=False)
    print(f'National Grid Carbon Intensity data downloaded and updated, and saved to {NG_CSV}')
    # # Save updated data to a new CSV file
    # NG_CSV = 'gb_carbon_intensity'+str(Year_NG)+'.csv'
    # df.to_csv(NG_CSV, index=False)
''' --------------------------------------------- '''

#def main():
display_info = 0 # change to 1 to print yearly data
display_car_int = 0
with open(NG_CSV) as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names)
    next(csv_dict_reader)  # skips header
    # rank carbon intensity first
    print(f'# # # # #')
    print(f'Sorting National Grid ESO 24-hour forecast ranking...')
    for row in csv_dict_reader:
        Year_NG = float(row['Year'])
        if year == Year_NG:
            Elec_CarInt = float(row['forecast'])
        # else:
        #     break
        row_num = row_num + 1
        # Hour = float(row['Hour'])
        # Minute = float(row['Minute'])
        # if Minute == 30:
        #     Hour += 0.5
        hour_count = hour_count + time_increment
        car_int_list.append(Elec_CarInt)  # adds the carbon intensity period to the rank list (In future this will be changed to the forecast list)
        if hour_count > 24:
            # Elec_CarInt
            a = np.array(car_int_list)
            sorted_indices_as = np.argsort(a)
            sorted_indices_ds = np.argsort(-a)  # -a is descending
            ranks_as = np.empty_like(sorted_indices_as)
            ranks_as[sorted_indices_as] = np.arange(len(a))
            car_int_as[day] = ranks_as
            if display_car_int:
                print(f'Ascending order: {ranks_as}')
            ranks_ds = np.empty_like(sorted_indices_ds)
            ranks_ds[sorted_indices_ds] = np.arange(len(a))
            car_int_ds[day] = ranks_ds
            if display_car_int:
                print(f'Descending order: {ranks_ds}')
                print(f'Day is {day}')
            # writes the array to row
            with open(Car_Int_as_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
                writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_as)  #
                for element in ranks_as:  # for element in ranks_as
                    writer.writerow({
                        #'Hour': hour_increment, # Hour always comes out as 24
                        'Rank Ascending': element
                    })
                    hour_increment = hour_increment + time_increment
            hour_count = time_increment
            hour_increment = 0.5
            with open(Car_Int_ds_CSV, 'a', newline='') as csv_w_file:  # 'a' is append, 'w' is write
                writer = csv.DictWriter(csv_w_file, fieldnames=field_names_car_int_ds)  #
                for element in ranks_ds:  # for element in ranks_as
                    writer.writerow({
                        #'Hour': hour_increment, # Hour always comes out as 24
                        'Rank Descending': element
                    })
                    hour_increment = hour_increment + time_increment
            array_as = np.concatenate((array_as, ranks_as), axis=None)
            car_int_list = []  # clear list for next 24 hours
            hour_count = time_increment
            day = day + 1
            hour_increment = 0.5
    #print(f'Ranked array: {array_as}')
    Hour = 0
    time_increment = 0.5
    hour_count = 0.5
    row_num = 0
    day = 1
    # read data and write
    print(f'# # # # #')
    print(f'National Grid ESO 24-hour forecast ranked')
    #print(f'Writing National Grid ESO 24-hour forecast...')

print(f'# # # # #')
print(f'Combining National Grid ESO 24-hour forecast with input data...')

# merging the csv files of input plus ascending and descending rank of carbon intensity
df = pd.concat(map(pd.read_csv, [input_CSV, NG_CSV, Car_Int_as_CSV, Car_Int_ds_CSV]), axis=1)#, ignore_index=True)
df.to_csv(combined_CSV)

print(f'# # # # #')
print(f'Running Building Energy Modelling System (BEMS)...')
# Previously loaded input_CSV, now loads from combined_CSV to include the ranked National Grid ESO carbon intensity
# field_names also needed changing to the field_names_combined
with open(combined_CSV, 'r') as csv_file:
    csv_dict_reader = csv.DictReader(csv_file, field_names_combined)
    next(csv_dict_reader)  # skips header
    for row in csv_dict_reader:
        #global row_num, hour_count, Battery_SoC, week_num, day_of_week, days_week_num, day, day_breakdown
        row_num = row_num + 1
        # Variable resets
        Export_Electricity = 0.0  # Resets the export electricity
        Efficiency_Losses_Sum = 0.0  # Resets the efficiency losses
        Battery_Status = "Ready"
        Hour = float(row['Hour'])
        hour_count = hour_count + time_increment
        Temperature = float(row['Temperature'])    # changes integer on every cycle
        Electricity_Usage = float(row['Electricity_Usage']) #+ Battery_Charge    # adds battery_charge from previous HH period
        Electricity_Usage_B = Electricity_Usage
        Battery_Charge = 0 # resets the battery_level to 0 ready to charge/discharge again in next phase
        Elec_CarInt = float(row['forecast'])
        Gas = float(row['Gas']) #* Gas_Convert # used to be m3 which required conversion to kWh
        gas_heating = float(row['Heating'])
        gas_dhw = float(row['Hot Water'])
        gas_cooking = float(row['Cooking'])
        ind_hob_cooking = (gas_cooking * gas_hob_efficiency) / ind_hob_efficiency
        # Carbon Intensitites
        gas_heating_CarInt = gas_heating * Gas_CarInt
        gas_dhw = gas_dhw * Gas_CarInt
        gas_cooking_CarInt = gas_cooking * Gas_CarInt
        ind_hob_cooking_CarInt = ind_hob_cooking * Elec_CarInt
        # car_int_list.append(Elec_CarInt)    # adds the carbon intensity period to the rank list (In future this will be changed to the forecast list)
        rank_as = float(row['Rank Ascending']) + 1
        rank_ds = float(row['Rank Descending']) + 1
        Elec_Cost = float(row['Cost'])
        Sol_IR = float(row['Solar_IR'])
        SolPV_prod = Sol_IR * SolarPV_size * SolarPV_efficiency
        # Heat Transfer Coefficient
        daily_temp.append(Temperature)
        daily_energy = daily_energy + gas_heating
        # -------------
        # Battery Phase
        # -------------
        #
        # Battery Condition setup
        if Battery_SoC == Max_Capacity:
            bat_SOC_full = True
        elif Battery_SoC < Max_Capacity:
            bat_SOC_full = False
        Excess_Solar_Production = Electricity_Usage - SolPV_prod
        if Excess_Solar_Production < 0:
            solprod_exceeds_usage = True
        else:
            solprod_exceeds_usage = False
        if rank_as <= 1 + bat_num - 1: # integer each way is battery numbers
            low_carbon_period = True
        else:
            low_carbon_period = False
        if rank_as >= 48 - bat_num + 1: # integer each way is battery numbers
            high_carbon_period = True
        else:
            high_carbon_period = False
        #
        # Battery System flow
        if bat_SOC_full:
            if solprod_exceeds_usage:
                Battery_Status = "Export"
                battery_events.battery_event_export()
            elif solprod_exceeds_usage == False:
                if high_carbon_period:
                    Battery_Status = "Discharge"
                    battery_events.battery_event_discharge()
                elif high_carbon_period == False:
                    Battery_Status = "Full"
                    battery_events.battery_event_full()
        elif bat_SOC_full == False:
            if solprod_exceeds_usage:
                Battery_Status = "Charge"
                battery_events.battery_event_charge()
            elif solprod_exceeds_usage == False:
                if low_carbon_period:
                    Battery_Status = "Charge"
                    battery_events.battery_event_charge()
                elif low_carbon_period == False:
                    Battery_Status = "Hold"
                    battery_events.battery_event_hold()
        #
        # Battery Actions
        if Battery_Status == "Export":
            Battery_SoC = Battery_SoC + -(Excess_Solar_Production * Battery_efficiency) # adds the total excess on top (Remember solar is a -ve int)
            SoC_Excess = Battery_SoC - Max_Capacity # This is the excess to export, but charges the battery to full first
            Efficiency_Losses_Sum = Efficiency_Losses_Sum + -(Excess_Solar_Production * (1 - Battery_efficiency))
            Export_Electricity = Export_Electricity + SoC_Excess # exports excess energy
            Battery_SoC = Max_Capacity # Resets the battery to full
            Electricity_Usage_B = Electricity_Usage_B - Export_Electricity
            if Electricity_Usage_B < 0: # Export to grid if excess is more than consumed
                Elec_Cost = SEG
        if Battery_Status == "Discharge":
            Battery_SoC = Battery_SoC - Max_Discharge_Capacity
            Electricity_Usage_B = Electricity_Usage - (Max_Discharge_Capacity * Battery_efficiency) # Reduces the electricity usage by the discharge amount
            Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Max_Discharge_Capacity * (1 - Battery_efficiency))
            Elec_Cost = SEG # Smart Export Guarantee (SEG)
        #if Battery_Status == "Hold": # Do nothing
        if Battery_Status == "Charge":
            Battery_SoC = Battery_SoC + SolPV_prod # SolPV_prod includes efficiency losses
            if low_carbon_period:
                if Battery_SoC + Max_Charge_Capacity <= Max_Capacity: # full charge without exceeding the batteries SoC
                    Battery_SoC = Battery_SoC + (Max_Charge_Capacity * Battery_efficiency)
                    Electricity_Usage_B = Electricity_Usage + Max_Charge_Capacity # Adds live electricity usage
                    #Battery_Charge = Max_Charge_Capacity # This charge amount is added to next period's electricity usage
                    Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Max_Charge_Capacity * (1 - Battery_efficiency))
                elif Battery_SoC + Max_Charge_Capacity > Max_Capacity: # full charge which exceeds the batteries SoC and requires balancing
                    Battery_SoC = Battery_SoC + Max_Charge_Capacity # include efficiency loss in a second..
                    SoC_Excess = Battery_SoC - Max_Capacity # calculates the excess
                    Battery_Charge = Max_Charge_Capacity - SoC_Excess # calculates actual energy used to charge
                    Electricity_Usage_B = Electricity_Usage + (Battery_Charge / Battery_efficiency) # now take into account efficiency loss
                    Efficiency_Losses_Sum = Efficiency_Losses_Sum + (Battery_Charge * (1 - Battery_efficiency))
                    Battery_SoC = Max_Capacity  # Resets the battery to full
        #
        if display_info == 1:
            #print('Hour ' + Hour + ' has a temperature of ' + Temperature + ' and uses ' + Electricity + ' kWh of electricity' + ' costing ' + Elec_Cost +' p/kWh and emitting ' + CarInt + ' g/kWh. Solar PV production is ' + SolPV_prod + ' kWh')
            print(f"Hour {Hour} has a temperature of {Temperature} and uses {Electricity_Usage} kWh of electricity costing {'{0:.1f}'.format(Elec_Cost)} p/kWh, emits {Elec_CarInt} g/kWh. Solar PV produces {'{0:.2f}'.format(SolPV_prod)} kWh")
        # Formulas
        Elec_Cost_Scen_A =  (Elec_Cost * Electricity_Usage) / 100
        ElecCarEm_Scen_A = (Elec_CarInt * Electricity_Usage) / 1000
        Gas_Cost_Scen_A = (Gas_Cost * Gas) / 100
        Gas_CarEm_Scen_A = (Gas_CarInt * Gas) / 1000
        Elec_Cost_Scen_B = (Elec_Cost * Electricity_Usage_B) / 100
        ElecCarEm_Scen_B = (Elec_CarInt * Electricity_Usage_B) / 1000
        #
        #day_dict[Hour] = day_list  # puts lists within the dictionary
        day_dict[day] = {Hour: {'Week_Number': week_num,
                  'Day_of_week': day_of_week,
                  'Day_Week_Number': days_week_num,
                  'Temperature': Temperature,
                  'Electricity_Usage': Electricity_Usage,
                  'Carbon Intensity': Elec_CarInt,
                  'Electricity Cost': Elec_Cost,
                  'Solar_IR': Sol_IR,
                  'SolPV_prod': SolPV_prod,
                  'Gas': Gas,
                  'Heating': gas_heating,
                  'Hot Water': gas_dhw,
                  'Cooking': gas_cooking,
                  'Induction Cooking': ind_hob_cooking,
                  'Gas Heating Carbon Intensity': gas_heating_CarInt,
                  'Gas DHW Carbon Intensity': gas_dhw,
                  'Gas Cooking Carbon Intensity': gas_cooking_CarInt,
                  'Induction Cooking Carbon Intensity': ind_hob_cooking_CarInt,
                  'Rank Ascending': rank_as,
                  'Rank Descending': rank_ds,
                  'Elec_Cost_Scen_A': Elec_Cost_Scen_A,
                  'ElecCarEm_Scen_A': ElecCarEm_Scen_A,
                  'Gas_Cost_Scen_A': Gas_Cost_Scen_A,
                  'Gas_CarEm_Scen_A': Gas_CarEm_Scen_A,
                  'Excess_Solar_Production': Excess_Solar_Production,
                  'Efficiency_Losses': Efficiency_Losses_Sum,
                  'Battery_SoC': Battery_SoC,
                  'Export_Electricity': Export_Electricity,
                  'Battery_Status': Battery_Status,
                  'Electricity_Usage_Scen_B': Electricity_Usage_B,
                  'Elec_Cost_Scen_B': Elec_Cost_Scen_B,
                  'ElecCarEm_Scen_B': ElecCarEm_Scen_B,
                  }}
        # D[Hour] = {'Day_of_week': day_of_week,
        #           'Temperature': Temperature,
        #           'Electricity_Usage': Electricity_Usage,
        #           'Carbon Intensity': CarInt,
        #           'Electricity Cost': Elec_Cost,
        #           'Solar_IR': Sol_IR,
        #           'SolPV_prod': SolPV_prod,
        #           'Gas': Gas,
        #           'Excess_Solar_Production': Excess_Solar_Production,
        #           'Efficiency_Losses': Efficiency_Losses_Sum,
        #           'Battery_SoC': Battery_SoC,
        #           'Export_Electricity': Export_Electricity,
        #           'Battery_Status': Battery_Status
        #           }
        Y[day][Hour] = {'Week_Number': week_num,
                  'Day_of_week': day_of_week,
                  'Day_Week_Number': days_week_num,
                  'Temperature': Temperature,
                  'Electricity_Usage': Electricity_Usage,
                  'Carbon Intensity': Elec_CarInt,
                  'Electricity Cost': Elec_Cost,
                  'Solar_IR': Sol_IR,
                  'SolPV_prod': SolPV_prod,
                  'Gas': Gas,
                  'Heating': gas_heating,
                  'Hot Water': gas_dhw,
                  'Cooking': gas_cooking,
                  'Induction Cooking': ind_hob_cooking,
                  'Gas Heating Carbon Intensity': gas_heating_CarInt,
                  'Gas DHW Carbon Intensity': gas_dhw,
                  'Gas Cooking Carbon Intensity': gas_cooking_CarInt,
                  'Induction Cooking Carbon Intensity': ind_hob_cooking_CarInt,
                  'Rank Ascending': rank_as,
                  'Rank Descending': rank_ds,
                  'Elec_Cost_Scen_A': Elec_Cost_Scen_A,
                  'ElecCarEm_Scen_A': ElecCarEm_Scen_A,
                  'Gas_Cost_Scen_A': Gas_Cost_Scen_A,
                  'Gas_CarEm_Scen_A': Gas_CarEm_Scen_A,
                  'Excess_Solar_Production': Excess_Solar_Production,
                  'Efficiency_Losses': Efficiency_Losses_Sum,
                  'Battery_SoC': Battery_SoC,
                  'Export_Electricity': Export_Electricity,
                  'Battery_Status': Battery_Status,
                  'Electricity_Usage_Scen_B': Electricity_Usage_B,
                  'Elec_Cost_Scen_B': Elec_Cost_Scen_B,
                  'ElecCarEm_Scen_B': ElecCarEm_Scen_B,
                  }
        sum_Electricity_Usage += Electricity_Usage
        sum_Electricity_Usage_B += Electricity_Usage_B

        # Writes the daily output CSV file
        #elec_dict[Hour] = Electricity_Usage
        Sol_IR_dict[Hour] = Sol_IR
        write_row_hourly()  # This triggers the write_row trigger to write all information to the CSV data file
        if hour_count > 24:
            list = Sol_IR_dict.values()
            # HTC
            if show_htc:
                print(f"Day {day}")
                print(f"Daily energy used for heating is {round(daily_energy, 2)} kWh")
                print(f"Average DOT is {round(statistics.mean(daily_temp), 2)} degrees C")
                print(f"Energy required to heat property from {dot}C to {tsp}C is {HTC_calc()} kW")
            #
            # Rank carbon intensity in 24 hours

            # --------------------
            # print(f'Lowest carbon intensity values:')
            # ranked(ranks_as)
            # print(f'Highest carbon intensity values:')
            # ranked(ranks_ds)

            # ---------------------------------
            # Plots
            # mean_data = statistics.mean(list)
            # sum_data = sum(list)
            # x.append(day)
            # y.append(sum_data)
            # y_mean.append(mean_data)
            # -----
            #Y[day] = [{D1}
            hour_count = time_increment
            index = days_week.index(day_of_week)
            # Start a new week
            if days_week_num == 7: # end of week
                days_week_num = 1
            else:
                days_week_num = days_week_num + 1
            # Start a new week
            if index >= 6:
                day_of_week = 'Mon'
            else:
                day_of_week = days_week[index + 1]
            # Setup week number, starting from the day the year started with, rather than always a Monday - So all weeks are 7 days
            if days_week_num == year_start_day_num and day > 6:
                week_num = week_num + 1
            #Y[day] = D
            day = day + 1
            day_dict[day] = {}
            #if day < 60 or day > 335:  # temporary crude way of returning heating days, could also try heating amount
            htc_list.append(HTC_calc())
            # Clear HTC data
            daily_temp.clear()
            daily_energy = 0

# for i in range(1,365):
#     for h in range(1,25):
#         elec_dict[h] = Y[i][h]['Electricity_Usage']
#         print(elec_dict[h])
#         if h == 24:
#             sum_elec_data[i] = sum(elec_dict.values())
#     x.append(i)
#     y.append(sum_elec_data[i])


# Plots
#plotList = Y[day][Hour]['Carbon Intensity']
#x = np.array([1,17521])
#y = np.array(day_dict[day][Hour][Electricity_Usage])

#plt.plot(x, y)

#plt.plot(x, y_mean)

#print(year_start_day)





print(f'# # # # #')
today = date.today()
print("Today's date is ", today, " which is a",days_week[today.weekday()])    # also use today.day, today.month, today.year
sum_htc = sum(htc_list)
print(f"Heating Season Energy: {round(sum_htc, 2)} kW")
print(f"{len(htc_list)} heating periods")
htc_final = sum(htc_list) / len(htc_list)
print(f"Average Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW")
print(f'# # # # #')
print(f"Calculation completed") # Next calculate mean of htc_list ie all HTC in heating season
with open(Output_Text, 'a') as f:
    f.write(f"Todays date is {today} which is a {days_week[today.weekday()]}{2*chr(10)}")
    f.write(f"The battery carried out {battery_events.event_hold} hold events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_full} full events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_charge} charge events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_discharge} discharge events{chr(10)}")
    f.write(f"The battery carried out {battery_events.event_export} export events{2*chr(10)}")
    f.write(f"Average Heat Transfer Coefficient calculation of all heating days is {round(htc_final, 2)} kW{2*chr(10)}")
    f.write(f"Scenario A Electricity Usage {round(sum_Electricity_Usage, 2)} kWh{chr(10)}")
    f.write(f"Scenario B Electricity Usage {round(sum_Electricity_Usage_B, 2)} kWh{chr(10)}")


# today.weekday 0 is monday, 6 is sunday
#print(day_of_week)

day_breakdown = day_breakdown + 1
#print(f"Day {day_breakdown} of {year} is a {Y[day_breakdown][Hour]['Day_of_week']}")   # +1 day because it starts at 0
#print(day_dict[day_breakdown])
if display_info:
    print(f'# # # # #')
    print(f'Sorted carbon intensity on day {day_breakdown} is {car_int_as[day_breakdown]}')
#print(day_list)
if display_info:
    print(f'# # # # #')
    print(D)
#for i in range(1,24):
if display_info:
    print(f'# # # # #')
    print(Y[1])

#save_plot()
#plt.show()


#print("Electricity usage was " + str(day_dict[day_breakdown]))

# # print(f"Average daily temperature is {'{0:.2f}'.format(average_temp[day_breakdown])} degrees C")        #prints, which means the last field is duplicated
# # print(f"Average daily electricity usage is {'{0:.2f}'.format(average_elec[day_breakdown])} kWh")
# print(Carbon_Int[day_breakdown])
#
# # This group sorts the carbon intensity in the current day to the number of batteries
# Carbon_Int_sort = sorted(Carbon_Int[day_breakdown])
# Carbon_Int_sort = Carbon_Int_sort[0:bat_num]
#
# Carbon_Int_dict = {""}
#
# print(Carbon_Int_sort)          # sorts carbon intensity, sorted by lowest, for number of batteries
#
# Carbon_Int_sortR = sorted(Carbon_Int[day_breakdown])
# Carbon_Int_sortR.sort(reverse=True)
# Carbon_Int_sortR = Carbon_Int_sortR[0:bat_num]
# print(Carbon_Int_sortR)             # for some reason above two need to go after Carbon_Int_sort
#
# #for i in range(0,bat_num):
#     #Carbon_Int_dict[i] = 1
#     #Carbon_Int_dict["Carbon_Int_sortR"] = 0
#     #if Carbon_Int_sort[i] == Carbon_Int[i]:
#     #    (Carbon_Int[i][i], "hmm")
# print(Carbon_Int_sort[0])
# print(Carbon_Int_dict)
#
# print(Temperature_list[day_breakdown])      # prints 24 hourly temperature
# print("Electricity is " + str(Electricity_list[day_breakdown]))

# print(day_of_week[200])     # returns the day of week for specified day number
#for i in range(1,24):
#    print(str(average_temp))
#for i in range(1,24):



            #print(type(day_dict['4'][0])) # returns class type
            #print((day_dict['4'][0]))  # returns a set position
            #new_value = new_value + float(day_dict['4'][0])
            #print('Daily temperatures ' + str(daily_temp))
            #print(max(daily_temp))
            #print(day_dict.keys())     # prints the keys eg 1-24
            #print(day_dict.values())   # prints the values

            #print(day_dict[day_list[1]])


# if __name__ == "__main__":
#     main()