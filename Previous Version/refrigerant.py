#import CoolProp.CoolProp as CP
import numpy as np
import matplotlib.pyplot as plt

refrigerant_detail = True
SHC_Water = 4.18 # specific heat capacity of water

def plot_refrigerant_cycle(evap_temp_C, cond_temp_C, Thermal_Status, superheat=5, subcooling=5):
    refrigerant = 'R32' #'R134a'

    # Convert temperatures to Kelvin
    evap_temp_K = evap_temp_C + 273.15
    cond_temp_K = cond_temp_C + 273.15

    # State 1: Saturated vapor at evaporator outlet (after evaporation, before compression)
    P1 = CP.PropsSI('P', 'T', evap_temp_K, 'Q', 1, refrigerant)
    h1 = CP.PropsSI('H', 'T', evap_temp_K, 'Q', 1, refrigerant)

    # State 2: Superheated vapor (after compression)
    P2 = CP.PropsSI('P', 'T', cond_temp_K, 'Q', 1, refrigerant)
    s1 = CP.PropsSI('S', 'T', evap_temp_K, 'Q', 1, refrigerant)
    h2 = CP.PropsSI('H', 'P', P2, 'S', s1, refrigerant)

    # State 3: Saturated liquid at condenser outlet (after condensation, before expansion)
    h3 = CP.PropsSI('H', 'T', cond_temp_K, 'Q', 0, refrigerant)
    P3 = P2  # Same pressure as P2 because condensation is at constant pressure

    # decimal sort
    h1 = h1 / 1000
    P1 = P1 / 1000
    h2 = h2 / 1000
    P2 = P2 / 1000
    h3 = h3 / 1000
    P3 = P3 / 1000

    # State 4: Subcooled liquid (after expansion valve)
    h4 = h3  # Constant enthalpy process (in a simplified model)
    P4 = P1  # Same pressure as P1 because expansion is at constant pressure

    effI = 0.65  # assume isentropic efficiency of compressor (%)
    # effI = (h2 - h1) / (h2l - h1)
    h2s = ((h2 - h1) / effI) + h1

    h1 = 405 # kJ.kg-1
    h2 = 455 # kJ.kg-1
    h3 = 285 #kJ.kg-1
    h4 = h3

    effI = 0.65  # assume isentropic efficiency of compressor (%)
    # effI = (h2 - h1) / (h2l - h1)
    h2s = ((h2 - h1) / effI) + h1




    # 5k super heat, 5k super cool
    mass_flow_rate_fluid = 2  # kg.s-1 of fluid
    #Tao = 50
    #Tcond = Tao + 5  # condensor 5k higher
    evap_temp_C_cool = evap_temp_C - 5
    Tevap_delta = evap_temp_C - evap_temp_C_cool
    Qe = mass_flow_rate_fluid * SHC_Water * Tevap_delta  # kW
    mass_flow_rate_re = (Qe / (h1 - h4))
    Wc = mass_flow_rate_re * (h2s - h1)  # kW
    Qc = mass_flow_rate_re * (h2 - h3)
    COP_cool = round(Qe / Wc, 2)
    COP_heat = round(Qc / Wc, 2)
    COP = (h2 - h3) / (h2 - h1)
    if refrigerant_detail:
        print(f'Evaporator temp: {Tevap_delta}')
        print(f'Qe: {Qe}, Qc: {Qc}, Wc: {Wc}, mass flow rate re: {mass_flow_rate_re}')

        ''''# Calculate COP
        # COP = (Heat absorbed in evaporator) / (Work input to compressor)
        heat_absorbed = h1 - h4  # Heat absorbed in evaporator
        work_input = h2 - h1  # Work input to compressor
        COP = heat_absorbed / work_input'''

        print(f'h1: {h1}, h2: {h2}, h2s: {h2s}, h3: {h3}, h4: {h4}')
        print(f'P1: {P1}, P2: {P2}, P3: {P3}, P4: {P4}')

    if Thermal_Status == "Heating":
        print(f'Coefficient of Performance (COP heating): {COP_heat:.2f}')
    elif Thermal_Status == "Cooling":
        print(f'Coefficient of Performance (COP cooling): {COP_cool:.2f}')
    elif Thermal_Status == "Both":
        print(f'Coefficient of Performance (COP heating): {COP_heat:.2f}')
        print(f'Coefficient of Performance (COP cooling): {COP_cool:.2f}')

    #print(f'Coefficient of Performance (COP Total): {COP:.2f}')
    '''
    # Define h_min and h_max based on valid enthalpy ranges for R134a
    h_min = CP.PropsSI('H', 'T', 200, 'Q', 0, refrigerant)  # low temp, saturated liquid
    h_max = CP.PropsSI('H', 'T', 370, 'Q', 1, refrigerant)  # high temp, saturated vapor
    h_vals = np.linspace(h_min, h_max, 500)

    def safe_propsSI(output, input1, value1, input2, value2, ref):
        try:
            return CP.PropsSI(output, input1, value1, input2, value2, ref)
        except ValueError:
            return np.nan

    P_vals_liquid = [safe_propsSI('P', 'H', h, 'Q', 0, refrigerant) for h in h_vals]
    P_vals_vapor = [safe_propsSI('P', 'H', h, 'Q', 1, refrigerant) for h in h_vals]

    # Remove any invalid (NaN) values
    valid_liquid_indices = ~np.isnan(P_vals_liquid)
    valid_vapor_indices = ~np.isnan(P_vals_vapor)

    plt.figure(figsize=(10, 6))
    plt.plot(h_vals[valid_liquid_indices] / 1000, np.array(P_vals_liquid)[valid_liquid_indices],
             label='Saturated Liquid Line')
    plt.plot(h_vals[valid_vapor_indices] / 1000, np.array(P_vals_vapor)[valid_vapor_indices],
             label='Saturated Vapor Line')

    # Cycle Points
    cycle_h = [h1, h2, h3, h4, h1]  # Closing the cycle
    cycle_P = [P1, P2, P3, P4, P1]

    plt.plot(np.array(cycle_h) / 1000, cycle_P, 'ro-', label='Refrigeration Cycle')

    plt.yscale('log')
    plt.xlabel('Enthalpy (kJ/kg)')
    plt.ylabel('Pressure (bar)')
    plt.title(f'P-H Diagram with Refrigerant Cycle for R134a')
    plt.legend()
    plt.grid(True)
    plt.show()'''

# # Example usage with realistic temperatures
# evap_temp_C = 15  # Evaporator temperature (°C)
# cond_temp_C = 30  # Condenser temperature (°C)
#
# plot_refrigerant_cycle(evap_temp_C, cond_temp_C, "Both")
