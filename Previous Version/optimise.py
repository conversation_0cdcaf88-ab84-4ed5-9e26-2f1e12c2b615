from scipy.optimize import minimize
import numpy as np

# Sample data representing solar panel arrays
# Format: (size, capital cost, maintenance cost, lifetime)
solar_arrays = [
    (1, 14000, 500, 20),
    (2, 18000, 800, 25),
    (3, 25000, 1000, 30)
]

# Energy generation data (kWh/year) for each array size
energy_generation = {
    1: 8000,
    2: 7000,
    3: 10000
}

# Cost of importing electricity (per kWh)
import_cost = 0.1  # $0.10/kWh

# Profit from exporting solar electricity (per kWh)
export_profit = 0.05  # $0.05/kWh

# Building consumption
building_consumption = 12000  # kWh/year

# Function to calculate net present value (NPV) for given array size
def calculate_npv(size, lifetime, capital_cost, maintenance_cost, energy_generation):
    total_revenue = sum((energy_generation[size] * (1 - import_cost + export_profit)) for _ in range(lifetime))
    total_cost = capital_cost + sum(maintenance_cost for _ in range(lifetime))
    npv = total_revenue - total_cost
    return npv

# Objective function for optimization
# Objective function for optimization
def objective_function(sizes):
    sizes = np.round(sizes).astype(int)  # Round sizes to nearest integer
    total_npv = sum(calculate_npv(size, *solar_arrays[size-1][1:], energy_generation) for size in sizes)
    return -total_npv  # Negate to convert maximization problem to minimization


# Constraint: Total energy generation should meet or exceed building consumption
def constraint_consumption(sizes):
    sizes = np.round(sizes).astype(int)  # Round sizes to nearest integer
    total_energy = sum(energy_generation[size] for size in sizes)
    return total_energy - building_consumption

# Initial guess for array sizes (equal allocation)
initial_guess = [1] * len(solar_arrays)

# Bounds for each array size (minimum 1, maximum 3 in this case)
bounds = [(1, 3)] * len(solar_arrays)

# Optimization
result = minimize(objective_function, initial_guess, method='SLSQP', bounds=bounds, constraints={'type': 'ineq', 'fun': constraint_consumption})

# Extract results
optimal_sizes = result.x
optimal_npv = -result.fun

print("Optimal array sizes:", optimal_sizes)
print("Optimal NPV:", optimal_npv)
