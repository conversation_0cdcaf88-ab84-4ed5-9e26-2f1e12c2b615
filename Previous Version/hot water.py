import math


class HotWaterStorageTank:
    def __init__(self, volume, initial_temp, insulation_thickness, insulation_thermal_conductivity,
                 tai, cold_water_temp):
        self.volume = volume  # liters
        self.water_mass = self.volume * 1000  # grams
        self.temperature = initial_temp  # Celsius
        self.insulation_thickness = insulation_thickness  # meters
        self.insulation_thermal_conductivity = insulation_thermal_conductivity  # W/(m*K)
        self.tai = tai  # Celsius
        self.cold_water_temp = cold_water_temp  # Celsius

    def remove_water(self, volume):
        if volume > self.volume:
            raise ValueError("Cannot remove more water than tank capacity")
        self.water_mass -= volume * 1000  # convert volume to grams
        self._update_temperature()

    def add_water(self, volume, temp):
        new_mass = self.water_mass + volume * 1000 * (temp - self.temperature)  # energy balance
        self.water_mass = min(new_mass, self.volume * 1000)  # cap at tank capacity
        self._update_temperature()

    def _update_temperature(self):
        # Calculate new temperature based on energy balance
        energy_in = self.volume * 1000 * (self.temperature - self.cold_water_temp) * 4.186  # Joules
        energy_out = self._heat_loss() * (3600 / 4.186)  # convert W to J/s
        self.temperature = (energy_in - energy_out) / (self.water_mass * 4.186) + self.cold_water_temp

    def _heat_loss(self):
        # Calculate rate of heat loss through insulation
        area = math.pi * (self.volume / math.pi) ** (
                    2 / 3) * 2 * math.pi * self.insulation_thickness  # surface area of tank
        thermal_resistance = self.insulation_thickness / self.insulation_thermal_conductivity
        heat_flux = (self.temperature - self.tai) / thermal_resistance  # W/m^2
        return heat_flux * area

    def list_volume(self):
        water_mass = self.water_mass
        print(f'Amount of water is {(water_mass/1000)}L')

tank_1 = HotWaterStorageTank(300, 60, 0.25, 0.5, 20, 12)
tank_1.list_volume()
tank_1.remove_water(20)
tank_1._heat_loss()
tank_1.list_volume()
tank_1.add_water(20, 20)
tank_1.list_volume()