
def generate_solarpv_sizes(SolarPV_size, Simulations_Count, SolarPV_step_size):
    if Simulations_Count < 3:
        raise ValueError("Multi_Simulations should be at least 3 to have a valid list.")

    if SolarPV_step_size <= 0:
        raise ValueError("Step size must be greater than 0.")

    SolarPV_size_multi = [0] * Simulations_Count

    # Assign the third element to the SolarPV_size
    middle_index = Simulations_Count // 2
    SolarPV_size_multi[middle_index] = SolarPV_size

    # Fill in values before the middle index (going down by step_size)
    for i in range(middle_index - 1, -1, -1):
        SolarPV_size_multi[i] = max(SolarPV_size_multi[i + 1] - SolarPV_step_size, 0)

    # Fill in values after the middle index (going up by step_size)
    for i in range(middle_index + 1, Simulations_Count):
        SolarPV_size_multi[i] = SolarPV_size_multi[i - 1] + SolarPV_step_size

    return SolarPV_size_multi