import folium
import math
import json
import webbrowser
import os
import csv
import time
from datetime import datetime
from pathlib import Path
from geopy.geocoders import Nominatim
from folium.plugins import FastMarkerCluster
import plotly.offline as pyo
import plotly.graph_objects as go

# Initialize the geocoder.
geolocator = Nominatim(user_agent="energy_model_app")

def haversine(lon1, lat1, lon2, lat2):
    from math import radians, sin, cos, sqrt, atan2
    R = 6371
    dlon = radians(lon2 - lon1)
    dlat = radians(lat2 - lat1)
    a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    return R * c

# -------------------------------
# Load building basic info from Portfolio.csv.
portfolio_csv_path = os.path.join("Portfolio", "Portfolio.csv")
buildings = []
with open(portfolio_csv_path, "r", newline="", encoding="utf-8-sig") as f:
    reader = csv.DictReader(f)
    for row in reader:
        if "Name" not in row or not row["Name"].strip():
            continue
        building = {}
        building["name"] = row["Name"].strip()
        postcode = row["Postcode"].strip()
        try:
            location = geolocator.geocode(postcode)
            time.sleep(1)
        except Exception as e:
            location = None
        if location is not None:
            building["lat"] = location.latitude
            building["lon"] = location.longitude
        else:
            print(f"Warning: Could not geocode postcode {postcode} for building {building['name']}")
            continue
        building["area"] = float(row["GIA"])  # already in m²
        building["floors"] = int(row["Floors"])
        building["annual_elec"] = int(row["Annual Electricity Consumption"])
        building["annual_gas"] = int(row["Annual Gas Consumption"])
        building["epc"] = row["EPC Rating"].strip()

        # Define CSV file paths.
        hh_filename = os.path.join("Portfolio", f"{building['name']}_HH.csv")
        daily_filename = os.path.join("Portfolio", f"{building['name']}_daily.csv")
        monthly_filename = os.path.join("Portfolio", f"{building['name']}_monthly.csv")

        # Priority: HH > daily > monthly.
        if os.path.exists(hh_filename):
            building["source_frequency"] = "hh"
            hh_records = []
            with open(hh_filename, "r", newline="", encoding="utf-8-sig") as hf:
                hreader = csv.DictReader(hf)
                for hrow in hreader:
                    dt_str = hrow["Datetime"].strip()  # Expected: "dd/mm/yyyy hh:mm"
                    try:
                        dt_obj = datetime.strptime(dt_str, "%d/%m/%Y %H:%M")
                    except Exception as e:
                        continue
                    try:
                        elec = int(hrow["Electricity"])
                    except:
                        elec = None
                    try:
                        gas = int(hrow["Gas"])
                    except:
                        gas = None
                    hh_records.append({
                        "dt_str": dt_str,
                        "dt_obj": dt_obj,
                        "elec": elec,
                        "gas": gas
                    })
            building["hh_records"] = hh_records

            # Group HH data by month.
            hh_grouped = {}
            for rec in hh_records:
                month_key = rec["dt_obj"].strftime("%b %Y")
                if month_key not in hh_grouped:
                    hh_grouped[month_key] = {"labels": [], "hh_elec": [], "hh_gas": []}
                label = rec["dt_obj"].strftime("%d %H:%M")
                hh_grouped[month_key]["labels"].append(label)
                hh_grouped[month_key]["hh_elec"].append(rec["elec"])
                hh_grouped[month_key]["hh_gas"].append(rec["gas"])
            sorted_months = sorted(hh_grouped.keys(), key=lambda m: datetime.strptime(m, "%b %Y"))
            hh_grouped_list = []
            for m in sorted_months:
                hh_grouped_list.append({
                    "month": m,
                    "labels": hh_grouped[m]["labels"],
                    "hh_elec": hh_grouped[m]["hh_elec"],
                    "hh_gas": hh_grouped[m]["hh_gas"]
                })
            building["hh_grouped"] = hh_grouped_list

            # Aggregate HH data to daily.
            daily_agg = {}
            for rec in hh_records:
                day_key = rec["dt_obj"].strftime("%d/%m/%Y")
                if day_key not in daily_agg:
                    daily_agg[day_key] = {"elec": 0, "gas": 0}
                if rec["elec"] is not None:
                    daily_agg[day_key]["elec"] += rec["elec"]
                if rec["gas"] is not None:
                    daily_agg[day_key]["gas"] += rec["gas"]
            sorted_days = sorted(daily_agg.keys(), key=lambda d: datetime.strptime(d, "%d/%m/%Y"))
            daily_dates = []
            daily_elec = []
            daily_gas = []
            for d in sorted_days:
                daily_dates.append(d)
                daily_elec.append(daily_agg[d]["elec"])
                daily_gas.append(daily_agg[d]["gas"])
            building["aggregated_daily"] = {"dates": daily_dates, "elec": daily_elec, "gas": daily_gas}

            # Aggregate HH data to monthly.
            monthly_agg = {}
            for rec in hh_records:
                month_key = rec["dt_obj"].strftime("%b %Y")
                if month_key not in monthly_agg:
                    monthly_agg[month_key] = {"elec": 0, "gas": 0}
                if rec["elec"] is not None:
                    monthly_agg[month_key]["elec"] += rec["elec"]
                if rec["gas"] is not None:
                    monthly_agg[month_key]["gas"] += rec["gas"]
            sorted_months = sorted(monthly_agg.keys(), key=lambda m: datetime.strptime(m, "%b %Y"))
            monthly_labels = []
            monthly_elec = []
            monthly_gas = []
            for m in sorted_months:
                monthly_labels.append(m)
                monthly_elec.append(monthly_agg[m]["elec"])
                monthly_gas.append(monthly_agg[m]["gas"])
            building["aggregated_monthly"] = {"months": monthly_labels, "elec": monthly_elec, "gas": monthly_gas}

            # Fallback for gas: if all HH records have gas == None or the aggregated daily gas is zero while electricity exists.
            if all(rec["gas"] is None for rec in hh_records) or (sum(daily_gas) == 0 and sum(daily_elec) > 0):
                if os.path.exists(daily_filename):
                    daily_records_gas = []
                    with open(daily_filename, "r", newline="", encoding="utf-8-sig") as df:
                        dreader = csv.DictReader(df)
                        for drow in dreader:
                            date_str = drow["Date"].strip()
                            try:
                                dt_obj = datetime.strptime(date_str, "%d/%m/%Y")
                            except Exception as e:
                                continue
                            try:
                                gas_val = int(drow["Gas"])
                            except:
                                gas_val = None
                            daily_records_gas.append({
                                "date_str": date_str,
                                "dt_obj": dt_obj,
                                "gas": gas_val
                            })
                    daily_agg_gas = {}
                    for rec in daily_records_gas:
                        day_key = rec["dt_obj"].strftime("%d/%m/%Y")
                        if rec["gas"] is not None:
                            daily_agg_gas.setdefault(day_key, 0)
                            daily_agg_gas[day_key] += rec["gas"]
                    sorted_days_gas = sorted(daily_agg_gas.keys(), key=lambda d: datetime.strptime(d, "%d/%m/%Y"))
                    daily_gas_fallback = [daily_agg_gas[d] for d in sorted_days_gas]
                    building["aggregated_daily"]["gas"] = daily_gas_fallback

                    monthly_agg_gas = {}
                    for rec in daily_records_gas:
                        month_key = rec["dt_obj"].strftime("%b %Y")
                        if rec["gas"] is not None:
                            monthly_agg_gas.setdefault(month_key, 0)
                            monthly_agg_gas[month_key] += rec["gas"]
                    sorted_months_gas = sorted(monthly_agg_gas.keys(), key=lambda m: datetime.strptime(m, "%b %Y"))
                    monthly_gas_fallback = [monthly_agg_gas[m] for m in sorted_months_gas]
                    building["aggregated_monthly"]["gas"] = monthly_gas_fallback
                elif os.path.exists(monthly_filename):
                    monthly_data = {}
                    with open(monthly_filename, "r", newline="", encoding="utf-8-sig") as mf:
                        mreader = csv.DictReader(mf)
                        for mrow in mreader:
                            month = mrow["Month"].strip()
                            try:
                                gas_val = int(mrow["Gas"])
                            except:
                                gas_val = None
                            monthly_data[month] = gas_val
                    months_order = ["January", "February", "March", "April", "May", "June",
                                    "July", "August", "September", "October", "November", "December"]
                    monthly_gas_fallback = []
                    for m in months_order:
                        if m in monthly_data:
                            monthly_gas_fallback.append(monthly_data[m])
                    building["aggregated_monthly"]["gas"] = monthly_gas_fallback

        elif os.path.exists(daily_filename):
            building["source_frequency"] = "daily"
            daily_records = []
            with open(daily_filename, "r", newline="", encoding="utf-8-sig") as df:
                dreader = csv.DictReader(df)
                for drow in dreader:
                    date_str = drow["Date"].strip()
                    try:
                        dt_obj = datetime.strptime(date_str, "%d/%m/%Y")
                    except Exception as e:
                        continue
                    try:
                        elec = int(drow["Electricity"])
                    except:
                        elec = None
                    try:
                        gas = int(drow["Gas"])
                    except:
                        gas = None
                    daily_records.append({
                        "date_str": date_str,
                        "dt_obj": dt_obj,
                        "elec": elec,
                        "gas": gas
                    })
            building["daily_records"] = daily_records
            building["daily_dates"] = [r["date_str"] for r in daily_records]
            building["daily_elec"] = [r["elec"] for r in daily_records]
            building["daily_gas"] = [r["gas"] for r in daily_records]
            monthly_agg = {}
            for rec in daily_records:
                month_key = rec["dt_obj"].strftime("%b %Y")
                if month_key not in monthly_agg:
                    monthly_agg[month_key] = {"elec": 0, "gas": 0}
                if rec["elec"] is not None:
                    monthly_agg[month_key]["elec"] += rec["elec"]
                if rec["gas"] is not None:
                    monthly_agg[month_key]["gas"] += rec["gas"]
            sorted_months = sorted(monthly_agg.keys(), key=lambda m: datetime.strptime(m, "%b %Y"))
            monthly_labels = []
            monthly_elec = []
            monthly_gas = []
            for m in sorted_months:
                monthly_labels.append(m)
                monthly_elec.append(monthly_agg[m]["elec"])
                monthly_gas.append(monthly_agg[m]["gas"])
            building["aggregated_monthly"] = {"months": monthly_labels, "elec": monthly_elec, "gas": monthly_gas}
        elif os.path.exists(monthly_filename):
            building["source_frequency"] = "monthly"
            monthly_data = {}
            with open(monthly_filename, "r", newline="", encoding="utf-8-sig") as mf:
                mreader = csv.DictReader(mf)
                for mrow in mreader:
                    month = mrow["Month"].strip()
                    try:
                        elec = int(mrow["Electricity"])
                    except:
                        elec = None
                    try:
                        gas = int(mrow["Gas"])
                    except:
                        gas = None
                    monthly_data[month] = {"Electricity": elec, "Gas": gas}
            months_order = ["January", "February", "March", "April", "May", "June",
                            "July", "August", "September", "October", "November", "December"]
            monthly_labels = []
            monthly_elec = []
            monthly_gas = []
            for m in months_order:
                if m in monthly_data:
                    monthly_labels.append(m)
                    monthly_elec.append(monthly_data[m]["Electricity"])
                    monthly_gas.append(monthly_data[m]["Gas"])
            building["monthly_elec"] = monthly_elec
            building["monthly_gas"] = monthly_gas
            building["monthly_labels"] = monthly_labels
        else:
            building["source_frequency"] = None

        buildings.append(building)

# -------------------------------
# Create a Folium map centered over London.
if buildings:
    m = folium.Map(location=[buildings[0]["lat"], buildings[0]["lon"]], zoom_start=12)
else:
    m = folium.Map(location=[51.5074, -0.1278], zoom_start=12)
hq = buildings[0] if buildings else None

# Create a FastMarkerCluster.
marker_cluster = FastMarkerCluster(data=[]).add_to(m)

# -------------------------------
# For each building with HH data, generate a Plotly interactive chart HTML file.
for i, building in enumerate(buildings):
    b_id = f"b{i}"
    if building.get("source_frequency") == "hh":
        x_vals = [rec["dt_obj"].isoformat() for rec in building["hh_records"]]
        y_elec = [rec["elec"] for rec in building["hh_records"]]
        y_gas = [rec["gas"] for rec in building["hh_records"]]
        valid_elec = [v for v in y_elec if v is not None]
        valid_gas = [v for v in y_gas if v is not None]
        max_elec = max(valid_elec) if valid_elec else 0
        max_gas = max(valid_gas) if valid_gas else 0
        ymax = max(max_elec, max_gas) * 1.1 if max(max_elec, max_gas) > 0 else 10

        fig = go.Figure()
        fig.add_trace(go.Scatter(x=x_vals, y=y_elec, mode='lines', name='Electricity Consumption',
                                 line=dict(color='blue')))
        fig.add_trace(go.Scatter(x=x_vals, y=y_gas, mode='lines', name='Gas Consumption',
                                 line=dict(color='red')))
        fig.update_layout(title=f"Half Hourly Consumption for {building['name']}",
                          xaxis_title="Datetime",
                          yaxis_title="Consumption",
                          yaxis=dict(range=[0, ymax]))
        # Save the Plotly chart in the Portfolio folder.
        plot_filename = os.path.join("Portfolio", f"half_hourly_{b_id}.html")
        pyo.plot(fig, filename=plot_filename, auto_open=False)
        # Store the relative path with forward slashes.
        rel_path = os.path.join("Portfolio", f"half_hourly_{b_id}.html").replace("\\", "/")
        building["hh_plot_filename"] = rel_path

# -------------------------------
# Build popups for each building.
for i, building in enumerate(buildings):
    b_id = f"b{i}"
    area_m2 = building["area"]
    area_formatted = f"{area_m2:,.0f}"
    floors_formatted = f"{building['floors']:,}"
    annual_elec_formatted = f"{building['annual_elec']:,}"
    annual_gas_formatted = f"{building['annual_gas']:,}"

    if building.get("source_frequency") == "hh":
        hh_grouped_js = json.dumps(building["hh_grouped"])
        daily_labels_js = json.dumps(building["aggregated_daily"]["dates"])
        daily_elec_js = json.dumps(building["aggregated_daily"]["elec"])
        daily_gas_js = json.dumps(building["aggregated_daily"]["gas"])
        monthly_labels_js = json.dumps(building["aggregated_monthly"]["months"])
        monthly_elec_js = json.dumps(building["aggregated_monthly"]["elec"])
        monthly_gas_js = json.dumps(building["aggregated_monthly"]["gas"])
        available_buttons = {"hh": True, "daily": True, "monthly": True}
    elif building.get("source_frequency") == "daily":
        daily_labels_js = json.dumps(building["daily_dates"])
        daily_elec_js = json.dumps(building["daily_elec"])
        daily_gas_js = json.dumps(building["daily_gas"])
        monthly_labels_js = json.dumps(building["aggregated_monthly"]["months"])
        monthly_elec_js = json.dumps(building["aggregated_monthly"]["elec"])
        monthly_gas_js = json.dumps(building["aggregated_monthly"]["gas"])
        available_buttons = {"hh": False, "daily": True, "monthly": True}
    elif building.get("source_frequency") == "monthly":
        monthly_labels_js = json.dumps(building["monthly_labels"])
        monthly_elec_js = json.dumps(building["monthly_elec"])
        monthly_gas_js = json.dumps(building["monthly_gas"])
        available_buttons = {"hh": False, "daily": False, "monthly": True}
    else:
        available_buttons = {"hh": False, "daily": False, "monthly": False}

    hh_button_html = ""
    daily_button_html = ""
    monthly_button_html = ""
    daily_detail_div = ""
    monthly_detail_div = ""

    if available_buttons.get("hh"):
        hh_button_html = f'<button onclick="window.open(\'{building["hh_plot_filename"]}\', \'_blank\')">View Half Hourly Consumption</button>'
    if available_buttons.get("daily"):
        daily_button_html = f'<button onclick="showDetail(\'{b_id}\', \'daily\')">View Daily Consumption</button>'
        daily_detail_div = f"""
    <div id="detail-content-{b_id}-daily" style="display:none; border:1px solid #ccc; border-radius:5px;
         background-color:#e6f7ff; padding:10px; font-family:'Century Gothic', sans-serif;">
      <h4 style="font-size:20px; text-decoration:underline; margin-bottom:10px;">Daily Consumption for {building['name']}</h4>
      <div style="width:400px; height:300px;">
        <canvas id="chart-{b_id}-daily" width="400" height="300"></canvas>
      </div>
      <br>
      <button onclick="hideDetail('{b_id}', 'daily')">Back</button>
    </div>
    """
    if available_buttons.get("monthly"):
        monthly_button_html = f'<button onclick="showDetail(\'{b_id}\', \'monthly\')">View Monthly Consumption</button>'
        monthly_detail_div = f"""
    <div id="detail-content-{b_id}-monthly" style="display:none; border:1px solid #ccc; border-radius:5px;
         background-color:#e6f7ff; padding:10px; font-family:'Century Gothic', sans-serif;">
      <h4 style="font-size:20px; text-decoration:underline; margin-bottom:10px;">Monthly Consumption for {building['name']}</h4>
      <div style="width:400px; height:300px;">
        <canvas id="chart-{b_id}-monthly" width="400" height="300"></canvas>
      </div>
      <br>
      <button onclick="hideDetail('{b_id}', 'monthly')">Back</button>
    </div>
    """

    consumption_buttons = " ".join([btn for btn in [hh_button_html, daily_button_html, monthly_button_html] if btn])

    popup_html = f"""
    <!-- Load Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      console.log("Chart.js loaded:", Chart);
    </script>
    <div id="main-content-{b_id}" style="border:1px solid #ccc; border-radius:5px;
         background-color:#e6f7ff; padding:10px; font-family:'Century Gothic', sans-serif;">
      <h4 style="font-size:20px; text-decoration:underline; margin-bottom:10px;">{building['name']}</h4>
      <p><b>Gross Internal Area:</b> {area_formatted} m<sup>2</sup></p>
      <p><b>Number of Floors:</b> {floors_formatted}</p>
      <p><b>Annual Electricity Consumption:</b> {annual_elec_formatted} kWh</p>
      <p><b>Annual Gas Consumption:</b> {annual_gas_formatted} kWh</p>
      <p><b>EPC Rating:</b> {building['epc']}</p>
      {consumption_buttons}
    </div>
    {daily_detail_div}
    {monthly_detail_div}
    <script>
    function showDetail(b_id, freq) {{
        document.getElementById("main-content-" + b_id).style.display = "none";
        document.getElementById("detail-content-" + b_id + "-" + freq).style.display = "block";
        if(!window["chartInitialized_" + b_id + "_" + freq]) {{
            var ctx = document.getElementById("chart-" + b_id + "-" + freq).getContext('2d');
            var chart;
            if(freq === "daily") {{
                chart = new Chart(ctx, {{
                    type: 'line',
                    data: {{
                        labels: {daily_labels_js},
                        datasets: [
                            {{
                                label: 'Electricity Consumption',
                                data: {daily_elec_js},
                                fill: false,
                                borderColor: 'blue',
                                pointRadius: 0
                            }},
                            {{
                                label: 'Gas Consumption',
                                data: {daily_gas_js},
                                fill: false,
                                borderColor: 'red',
                                pointRadius: 0
                            }}
                        ]
                    }},
                    options: {{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {{
                            x: {{
                                ticks: {{
                                    autoSkip: true,
                                    maxTicksLimit: 10,
                                    callback: function(value, index, values) {{
                                        var parts = value.split('/');
                                        if(parts.length === 3 && parts[0] === "01") {{
                                            var monthMap = {{
                                                "01": "January", "02": "February", "03": "March", "04": "April",
                                                "05": "May", "06": "June", "07": "July", "08": "August",
                                                "09": "September", "10": "October", "11": "November", "12": "December"
                                            }};
                                            return monthMap[parts[1]];
                                        }} else {{
                                            return "";
                                        }}
                                    }}
                                }}
                            }},
                            y: {{
                                beginAtZero: true
                            }}
                        }},
                        plugins: {{
                            tooltip: {{
                                callbacks: {{
                                    label: function(context) {{
                                        var label = context.dataset.label || '';
                                        if(label) {{
                                            label += ': ';
                                        }}
                                        if(context.parsed.y !== null) {{
                                            label += context.parsed.y.toLocaleString();
                                        }}
                                        return label;
                                    }}
                                }}
                            }}
                        }}
                    }}
                }});
            }} else if(freq === "monthly") {{
                chart = new Chart(ctx, {{
                    type: 'line',
                    data: {{
                        labels: {monthly_labels_js},
                        datasets: [
                            {{
                                label: 'Electricity Consumption',
                                data: {monthly_elec_js},
                                fill: false,
                                borderColor: 'blue'
                            }},
                            {{
                                label: 'Gas Consumption',
                                data: {monthly_gas_js},
                                fill: false,
                                borderColor: 'red'
                            }}
                        ]
                    }},
                    options: {{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {{
                            x: {{
                                ticks: {{ autoSkip: false }}
                            }},
                            y: {{
                                beginAtZero: true
                            }}
                        }},
                        plugins: {{
                            tooltip: {{
                                callbacks: {{
                                    label: function(context) {{
                                        var label = context.dataset.label || '';
                                        if(label) {{
                                            label += ': ';
                                        }}
                                        if(context.parsed.y !== null) {{
                                            label += context.parsed.y.toLocaleString();
                                        }}
                                        return label;
                                    }}
                                }}
                            }}
                        }}
                    }}
                }});
            }}
            window["chartInitialized_" + b_id + "_" + freq] = true;
            window["chart_" + b_id + "_" + freq] = chart;
        }} else {{
            window["chart_" + b_id + "_" + freq].resize();
        }}
    }}
    function hideDetail(b_id, freq) {{
        document.getElementById("detail-content-" + b_id + "-" + freq).style.display = "none";
        document.getElementById("main-content-" + b_id).style.display = "block";
    }}
    </script>
    """
    iframe = folium.IFrame(html=popup_html, width=450, height=600)
    popup = folium.Popup(iframe, max_width=450)
    marker = folium.Marker(
        location=[building["lat"], building["lon"]],
        popup=popup,
        tooltip=building["name"],
        icon=folium.Icon(color="red" if i == 0 else "blue", icon="info-sign")
    )
    marker_cluster.add_child(marker)
    if i != 0 and hq is not None:
        distance = haversine(hq["lon"], hq["lat"], building["lon"], building["lat"])
        folium.PolyLine(
            locations=[[hq["lat"], hq["lon"]], [building["lat"], building["lon"]]],
            tooltip=f"Distance: {distance:,.2f} km",
            color="green"
        ).add_to(m)

m.save("london_buildings_portfolio.html")
print("Map has been saved to london_buildings_portfolio.html")
webbrowser.open("london_buildings_portfolio.html")
