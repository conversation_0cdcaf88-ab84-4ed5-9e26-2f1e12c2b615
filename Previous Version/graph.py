# Update plot parameters
from matplotlib import rcParams

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

df = pd.read_csv(
    f"Annual Data.csv"#,
    # parse_dates=["date"],
    # index_col=["date"]
)

df.head()

rcParams['font.family'] = "serif"
rcParams['font.style'] = "oblique"

# Specify more colours
color_gnome_orange_3 = "#ff7800"
color_gnome_red_5 = "#a51d2d"

fig, ax = plt.subplots(
    figsize=(10,6),
    facecolor="white"
)
ax.set_title(
    "Energy Consumption and Generation",
    fontsize=16
)
ax.plot(
    df["Electricity Usage"],
    color=color_gnome_orange_3
)
ax.plot(
    df["Electricity_Usage_Scen_B"],
    color=color_gnome_red_5
)
ax.fill_between(
    x=df.index,
    y1=df["Electricity Usage"],
    y2=df["Electricity_Usage_Scen_B"],
    where=df["Electricity Usage"]>df["Electricity_Usage_Scen_B"],
    color="#ed333b",  # GNOME red 2
    alpha=0.2,
    interpolate=True
)
ax.fill_between(
    x=df.index,
    y1=df["Electricity Usage"],
    y2=df["Electricity_Usage_Scen_B"],
    where=df["Electricity Usage"]<df["Electricity_Usage_Scen_B"],
    color="#f8e45c",  # GNOME yellow 2
    alpha=0.2,
    interpolate=True
)

# Set axis labels
ax.set_xlabel(
    "Day of the Year",
    fontsize=14
)

# Set axis limits
#ax.set_ylim(0, 25)
ax.set_xlim(0, 366)

# Set axis limits
# ax.set_ylim(0, 200)
# ax.set_xlim(
#     pd.Timestamp(
#         day=1,
#         month=1,
#         year=2023
#         ),
#     pd.Timestamp(
#         day=31,
#         month=12,
#         year=2023,
#         )
# )

# Set "y" tick marks
#ax.set_yticks(list(range(0, 25, 1)[1:-1]))
#ax.set_yticklabels([f"100,00" if y==100 else f"{y}" for y in range(0, 25, 1)[1:-1]])

# Move "y" axis labels and ticks to the right
ax.yaxis.tick_right()

# Add grid lines
ax.grid(
    color="#c0bfbc"  # GNOME light 4
)

# Annotations
ax.text(
    x=50,
    y=11,
    s="Baseline",
    color=color_gnome_orange_3,
    rotation=-3
)
ax.text(
    x=50,
    y=-2.5,
    s="Scenario 1",
    color=color_gnome_red_5,
    rotation=-16
)
ax.text(
    x=150,
    y=13,
    s="Daily Electricity (kWh)",
    color="black",
    fontsize=12,
    weight="bold"
)
# ax.text(
#     x=pd.Timestamp(
#         year=1765,
#         month=10,
#         day=1
#     ),
#     y=114,
#     s=f"Generation", # BALANCE in\n FAVOUR of\n ENGLAND
#     color="black",
#     fontsize=12,
#     weight="bold"
# )

plt.tight_layout()

plt.savefig(
    fname="Electricity Consumption & Generation.png",
    format="png",
    dpi=125
);

plt.show()