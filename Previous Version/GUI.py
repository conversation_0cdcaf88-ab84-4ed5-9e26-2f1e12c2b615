import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk

bg_colour = "#3d6466"

def validate_input(content):
    if content.isdigit():
        value = int(content)
        if 4 <= value <= 10:
            return True
        else:
            messagebox.showerror("Invalid input", "Value must be between 4 and 10.")
            return False
    elif content == "":
        return True  # Allow empty input to clear the box
    else:
        messagebox.showerror("Invalid input", "Please enter a number.")
        return False

def get_entry_values(entries, variables):
    try:
        for entry, value_variable in zip(entries, variables):
            value = entry.get()
            if value.isdigit() and 4 <= int(value) <= 10:
                value_variable.set(int(value))
            else:
                messagebox.showerror("Invalid input", "Please enter a valid number between 4 and 10.")
                return
        messagebox.showinfo("Success", "All values entered successfully!")
    except Exception as e:
        messagebox.showerror("Error", str(e))

def use_user_value(value_variable):
    value = value_variable.get()
    messagebox.showinfo("User Value", f"The stored user value is: {value}")

def clear_widgets(frame):
    for widget in frame.winfo_children():
        widget.destroy()

def load_frame1():
    clear_widgets(frame2)  # destroy other frames
    frame1.tkraise()  # place at each frame to stack
    frame1.pack_propagate(False)

    # Frame 1 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/J&H Logo (circular).png", logo_width, logo_height)
    logo_widget = tk.Label(frame1, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.pack()

    tk.Label(frame1,
             text="Enter your energy",
             bg=bg_colour,
             fg="white",
             font=("TKMenuFont", 14)
             ).pack()

    # button widget
    tk.Button(
        frame1,
        text="Browse Energy Systems",
        font=("TKHeadingFont", 20),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=load_frame2
    ).pack(pady=20)  # pady adds gaps between bits

    # Button to use the first entered value
    tk.Button(
        frame1,
        text="Use First Value",
        font=("TKHeadingFont", 20),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=lambda: use_user_value(user_values[0])
    ).pack(pady=20)  # pady adds gaps between bits

    # Button to use the second entered value
    tk.Button(
        frame1,
        text="Use Second Value",
        font=("TKHeadingFont", 20),
        bg="#28393a",
        fg="white",
        cursor="hand2",
        activebackground="#badee2",
        activeforeground="black",
        command=lambda: use_user_value(user_values[1])
    ).pack(pady=20)  # pady adds gaps between bits

def load_frame2():
    clear_widgets(frame1)  # destroy other frames
    frame2.tkraise()  # place at each frame to stack

    # Frame 2 widgets
    # Set the desired logo size
    logo_width = 200
    logo_height = 200
    logo_img = resize_image("RandomRecipePicker-main/starter_files/assets/solar_panels.jpg", logo_width, logo_height)
    logo_widget = tk.Label(frame2, image=logo_img, bg=bg_colour)
    logo_widget.image = logo_img  # Tkinter forces this
    logo_widget.pack(pady=20)

    tk.Label(frame2,
             text="Solar Panels",  # adjust later
             bg=bg_colour,
             fg="white",
             font=("TKHeadingFont", 20)
             ).pack(pady=25)

    # Entry widgets with validation for multiple values
    for i in range(2):  # Change the range for more entry widgets
        vcmd = (root.register(validate_input), '%P')
        entry = tk.Entry(frame2, validate='key', validatecommand=vcmd)
        entry.pack(pady=10)  # Add some padding
        entries.append(entry)
        user_values.append(tk.IntVar())

    # Single button to get all entry values
    submit_button = tk.Button(frame2, text="Submit All Values", command=lambda: get_entry_values(entries, user_values))
    submit_button.pack(pady=20)

    # Button to go back to the first frame
    back_button = tk.Button(frame2, text="BACK", font=("TKHeadingFont", 18), bg="#28393a", fg="white", cursor="hand2",
                            activebackground="#badee2", activeforeground="black", command=load_frame1)
    back_button.pack(pady=20)  # pady adds gaps between bits

def resize_image(image_path, width, height):
    image = Image.open(image_path)
    resized_image = image.resize((width, height), Image.LANCZOS)
    return ImageTk.PhotoImage(resized_image)

# Initialize app
root = tk.Tk()
root.title("Energy Model")  # Title

root.eval("tk::PlaceWindow . center")  # Centers on screen

frame1 = tk.Frame(root, width=500, height=600, bg=bg_colour)  # Window size and background teal
frame2 = tk.Frame(root, bg=bg_colour)  # size here isn't limited by removing size

for frame in (frame1, frame2):
    frame.grid(row=0, column=0, sticky="nesw")  # Default values, define anyway. sticky stops white corners

# Lists to store entry widgets and their corresponding IntVar variables
entries = []
user_values = []

load_frame1()

# Run app
root.mainloop()
