from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.pyplot as plt
import seaborn as snsc
import pandas as pd

# Load the CSV file
file_path = 'H:\\My Documents\\Ian\\Python\\Energy Model\\BEMSS\\data\\certificates.csv'
df = pd.read_csv(file_path)

# Convert relevant columns to datetime
df['INSPECTION_DATE'] = pd.to_datetime(df['INSPECTION_DATE'], errors='coerce')
df['LODGEMENT_DATETIME'] = pd.to_datetime(df['LODGEMENT_DATETIME'], errors='coerce')

# Calculate expiration dates (10 years after the inspection date)
df['EXPIRATION_DATE'] = df['INSPECTION_DATE'] + pd.DateOffset(years=10)

# Check for expired EPCs
current_date = pd.Timestamp.now()
expired_epcs = df[df['EXPIRATION_DATE'] < current_date]

# Save the list of expired EPCs to a CSV file for further review
expired_epcs.to_csv('data\\expired_epcs_list.csv', index=False)

# Convert LODGEMENT_DATETIME to a monthly string format for plotting
df['LODGEMENT_MONTH'] = df['LODGEMENT_DATETIME'].dt.to_period('M').astype(str)

# Get the correct LOCAL_AUTHORITY_LABEL for the title
local_authority = df['LOCAL_AUTHORITY_LABEL'].iloc[0] if not df['LOCAL_AUTHORITY_LABEL'].isna().all() else "Unknown Authority"

# Specify the path for the PDF file
pdf_path = 'H:\\My Documents\\Ian\\Python\\Energy Model\\BEMSS\\data\\EPC_charts.pdf'

# Create a color map for the ASSET_RATING_BAND chart
asset_rating_order = ['A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G']
colors = {'A+': '#007f3f', 'A': '#00cc44', 'B': '#99e600', 'C': '#ffff00',
          'D': '#ff9900', 'E': '#ffcc00', 'F': '#ff3300', 'G': '#990000'}

# Create a PDF document
with PdfPages(pdf_path) as pdf:
    # Function to add a title and paragraph
    def add_title_and_paragraph(title, paragraph):
        plt.figure(figsize=(10, 1))
        plt.text(0.5, 0.5, title, ha='center', va='center', fontsize=14, weight='bold')
        plt.axis('off')
        pdf.savefig()
        plt.close()

        plt.figure(figsize=(10, 2))
        plt.text(0, 1, paragraph, fontsize=10, wrap=True)
        plt.axis('off')
        pdf.savefig()
        plt.close()


    # Chart 1: LODGEMENT_DATE
    add_title_and_paragraph(
        title=f"{local_authority} - Lodgement Date Distribution",
        paragraph="This chart shows the number of Energy Performance Certificates (EPCs) lodged each month. "
                  "It helps identify trends in certificate lodgement over time."
    )

    plt.figure(figsize=(12, 6))
    sns.countplot(x='LODGEMENT_MONTH', data=df, order=sorted(df['LODGEMENT_MONTH'].dropna().unique()))
    plt.title('Bin Chart of LODGEMENT_DATE')
    plt.xlabel('Lodgement Month')
    plt.ylabel('Number of Certificates')
    plt.xticks(rotation=45, ha='right')

    ticks_to_display = df['LODGEMENT_MONTH'].dropna().unique()[::6]
    plt.xticks(ticks_to_display, rotation=45, ha='right')
    plt.tight_layout()
    pdf.savefig()
    plt.close()

    # Chart 2: PROPERTY_TYPE
    add_title_and_paragraph(
        title=f"{local_authority} - Property Type Distribution",
        paragraph="This chart shows the distribution of different property types within the dataset. "
                  "It indicates the types of properties for which EPCs were issued."
    )

    plt.figure(figsize=(10, 6))
    sns.countplot(y='PROPERTY_TYPE', data=df, order=df['PROPERTY_TYPE'].value_counts().index)
    plt.title('Bin Chart of PROPERTY_TYPE')
    plt.xlabel('Number of Certificates')
    plt.ylabel('Property Type')
    plt.tight_layout()
    pdf.savefig()
    plt.close()

    # Chart 3: ASSET_RATING_BAND
    add_title_and_paragraph(
        title=f"{local_authority} - Asset Rating Band Distribution",
        paragraph="This chart shows the distribution of EPCs across different asset rating bands, ranging from A+ to G. "
                  "The colors match the official EPC rating colors, making it easy to identify the energy efficiency ratings."
    )

    plt.figure(figsize=(10, 6))
    ax = sns.countplot(x='ASSET_RATING_BAND', data=df, order=asset_rating_order, hue='ASSET_RATING_BAND',
                       palette=colors, dodge=False)
    plt.title('Bin Chart of ASSET_RATING_BAND')
    plt.xlabel('Asset Rating Band')
    plt.ylabel('Number of Certificates')
    plt.legend([], [], frameon=False)
    plt.tight_layout()
    pdf.savefig()
    plt.close()

    # Chart 4: BUILDING_EMISSIONS
    add_title_and_paragraph(
        title=f"{local_authority} - Building Emissions Distribution",
        paragraph="This chart shows the distribution of building emissions (primary energy value) based on the EPC data. "
                  "It provides an insight into the energy efficiency of the buildings surveyed."
    )

    plt.figure(figsize=(10, 6))
    sns.histplot(df['PRIMARY_ENERGY_VALUE'].dropna(), bins=30, kde=False)
    plt.title('Bin Chart of BUILDING_EMISSIONS (Primary Energy Value)')
    plt.xlabel('Building Emissions (Primary Energy Value)')
    plt.ylabel('Frequency')
    plt.tight_layout()
    pdf.savefig()
    plt.close()

print(f"The charts, along with the titles and descriptions, have been saved as a PDF document at '{pdf_path}'.")