"""
==========================
UK NGESO CEF import to CSV
==========================

Module description
------------------
Imports UK National Grid electricity carbon intensity data from the NG live dataset to
CSV format.

Only whole year data should be imported; years 2018 onwards are available.
This script downloads everything, so includes 2020 leap year (consider adding a condition to skip month 2, day 29)

Splits the columns into datetime, but keeps original data for reference

Useful columns would be:
B = forecast (grams)
C = actual (grams)
F = day
G = month
H = year
I = hour
J = month
for reference, column headers are: datetime, forecast, actual, index, Date, Day, Month, Year, Hour, Minute

See https://data.nationalgrideso.com/carbon-intensity1/national-carbon-intensity-forecast

"""

import pandas as pd
import requests
from io import StringIO

url = 'https://data.nationalgrideso.com/backend/dataset/f406810a-1a36-48d2-b542-1dfb1348096e/resource/0e5fde43-2de7-4fb4-833d-c7bca3b658b0/download/gb_carbon_intensity.csv'

response = requests.get(url)

# Load CSV data into a pandas DataFrame
df = pd.read_csv(StringIO(response.content.decode('utf-8')))

# Split datetime column into separate date and time columns
df['Date'] = pd.to_datetime(df['datetime'], format='%Y-%m-%dT%H:%M:%S')
df['Day'] = df['Date'].dt.day
df['Month'] = df['Date'].dt.month
df['Year'] = df['Date'].dt.year
df['Hour'] = df['Date'].dt.hour
df['Minute'] = df['Date'].dt.minute

# Save updated data to a new CSV file
df.to_csv('updated_gb_carbon_intensity.csv', index=False)

print('CSV data downloaded and updated, and saved to updated_gb_carbon_intensity.csv')
