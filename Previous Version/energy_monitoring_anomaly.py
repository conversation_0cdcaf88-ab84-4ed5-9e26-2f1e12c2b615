import os
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoModelForCausalLM, AutoTokenizer
from sklearn.ensemble import IsolationForest  # CPU-based version
from sklearn.preprocessing import StandardScaler
from sklearn.compose import ColumnTransformer
import joblib
import subprocess  # To automate Hugging Face login

# Define the CSV file
CSV_FILE = "HH_data.csv"  # Ensure this file exists in the same directory as the script

# Define local model directory
MODEL_DIR = r"E:\local_llm_model"


# Ensure Hugging Face login to prevent slow downloads
def ensure_huggingface_login():
    """Ensures user is logged into Hugging Face to prevent slow model downloads."""
    try:
        subprocess.run(["huggingface-cli", "whoami"], check=True, stdout=subprocess.DEVNULL)
        print("✅ Hugging Face login detected.")
    except subprocess.CalledProcessError:
        print("🔐 You need to log in to Hugging Face to speed up downloads.")
        subprocess.run(["huggingface-cli", "login"], check=True)


# Load Phi-2 model and cache it locally
def load_phi2_model(model_name="microsoft/phi-2"):
    """Loads Phi-2 model from local cache or downloads it once and stores locally."""
    if not os.path.exists(MODEL_DIR):
        os.makedirs(MODEL_DIR)

    # First, check if the model is already saved locally
    if os.path.exists(os.path.join(MODEL_DIR, "config.json")):
        print("✅ Loading Phi-2 from local cache.")
        tokenizer = AutoTokenizer.from_pretrained(MODEL_DIR)
        model = AutoModelForCausalLM.from_pretrained(MODEL_DIR)
    else:
        print("⏳ Downloading Phi-2 for the first time. This may take a while...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=MODEL_DIR)
        model = AutoModelForCausalLM.from_pretrained(model_name, cache_dir=MODEL_DIR)

        # Save locally to avoid future downloads
        tokenizer.save_pretrained(MODEL_DIR)
        model.save_pretrained(MODEL_DIR)
        print(f"✅ Model saved to {MODEL_DIR} for future use.")

    return model, tokenizer


# Detect format and process data accordingly
def preprocess_data(csv_file):
    """Loads and processes energy data, handling different layouts."""
    df = pd.read_csv(csv_file)

    first_col = df.columns[0]
    df[first_col] = pd.to_datetime(df[first_col], dayfirst=True)  # Ensure proper date format

    # If data is already in long format (has a timestamp column)
    if "Timestamp" in df.columns and any(col.endswith("_kWh") for col in df.columns):
        print("✅ Detected long format (Timestamp-based).")
        return handle_missing_values(df)

    # Otherwise, assume it's in wide format with half-hourly columns
    df = convert_wide_to_long(df)

    return handle_missing_values(df)


# Convert wide format (one row per day, 48 columns) to long format
def convert_wide_to_long(df):
    """Converts wide format (one row per day, 48 columns) to long format."""

    df = df.rename(columns={df.columns[0]: "Date"})  # Rename first column to "Date"

    # Identify half-hourly time columns (hh:mm format)
    time_cols = [col for col in df.columns if ":" in col]

    if not time_cols:
        raise ValueError("❌ No half-hourly time columns detected in wide format.")

    print("🔄 Converting wide format (one row per day, 48 half-hourly columns) to long format.")

    df_long = df.melt(id_vars=["Date"], value_vars=time_cols, var_name="Time", value_name="Electricity_kWh")

    # Convert to proper datetime
    df_long["Timestamp"] = pd.to_datetime(df_long["Date"].astype(str) + " " + df_long["Time"])
    df_long = df_long.drop(columns=["Date", "Time"])  # Drop original columns

    return df_long


# Handle missing values
def handle_missing_values(df):
    """Fills or interpolates missing values before ML processing."""
    if df.isnull().sum().sum() > 0:
        print("⚠️ Missing values detected. Filling missing data...")
        df = df.interpolate().fillna(method="bfill")
    return df


# Detect anomalies using CPU-based Isolation Forest
def detect_anomalies(df):
    """Uses CPU-based Isolation Forest (sklearn) to detect anomalies."""

    df['Hour'] = df['Timestamp'].dt.hour
    df['DayOfWeek'] = df['Timestamp'].dt.dayofweek

    features = ['Electricity_kWh', 'Hour', 'DayOfWeek']

    preprocessor = ColumnTransformer(
        transformers=[('num', StandardScaler(), features)]
    ).set_output(transform="pandas")

    X_scaled = preprocessor.fit_transform(df)

    # Use CPU-based Isolation Forest
    model = IsolationForest(n_estimators=250, contamination=0.005, random_state=42)
    df['Anomaly'] = model.fit_predict(X_scaled)

    return df, model, preprocessor


# Save the trained model
def save_model(model, preprocessor, filename="isolation_forest_model.pkl"):
    """Saves the trained Isolation Forest model and preprocessor to a file."""
    joblib.dump({"model": model, "preprocessor": preprocessor}, filename)
    print(f"✅ Model and preprocessor saved as {filename}")


# Generate explanations for anomalies using Phi-2
def explain_anomalies(anomalies, model, tokenizer):
    """Generates natural language explanations for detected anomalies using Phi-2."""
    explanations = []
    for _, row in anomalies.iterrows():
        prompt = f"""Energy Consumption Analysis:

        - **Timestamp**: {row['Timestamp']}
        - **Electricity Usage**: {row['Electricity_kWh']} kWh
        - **Time of Day**: {row['Hour']}:00
        - **Day of Week**: {row['DayOfWeek']} (0=Monday, 6=Sunday)

        This energy usage has been detected as an anomaly. Explain why this could be happening.
        """

        inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
        outputs = model.generate(**inputs, max_new_tokens=100)
        explanation = tokenizer.decode(outputs[0], skip_special_tokens=True)

        explanations.append((row['Timestamp'], explanation))

    return explanations


# Main script execution
if __name__ == "__main__":
    # Ensure Hugging Face login
    ensure_huggingface_login()

    # Load Phi-2 model (cached to avoid slow loading)
    phi2_model, tokenizer = load_phi2_model()

    # Load and preprocess data
    df = preprocess_data(CSV_FILE)

    # Train/test split (80% training, 20% testing)
    split_index = int(0.8 * len(df))
    train_data = df.iloc[:split_index]
    test_data = df.iloc[split_index:]

    # Train the anomaly detection model on CPU
    df_with_anomalies, trained_model, preprocessor = detect_anomalies(df)

    # Save the trained model for future use
    save_model(trained_model, preprocessor)

    # Identify detected anomalies
    anomalies = df_with_anomalies[df_with_anomalies['Anomaly'] == -1]
    print(f"🚨 Detected {len(anomalies)} anomalies")

    # Plot anomalies
    plt.figure(figsize=(12, 6))
    plt.plot(df_with_anomalies['Timestamp'], df_with_anomalies['Electricity_kWh'], label="Electricity")
    plt.scatter(anomalies['Timestamp'], anomalies['Electricity_kWh'], color='red', label="Anomalies")
    plt.legend()
    plt.title("Anomaly Detection in Energy Consumption")
    plt.show()
