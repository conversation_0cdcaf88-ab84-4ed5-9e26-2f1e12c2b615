import os
import time
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoModelForCausalLM, AutoTokenizer
from sklearn.ensemble import IsolationForest  # CPU-based version
from sklearn.preprocessing import StandardScaler
from sklearn.compose import ColumnTransformer
import joblib
import subprocess  # To automate Hugging Face login
from datetime import datetime
import plotly.express as px

# ----- Configuration -----
TEST_MODE = True  # When True, only the first anomaly gets an LLM explanation (for testing)
CSV_FILE = "HH_data_small.csv"  # Input CSV file
MODEL_DIR = r"N:\local_llm_model"  # Local directory for caching the LLM model
MODEL_NAME_FILE = os.path.join(MODEL_DIR, "model_name.txt")  # To store the downloaded model name
ANOMALY_DIR = r"E:\Python\Energy Model\BEMMS\Anomaly_Detection"  # Directory for saving anomaly outputs
model_name = "microsoft/phi-3.5-mini-instruct"

# Ensure the anomaly output directory exists
if not os.path.exists(ANOMALY_DIR):
    os.makedirs(ANOMALY_DIR)

# ----- New Function: Check CUDA -----
def check_cuda():
    """Checks if CUDA is available and prints the GPU device name if so."""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"✅ CUDA is available. Running on GPU: {gpu_name}")
    else:
        print("❌ CUDA is not available. Running on CPU.")

# ----- Helper Functions for Model & Data Processing -----
def ensure_huggingface_login():
    """Ensure user is logged in to Hugging Face."""
    try:
        subprocess.run(["huggingface-cli", "whoami"], check=True, stdout=subprocess.DEVNULL)
        print("✅ Hugging Face login detected.")
    except subprocess.CalledProcessError:
        print("🔐 Please log in to Hugging Face to speed up downloads.")
        subprocess.run(["huggingface-cli", "login"], check=True)

def load_model(model_name=model_name):
    """
    Loads the specified model from local cache if it matches the given model_name.
    Otherwise, downloads the specified model and updates the cached model information.
    The model is moved to GPU if available.
    """
    if not os.path.exists(MODEL_DIR):
        os.makedirs(MODEL_DIR)
    use_cached = False
    if os.path.exists(MODEL_NAME_FILE) and os.path.exists(os.path.join(MODEL_DIR, "config.json")):
        with open(MODEL_NAME_FILE, "r") as f:
            cached_model_name = f.read().strip()
        if cached_model_name == model_name:
            use_cached = True
            print("✅ Loading specified model from local cache.")
        else:
            print("⚠️ Cached model does not match the specified model. Downloading the correct model.")
    else:
        print("No cached model found. Downloading the specified model.")
    if use_cached:
        tokenizer = AutoTokenizer.from_pretrained(MODEL_DIR)
        model = AutoModelForCausalLM.from_pretrained(MODEL_DIR)
    else:
        print(f"⏳ Downloading {model_name} for the first time. This may take a while...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=MODEL_DIR)
        model = AutoModelForCausalLM.from_pretrained(model_name, cache_dir=MODEL_DIR)
        tokenizer.save_pretrained(MODEL_DIR)
        model.save_pretrained(MODEL_DIR)
        with open(MODEL_NAME_FILE, "w") as f:
            f.write(model_name)
        print(f"✅ Model saved to {MODEL_DIR} for future use.")
    # Ensure pad token is set
    if tokenizer.pad_token is None:
        if tokenizer.eos_token is not None:
            tokenizer.pad_token = tokenizer.eos_token
        else:
            tokenizer.add_special_tokens({'pad_token': '[PAD]'})
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    return model, tokenizer

def preprocess_data(csv_file):
    """Loads CSV data and handles both long and wide formats."""
    df = pd.read_csv(csv_file)
    first_col = df.columns[0]
    df[first_col] = pd.to_datetime(df[first_col], dayfirst=True)
    # Check if already in long format (has "Timestamp" and a consumption column ending with _kWh)
    if "Timestamp" in df.columns and any(col.endswith("_kWh") for col in df.columns):
        print("✅ Detected long format (Timestamp-based).")
        return handle_missing_values(df)
    else:
        df = convert_wide_to_long(df)
        return handle_missing_values(df)

def convert_wide_to_long(df):
    """Converts wide-format data (one row per day, 48 half-hourly columns) to long format."""
    df = df.rename(columns={df.columns[0]: "Date"})
    time_cols = [col for col in df.columns if ":" in col]
    if not time_cols:
        raise ValueError("❌ No half-hourly time columns detected in wide format.")
    print("🔄 Converting wide format to long format.")
    df_long = df.melt(id_vars=["Date"], value_vars=time_cols, var_name="Time", value_name="Electricity_kWh")
    df_long["Timestamp"] = pd.to_datetime(df_long["Date"].astype(str) + " " + df_long["Time"])
    df_long = df_long.drop(columns=["Date", "Time"])
    return df_long

def handle_missing_values(df):
    """Fills missing values using interpolation and backfill."""
    if df.isnull().sum().sum() > 0:
        print("⚠️ Missing values detected. Filling missing data...")
        df = df.interpolate().fillna(method="bfill")
    return df

def detect_anomalies(df):
    """Detects anomalies in the full dataset using CPU-based Isolation Forest."""
    df['Hour'] = df['Timestamp'].dt.hour
    df['DayOfWeek'] = df['Timestamp'].dt.dayofweek
    features = ['Electricity_kWh', 'Hour', 'DayOfWeek']
    preprocessor = ColumnTransformer(transformers=[('num', StandardScaler(), features)]).set_output(transform="pandas")
    X_scaled = preprocessor.fit_transform(df)
    model = IsolationForest(n_estimators=100, contamination=0.05, random_state=42)
    df['Anomaly'] = model.fit_predict(X_scaled)
    return df, model, preprocessor

def save_model(model, preprocessor, filename="isolation_forest_model.pkl"):
    """Saves the Isolation Forest model and preprocessor to a file."""
    joblib.dump({"model": model, "preprocessor": preprocessor}, filename)
    print(f"✅ Model and preprocessor saved as {filename}")

# ----- Aggregated Data Functions -----
def aggregate_weekly(df, working_days=7):
    """
    Aggregates data by week and half-hour period.
    For working_days == 5, only Monday-Friday are used;
    For working_days == 7, all days are used.
    Returns an aggregated DataFrame with a 'Period' column in the form "Week X - HH:MM".
    """
    if working_days == 5:
        df_filtered = df[df['Timestamp'].dt.dayofweek < 5].copy()
    else:
        df_filtered = df.copy()
    df_filtered['Week'] = df_filtered['Timestamp'].dt.isocalendar().week
    df_filtered['TimeOfDay'] = df_filtered['Timestamp'].dt.strftime("%H:%M")
    agg_df = df_filtered.groupby(['Week', 'TimeOfDay'], as_index=False)['Electricity_kWh'].mean()
    agg_df['Period'] = "Week " + agg_df['Week'].astype(str) + " - " + agg_df['TimeOfDay']
    agg_df = agg_df.sort_values(by=['Week', 'TimeOfDay'])
    return agg_df

def aggregated_anomaly_detection(agg_df):
    """
    Runs Isolation Forest on aggregated data.
    Uses features: Electricity consumption, Week, and a simple numeric index preserving the order.
    """
    agg_df = agg_df.reset_index(drop=True)
    agg_df['Index'] = agg_df.index
    features = ['Electricity_kWh', 'Index']
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(agg_df[features])
    model = IsolationForest(n_estimators=100, contamination=0.05, random_state=42)
    agg_df['Anomaly'] = model.fit_predict(X_scaled)
    anomalies = agg_df[agg_df['Anomaly'] == -1]
    return agg_df, anomalies

def plot_aggregated_data(agg_df, scenario):
    """
    Creates an interactive Plotly line graph for the aggregated data.
    The x-axis uses the 'Period' column in the order of the DataFrame.
    Each week is shown as a separate segment, and anomalies are overlaid as red markers.
    """
    # Force the category order of 'Period' to be the DataFrame order.
    fig = px.line(agg_df, x='Period', y='Electricity_kWh',
                  title=f"Aggregated Average Consumption - {scenario} Scenario",
                  category_orders={'Period': agg_df['Period'].tolist()})
    anomalies = agg_df[agg_df['Anomaly'] == -1]
    if not anomalies.empty:
        fig.add_scatter(x=anomalies['Period'], y=anomalies['Electricity_kWh'], mode='markers',
                        marker=dict(color='red'), name='Anomalies')
    filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_{scenario}_aggregated.html")
    fig.write_html(filename)
    print(f"✅ Interactive graph for {scenario} scenario saved to {filename}")

# New Section: Plot the original full dataset as an interactive HTML chart,
# sorted by timestamp.
def plot_original_data_interactive(df, anomalies):
    """
    Creates an interactive Plotly line graph for the original full dataset.
    The data is first sorted by Timestamp.
    The original consumption is plotted as a line, and anomalies are overlaid as red markers.
    """
    df_sorted = df.sort_values(by="Timestamp")
    anomalies_sorted = anomalies.sort_values(by="Timestamp")
    fig = px.line(df_sorted, x='Timestamp', y='Electricity_kWh', title="Original Electricity Consumption")
    if not anomalies_sorted.empty:
        fig.add_scatter(x=anomalies_sorted['Timestamp'], y=anomalies_sorted['Electricity_kWh'],
                        mode='markers', marker=dict(color='red'), name='Anomalies')
    filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_original_interactive.html")
    fig.write_html(filename)
    print(f"✅ Interactive chart for original data saved to {filename}")

# ----- LLM Explanation Function -----
def explain_anomalies_llm(anomalies, model, tokenizer):
    """Generates LLM explanations for anomalies using the specified LLM."""
    explanations = []
    total = len(anomalies)
    for idx, (_, row) in enumerate(anomalies.iterrows(), start=1):
        print(f"Processing anomaly {idx}/{total}", flush=True)
        prompt = f"""Energy Consumption Analysis:

- Timestamp: {row['Timestamp']}
- Electricity Usage: {row['Electricity_kWh']} kWh
- Time of Day: {row['Hour']}:00
- Day of Week: {row['DayOfWeek']} (0=Monday, 6=Sunday)

This energy usage has been detected as an anomaly. Explain why this could be happening but keep it short and concise.
Consider the Time of Day {row['Hour']}:00, and Day of Week {row['DayOfWeek']} and if the building would be open at this time or not.
Consider what electrical system could cause this anomaly. For Example, small Electricity Usage could be lighting, large could be air handling units.
"""
        inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
        # Move inputs to the model's device
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        outputs = model.generate(**inputs, max_new_tokens=500)
        explanation = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"Response: {explanation}\n", flush=True)
        explanations.append((row['Timestamp'], row['Electricity_kWh'], explanation))
        if TEST_MODE:
            print("Test mode enabled: processed only the first anomaly.\n", flush=True)
            break
    return explanations

# ----- Main Execution Flow -----
if __name__ == "__main__":
    overall_start = time.time()

    # Step 0: Ensure Hugging Face login, check CUDA, and load the specified LLM model
    start = time.time()
    ensure_huggingface_login()
    check_cuda()  # Check and print GPU information
    the_model, tokenizer = load_model(model_name)  # Change model name if desired
    step0_time = time.time() - start

    # Step 1: Preprocess raw data and detect original anomalies
    start = time.time()
    df = preprocess_data(CSV_FILE)
    df_with_anomalies, trained_model, preprocessor = detect_anomalies(df)
    save_model(trained_model, preprocessor)
    original_anomalies = df_with_anomalies[df_with_anomalies['Anomaly'] == -1]
    # Format the Timestamp column as dd/mm/yyyy for CSV output
    original_anomalies_formatted = original_anomalies.copy()
    original_anomalies_formatted["Timestamp"] = original_anomalies_formatted["Timestamp"].dt.strftime('%d/%m/%Y')
    orig_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_original_anomalies.csv")
    original_anomalies_formatted.to_csv(orig_filename, index=False)
    print(f"✅ Original anomalies saved to {orig_filename}")
    step1_time = time.time() - start

    # Step 2: Aggregated Data Analysis
    start = time.time()
    # Scenario a: 5-day working (Monday-Friday)
    agg_5d = aggregate_weekly(df, working_days=5)
    agg_5d, anomalies_5d = aggregated_anomaly_detection(agg_5d)
    plot_aggregated_data(agg_5d, "5-day")
    agg_5d_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_5day_aggregated.csv")
    agg_5d.to_csv(agg_5d_filename, index=False)
    print(f"✅ Aggregated 5-day data saved to {agg_5d_filename}")
    # Scenario b: 7-day (all days)
    agg_7d = aggregate_weekly(df, working_days=7)
    agg_7d, anomalies_7d = aggregated_anomaly_detection(agg_7d)
    plot_aggregated_data(agg_7d, "7-day")
    agg_7d_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_7day_aggregated.csv")
    agg_7d.to_csv(agg_7d_filename, index=False)
    print(f"✅ Aggregated 7-day data saved to {agg_7d_filename}")
    # New Section: Plot the original full dataset interactively, sorted by Timestamp.
    plot_original_data_interactive(df_with_anomalies, original_anomalies)
    step2_time = time.time() - start

    # Step 3: Wait for user confirmation before proceeding with LLM explanations
    start = time.time()
    user_input = input("Aggregated anomaly detection finished. Type 'y' and press enter to continue with explanation finder: ").strip().lower()
    if user_input != 'y':
        print("Exiting script as per user request.")
        exit()
    step3_time = time.time() - start

    # Step 4: Run LLM explanation finder on the original anomalies (limited by TEST_MODE if True)
    start = time.time()
    if not original_anomalies.empty:
        explanations = explain_anomalies_llm(original_anomalies, the_model, tokenizer)
        explanation_df = pd.DataFrame(explanations, columns=["Timestamp", "Electricity_kWh", "Explanation"])
        merged_df = pd.merge(df_with_anomalies, explanation_df, on="Timestamp", how="left")
        # Format the Timestamp column as dd/mm/yyyy before saving the final CSV
        merged_df_copy = merged_df.copy()
        merged_df_copy["Timestamp"] = merged_df_copy["Timestamp"].dt.strftime('%d/%m/%Y')
        output_filename = os.path.join(ANOMALY_DIR, f"{datetime.now().strftime('%Y-%m-%d-%H%M%S')}_anomaly_explanations.csv")
        merged_df_copy.to_csv(output_filename, index=False)
        print(f"✅ Anomalies and explanations saved to {output_filename}")
    step4_time = time.time() - start

    overall_time = time.time() - overall_start

    print("\n--- Timing Summary ---")
    print(f"Step 0 (Login, CUDA check, model load): {step0_time:.2f} seconds")
    print(f"Step 1 (Preprocessing & original anomaly detection): {step1_time:.2f} seconds")
    print(f"Step 2 (Aggregated data analysis & plots): {step2_time:.2f} seconds")
    print(f"Step 3 (User confirmation): {step3_time:.2f} seconds")
    print(f"Step 4 (LLM explanation finder): {step4_time:.2f} seconds")
    print(f"Total execution time: {overall_time:.2f} seconds")
