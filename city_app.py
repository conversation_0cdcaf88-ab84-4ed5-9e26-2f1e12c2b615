from flask import Flask, render_template, send_from_directory
import webbrowser
import threading
import os

app = Flask(__name__, static_folder="static", template_folder="templates")

@app.route("/")
def index():
    return render_template("city_index.html")

@app.route("/<path:path>")
def static_proxy(path):
    return send_from_directory('static', path)

def open_browser():
    webbrowser.open("http://localhost:8080/")

if __name__ == "__main__":
    threading.Timer(1.0, open_browser).start()
    app.run(port=8080, debug=False)
