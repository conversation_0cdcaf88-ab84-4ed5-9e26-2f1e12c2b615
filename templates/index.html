<!DOCTYPE html>
<html>
<head>
  <title>Energy Model Simulation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      border: 1px solid #ccc;
      border-radius: 5px;
      background-color: white;
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
      box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
    }
    .header {
      background-color: #008080; /* dark turquoise */
      padding: 10px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      text-align: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
      margin: -20px -20px 20px -20px; /* extend header to cover container border */
    }
    .section {
      margin-bottom: 20px;
    }
    label {
      display: block;
      margin-top: 8px;
    }
    input {
      width: 100%;
      padding: 8px;
      margin-top: 4px;
      box-sizing: border-box;
    }
    button {
      background-color: #008080;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover {
      background-color: #006666;
    }
    .link {
      text-align: center;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">Energy Model Simulation</div>

    <!-- Simulation Options Form -->
    <form method="POST">
      <div class="section">
        <h2>Building Details</h2>
        <label>
          Floor Area:
          <input type="number" name="floor_area" required>
        </label>
        <label>
          Building Height:
          <input type="number" name="building_height" required>
        </label>
      </div>

      <div class="section">
        <h2>Solar PV Details</h2>
        <label>
          Solar PV Size (kWp):
          <input type="number" name="SolarPV_size" required>
        </label>
        <label>
          Orientation (N, E, S, W):
          <input type="text" name="orientation" required>
        </label>
      </div>

      <div class="section">
        <h2>Battery Details</h2>
        <label>
          Number of Batteries:
          <input type="number" name="bat_num" required>
        </label>
        <label>
          Battery Capacity (kWh):
          <input type="number" name="bat_capacity" required>
        </label>
      </div>

      <div class="section">
        <h2>Simulation Options</h2>
        <label>
          <input type="checkbox" name="multi_simulations">
          Enable Multi-Simulations
        </label>
        <label>
          Number of Simulations:
          <input type="number" name="simulation_count" value="1" min="1">
        </label>
      </div>

      <div class="section" style="text-align: center;">
        <button type="submit">Run Energy Model</button>
      </div>
    </form>

    <div class="link">
      <a href="{{ url_for('portfolio_map') }}">View Portfolio Map</a>
    </div>
  </div>
</body>
</html>
