''' VERSION CONTROL
v0.15 - to add NPV as an option, and a dotted vertical line as the carbon reduction value



'''
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import base64
from PIL import Image
import textwrap

''' IW colours
171/10/56 - brand red
23/156/154 - brand teal
139/217/0 - sustainability
200/0/0 - architecture
65/30/169 - building surveying
32/143/183 - building services
'''

# CARBON TARGET CONFIGURATION
# Set your carbon emissions reduction target here (tCO2 per year)
CARBON_TARGET = 20.0  # Change this value to your desired target

# NPV CONFIGURATION
# Discount rate for NPV calculations (typical for energy efficiency projects: 3-7%)
DISCOUNT_RATE = 0.05  # 5% discount rate
USE_NPV = True  # Set to True to use NPV-based MAC, False to use simple Net Cost

# Define mapping from CSV "Colour" names to RGB strings (for Plotly)
colour_mapping = {
    "Building Surveying": "rgb(65, 30, 169)",
    "Sustainability": "rgb(139, 217, 0)",
    "Building Services": "rgb(32, 143, 183)",
    "Architecture": "rgb(200, 0, 0)"
}

# NPV calculation function
def calculate_npv(initial_cost, annual_savings, lifetime, discount_rate):
    """
    Calculate Net Present Value of an investment
    NPV = -Initial_Cost + Sum(Annual_Savings / (1 + discount_rate)^year) for each year
    """
    if discount_rate == 0:
        return annual_savings * lifetime - initial_cost

    # Calculate present value of annual savings over the lifetime
    pv_savings = sum(annual_savings / (1 + discount_rate) ** year for year in range(1, int(lifetime) + 1))
    return pv_savings - initial_cost

# Load input data from CSV file
# CSV headers: Measure, Capital Cost, Carbon Reduction, Annual Savings, Lifetime, Colour
df = pd.read_csv("MACC_Input.csv")

# Rename 'Capital Cost' to 'Cost' for consistency
df.rename(columns={'Capital Cost': 'Cost'}, inplace=True)

# Map the textual "Colour" column to an RGB tuple and store in a new "Color" column
df["Color"] = df["Colour"].map(colour_mapping)

# 2. Calculate payback period (years)
df["Payback Period"] = df["Cost"] / df["Annual Savings"]

# 3. Calculate Net Cost, NPV & Lifetime Cost per Tonne CO2 Saved (MAC)
# Net Cost = Capital Cost - (Annual Savings * Lifetime) - simple calculation
df["Net Cost"] = df["Cost"] - (df["Annual Savings"] * df["Lifetime"])

# NPV = Present Value of future savings - Initial Cost
df["NPV"] = df.apply(lambda row: calculate_npv(row["Cost"], row["Annual Savings"],
                                               row["Lifetime"], DISCOUNT_RATE), axis=1)

# Total carbon saved over lifetime for each measure
df["LifetimeCarbonSaved"] = df["Carbon Reduction"]# * df["Lifetime"]

# Choose between NPV-based or Net Cost-based MAC calculation
if USE_NPV:
    # NPV-based MAC: Use NPV instead of Net Cost, annualized over lifetime
    df["MAC"] = (-df["NPV"] / df["Carbon Reduction"]) / df["Lifetime"]
    cost_basis = "NPV"
else:
    # Traditional Net Cost-based MAC
    df["MAC"] = (df["Net Cost"] / df["Carbon Reduction"]) / df["Lifetime"]
    cost_basis = "Net Cost"

# 4. Sort by MAC (ascending) for a left-to-right MACC
df = df.sort_values(by="MAC")

# 5. Calculate cumulative lifetime carbon savings for the x-axis
df["CumulativeCarbonSaved"] = df["LifetimeCarbonSaved"].cumsum()

# 6. Create a 'LeftEdge' so each bar starts where the previous one ended
df["LeftEdge"] = df["CumulativeCarbonSaved"] - df["LifetimeCarbonSaved"]

# 7. Create interactive Plotly chart
fig = go.Figure()

# 8. Add each measure as a separate trace using rectangles for precise positioning
for idx, row in df.iterrows():
    # Create hover text with detailed information
    hover_text = (
        f"<b>{row['Measure']}</b><br>"
        f"MAC: £{row['MAC']:.2f}/tCO2<br>"
        f"Carbon Reduction: {row['LifetimeCarbonSaved']:.1f} tCO2/year<br>"
        f"Capital Cost: £{row['Cost']:,.0f}<br>"
        f"Annual Savings: £{row['Annual Savings']:,.0f}<br>"
        f"NPV: £{row['NPV']:,.0f}<br>"
        f"Net Cost: £{row['Net Cost']:,.0f}<br>"
        f"Payback: {row['Payback Period']:.1f} years<br>"
        f"Lifetime: {row['Lifetime']} years"
    )

    # Define rectangle coordinates for precise bar positioning
    left_edge = row['LeftEdge']
    right_edge = row['LeftEdge'] + row['LifetimeCarbonSaved']
    mac_value = row['MAC']

    # Create rectangle using scatter plot with fill
    x_coords = [left_edge, right_edge, right_edge, left_edge, left_edge]
    y_coords = [0, 0, mac_value, mac_value, 0]

    # Create the filled rectangle (no hover on this trace)
    fig.add_trace(go.Scatter(
        x=x_coords,
        y=y_coords,
        fill='toself',
        fillcolor=row['Color'],
        line=dict(color='black', width=1),
        mode='lines',
        name=row['Measure'],
        showlegend=True,
        hoverinfo='skip'  # Skip hover on the outline
    ))

    # Create a grid of invisible hover points across the entire rectangle
    width = row['LifetimeCarbonSaved']
    height = abs(mac_value)

    # Determine number of hover points based on rectangle size
    num_x_points = max(3, int(width * 2))  # At least 3 points horizontally
    num_y_points = max(3, int(height / 50))  # Scale with height

    hover_x = []
    hover_y = []

    for i in range(num_x_points):
        for j in range(num_y_points):
            x_pos = left_edge + (width * i / (num_x_points - 1))
            if mac_value >= 0:
                y_pos = height * j / (num_y_points - 1)
            else:
                y_pos = -height * j / (num_y_points - 1)
            hover_x.append(x_pos)
            hover_y.append(y_pos)

    # Add invisible hover points covering the entire rectangle
    fig.add_trace(go.Scatter(
        x=hover_x,
        y=hover_y,
        mode='markers',
        marker=dict(size=8, opacity=0, color=row['Color']),  # Invisible markers with color
        name=row['Measure'],
        showlegend=False,  # Don't show in legend (duplicate)
        hovertemplate=hover_text + "<extra></extra>",
        hoverinfo='text',
        hoverlabel=dict(bgcolor=row['Color'], bordercolor='white', font_color='white')
    ))

# 8.1. Add carbon target line (vertical dotted line)
if CARBON_TARGET > 0:
    # Get approximate y-range for the target line
    y_min = df['MAC'].min() * 1.1 if df['MAC'].min() < 0 else df['MAC'].min() * 0.9
    y_max = df['MAC'].max() * 1.1

    fig.add_shape(
        type="line",
        x0=CARBON_TARGET, y0=y_min,
        x1=CARBON_TARGET, y1=y_max,
        line=dict(color="red", width=2, dash="dash"),
    )

    # Add annotation for target line
    fig.add_annotation(
        x=CARBON_TARGET,
        y=y_max * 0.9,
        text=f"Target<br>{CARBON_TARGET:.1f} tCO2/year",
        showarrow=False,
        bgcolor="white",
        bordercolor="red",
        borderwidth=1,
        font=dict(color="red", size=10)
    )

# 9. Add text annotations for measure names and carbon savings
for idx, row in df.iterrows():
    x_center = row["LeftEdge"] + row["LifetimeCarbonSaved"] / 2

    # Add measure name above/below bar
    wrapped_label = textwrap.fill(row["Measure"], width=15)
    y_pos = row["MAC"]

    fig.add_annotation(
        x=x_center,
        y=y_pos,
        text=wrapped_label,
        showarrow=False,
        yshift=15 if y_pos >= 0 else -15,
        font=dict(size=8, color="black"),
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="rgba(0,0,0,0.2)",
        borderwidth=1
    )

    # Add carbon savings annotation near x-axis
    if USE_NPV:
        is_roi = row["NPV"] >= 0
    else:
        is_roi = row["Net Cost"] <= 0

    y_offset = 15 if is_roi else -15

    fig.add_annotation(
        x=x_center,
        y=0,
        text=f"{row['LifetimeCarbonSaved']:.1f}",
        showarrow=False,
        yshift=y_offset,
        font=dict(size=8, color="black"),
        bgcolor="rgba(255,255,255,0.8)"
    )

# 10. Configure chart layout and styling
title_suffix = f" - {cost_basis} Based" if USE_NPV else ""

# Add logo as base64 encoded image
try:
    # Load and encode logo
    logo_path = "E:\\Python\\Energy Model\\BEMMS\\assets\\J&H Logo (circular).png"
    with open(logo_path, "rb") as f:
        logo_data = base64.b64encode(f.read()).decode()

    # Add logo to layout
    fig.add_layout_image(
        dict(
            source=f"data:image/png;base64,{logo_data}",
            xref="paper", yref="paper",
            x=0.02, y=0.98,
            sizex=0.1, sizey=0.1,
            xanchor="left", yanchor="top"
        )
    )
except:
    print("Logo file not found - continuing without logo")

# Update layout
fig.update_layout(
    title=dict(
        text=f"Marginal Abatement Cost Curve (MACC){title_suffix}",
        x=0.5,
        font=dict(size=16)
    ),
    xaxis=dict(
        title="Abatement Potential (tCO2 per year)",
        showgrid=False,
        zeroline=True,
        zerolinecolor='black',
        zerolinewidth=1,
        range=[-0.1, df['CumulativeCarbonSaved'].max() + 0.1]  # Start from 0 with small padding
    ),
    yaxis=dict(
        title="MAC (£/tCO2)",
        showgrid=True,
        gridcolor='lightgray',
        gridwidth=1,
        zeroline=True,
        zerolinecolor='black',
        zerolinewidth=2
    ),
    width=1200,
    height=600,
    showlegend=True,
    legend=dict(
        orientation="v",
        yanchor="top",
        y=1,
        xanchor="left",
        x=1.02,
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="black",
        borderwidth=1
    ),
    hovermode='closest',
    plot_bgcolor='white',
    hoverlabel=dict(
        bgcolor="rgba(255,255,255,0.9)",  # Default background (will be overridden by trace colors)
        bordercolor="black",
        font_size=12,
        font_family="Arial"
    )
)

# 11. Save as interactive HTML file and display
fig.write_html("MACC_Interactive.html")
fig.show()

# 12. Print measures with negative and positive return on investment
if USE_NPV:
    # Use NPV for ROI analysis
    non_roi_measures = df[df["NPV"] < 0]   # non-ROI: negative NPV
    roi_measures = df[df["NPV"] >= 0]      # ROI: positive or zero NPV

    print(f"Analysis based on NPV (Discount Rate: {DISCOUNT_RATE:.1%}):")
    print("\nMeasures with negative NPV (non-ROI):")
    print(non_roi_measures[["Measure", "NPV", "Net Cost"]].round(2))

    print("\nMeasures with positive NPV (ROI):")
    print(roi_measures[["Measure", "NPV", "Net Cost"]].round(2))
else:
    # Use Net Cost for ROI analysis
    non_roi_measures = df[df["Net Cost"] > 0]   # non-ROI: cost exceeds lifetime savings
    roi_measures = df[df["Net Cost"] <= 0]      # ROI: investment pays back or saves more than it costs

    print("Analysis based on Net Cost:")
    print("\nMeasures with negative return on investment (non-ROI):")
    print(non_roi_measures[["Measure", "Net Cost", "NPV"]].round(2))

    print("\nMeasures with positive return on investment (ROI):")
    print(roi_measures[["Measure", "Net Cost", "NPV"]].round(2))
