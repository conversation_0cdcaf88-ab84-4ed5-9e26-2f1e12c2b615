import folium
import math
import json
import webbrowser
import os
import csv
import time
from datetime import datetime
from pathlib import Path
from geopy.geocoders import Nominatim
from folium.plugins import FastMarkerCluster
import plotly.offline as pyo
import plotly.graph_objects as go

# -------------------------------
# Load (or create) the geocoding cache.
cache_file = "geocode_cache.json"
if os.path.exists(cache_file):
    with open(cache_file, "r", encoding="utf-8") as cf:
        geocode_cache = json.load(cf)
else:
    geocode_cache = {}

# Initialize the geocoder.
geolocator = Nominatim(user_agent="energy_model_app")

def haversine(lon1, lat1, lon2, lat2):
    from math import radians, sin, cos, sqrt, atan2
    R = 6371
    dlon = radians(lon2 - lon1)
    dlat = radians(lat2 - lat1)
    a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    return R * c

# -------------------------------
# Load building basic info from Portfolio.csv.
# Get the directory where this script is located
script_dir = os.path.dirname(os.path.abspath(__file__))
# Go up one level to the parent directory, then into Portfolio folder
portfolio_csv_path = os.path.join(os.path.dirname(script_dir), "Portfolio", "Portfolio.csv")
buildings = []
with open(portfolio_csv_path, "r", newline="", encoding="utf-8-sig") as f:
    reader = csv.DictReader(f)
    for row in reader:
        if "Name" not in row or not row["Name"].strip():
            continue
        building = {}
        building["name"] = row["Name"].strip()
        postcode = row["Postcode"].strip()
        # Check cache first.
        if postcode in geocode_cache:
            location = geocode_cache[postcode]
            print(f"Loaded geocode for {postcode} from cache: {location}")
        else:
            try:
                location_obj = geolocator.geocode(postcode)
                time.sleep(1)  # Respect usage policy.
            except Exception as e:
                location_obj = None
            if location_obj is not None:
                location = {"lat": location_obj.latitude, "lon": location_obj.longitude}
                geocode_cache[postcode] = location
                with open(cache_file, "w", encoding="utf-8") as cf:
                    json.dump(geocode_cache, cf)
                print(f"Geocoded {postcode}: {location}")
            else:
                print(f"Warning: Could not geocode postcode {postcode} for building {building['name']}")
                location = None
        if location is None:
            continue
        building["lat"] = location["lat"]
        building["lon"] = location["lon"]

        building["area"] = float(row["GIA"])  # already in m²
        building["floors"] = int(row["Floors"])
        building["annual_elec"] = int(row["Annual Electricity Consumption"])
        building["annual_gas"] = int(row["Annual Gas Consumption"])
        building["epc"] = row["EPC Rating"].strip()

        # Define CSV file paths.
        daily_filename = os.path.join(os.path.dirname(script_dir), "Portfolio", f"{building['name']}_daily.csv")
        monthly_filename = os.path.join(os.path.dirname(script_dir), "Portfolio", f"{building['name']}_monthly.csv")

        # Check for daily data first.
        if os.path.exists(daily_filename):
            building["source_frequency"] = "daily"
            daily_records = []
            with open(daily_filename, "r", newline="", encoding="utf-8-sig") as df:
                dreader = csv.DictReader(df)
                for drow in dreader:
                    date_str = drow["Date"].strip()  # Expected "dd/mm/yyyy"
                    try:
                        dt_obj = datetime.strptime(date_str, "%d/%m/%Y")
                    except Exception as e:
                        continue
                    try:
                        elec = int(drow["Electricity"])
                    except:
                        elec = None
                    try:
                        gas = int(drow["Gas"])
                    except:
                        gas = None
                    daily_records.append({
                        "date_str": date_str,
                        "dt_obj": dt_obj,
                        "elec": elec,
                        "gas": gas
                    })
            building["daily_records"] = daily_records
            building["daily_dates"] = [r["date_str"] for r in daily_records]
            building["daily_elec"] = [r["elec"] for r in daily_records]
            building["daily_gas"] = [r["gas"] for r in daily_records]

            # Calculate monthly data from daily records.
            monthly_agg = {}
            for rec in daily_records:
                month_key = rec["dt_obj"].strftime("%b %Y")
                if month_key not in monthly_agg:
                    monthly_agg[month_key] = {"elec": 0, "gas": 0}
                if rec["elec"] is not None:
                    monthly_agg[month_key]["elec"] += rec["elec"]
                if rec["gas"] is not None:
                    monthly_agg[month_key]["gas"] += rec["gas"]
            sorted_months = sorted(monthly_agg.keys(), key=lambda m: datetime.strptime(m, "%b %Y"))
            monthly_labels = []
            monthly_elec = []
            monthly_gas = []
            for m in sorted_months:
                monthly_labels.append(m)
                monthly_elec.append(monthly_agg[m]["elec"])
                monthly_gas.append(monthly_agg[m]["gas"])
            building["aggregated_monthly"] = {"months": monthly_labels, "elec": monthly_elec, "gas": monthly_gas}

        # Otherwise, if no daily data exists, check for monthly data.
        elif os.path.exists(monthly_filename):
            building["source_frequency"] = "monthly"
            monthly_data = {}
            with open(monthly_filename, "r", newline="", encoding="utf-8-sig") as mf:
                mreader = csv.DictReader(mf)
                for mrow in mreader:
                    month = mrow["Month"].strip()
                    try:
                        elec = int(mrow["Electricity"])
                    except:
                        elec = None
                    try:
                        gas = int(mrow["Gas"])
                    except:
                        gas = None
                    monthly_data[month] = {"Electricity": elec, "Gas": gas}
            # Use a fixed month order.
            months_order = ["January", "February", "March", "April", "May", "June",
                            "July", "August", "September", "October", "November", "December"]
            monthly_labels = []
            monthly_elec = []
            monthly_gas = []
            for m in months_order:
                if m in monthly_data:
                    monthly_labels.append(m)
                    monthly_elec.append(monthly_data[m]["Electricity"])
                    monthly_gas.append(monthly_data[m]["Gas"])
            building["monthly_labels"] = monthly_labels
            building["monthly_elec"] = monthly_elec
            building["monthly_gas"] = monthly_gas
        else:
            building["source_frequency"] = None

        buildings.append(building)

# -------------------------------
# Create a Folium map centered over London.
if buildings:
    m = folium.Map(location=[buildings[0]["lat"], buildings[0]["lon"]], zoom_start=12)
else:
    m = folium.Map(location=[51.5074, -0.1278], zoom_start=12)
hq = buildings[0] if buildings else None

# Create a FastMarkerCluster.
marker_cluster = FastMarkerCluster(data=[]).add_to(m)

# -------------------------------
# Build popups for each building.
# (Half-hourly functionality has been removed.)
for i, building in enumerate(buildings):
    b_id = f"b{i}"
    area_m2 = building["area"]
    area_formatted = f"{area_m2:,.0f}"
    floors_formatted = f"{building['floors']:,}"
    annual_elec_formatted = f"{building['annual_elec']:,}"
    annual_gas_formatted = f"{building['annual_gas']:,}"

    if building.get("source_frequency") == "daily":
        daily_labels_js = json.dumps(building["daily_dates"])
        daily_elec_js = json.dumps(building["daily_elec"])
        daily_gas_js = json.dumps(building["daily_gas"])
        monthly_labels_js = json.dumps(building["aggregated_monthly"]["months"])
        monthly_elec_js = json.dumps(building["aggregated_monthly"]["elec"])
        monthly_gas_js = json.dumps(building["aggregated_monthly"]["gas"])
        available_buttons = {"daily": True, "monthly": True}
    elif building.get("source_frequency") == "monthly":
        monthly_labels_js = json.dumps(building["monthly_labels"])
        monthly_elec_js = json.dumps(building["monthly_elec"])
        monthly_gas_js = json.dumps(building["monthly_gas"])
        available_buttons = {"monthly": True}
    else:
        available_buttons = {"daily": False, "monthly": False}

    daily_button_html = ""
    monthly_button_html = ""
    daily_detail_div = ""
    monthly_detail_div = ""

    if available_buttons.get("daily"):
        daily_button_html = f'<button onclick="showDetail(\'{b_id}\', \'daily\')">View Daily Consumption</button>'
        daily_detail_div = f"""
    <div id="detail-content-{b_id}-daily" style="display:none; border:1px solid #ccc; border-radius:5px; background-color:white; padding:10px; font-family:'Century Gothic', sans-serif;">
      <h4 style="background-color:#e6f7ff; padding:5px; margin:0; font-size:20px; text-decoration:underline;">Daily Consumption for {building['name']}</h4>
      <div style="width:400px; height:300px;">
        <canvas id="chart-{b_id}-daily" width="400" height="300"></canvas>
      </div>
      <br>
      <button onclick="hideDetail('{b_id}', 'daily')">Back</button>
    </div>
    """
    if available_buttons.get("monthly"):
        monthly_button_html = f'<button onclick="showDetail(\'{b_id}\', \'monthly\')">View Monthly Consumption</button>'
        monthly_detail_div = f"""
    <div id="detail-content-{b_id}-monthly" style="display:none; border:1px solid #ccc; border-radius:5px; background-color:white; padding:10px; font-family:'Century Gothic', sans-serif;">
      <h4 style="background-color:#e6f7ff; padding:5px; margin:0; font-size:20px; text-decoration:underline;">Monthly Consumption for {building['name']}</h4>
      <div style="width:400px; height:300px;">
        <canvas id="chart-{b_id}-monthly" width="400" height="300"></canvas>
      </div>
      <br>
      <button onclick="hideDetail('{b_id}', 'monthly')">Back</button>
    </div>
    """

    consumption_buttons = " ".join([btn for btn in [daily_button_html, monthly_button_html] if btn])

    # Popup with updated styling: white background overall; header with dark turquoise background, white bold text.
    popup_html = f"""
    <!-- Load Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      console.log("Chart.js loaded:", Chart);
    </script>
    <div id="popup-container-{b_id}" style="border:1px solid #ccc; border-radius:5px; background-color:white; font-family:'Century Gothic', sans-serif;">
      <div id="header-{b_id}" style="background-color:#008080; padding:10px; border-top-left-radius:5px; border-top-right-radius:5px;">
        <h4 style="color:white; font-weight:bold; margin:0; font-size:20px;">{building['name']}</h4>
      </div>
      <div id="content-{b_id}" style="padding:10px;">
        <p><b>Gross Internal Area:</b> {area_formatted} m<sup>2</sup></p>
        <p><b>Number of Floors:</b> {floors_formatted}</p>
        <p><b>Annual Electricity Consumption:</b> {annual_elec_formatted} kWh</p>
        <p><b>Annual Gas Consumption:</b> {annual_gas_formatted} kWh</p>
        <p><b>EPC Rating:</b> {building['epc']}</p>
        {consumption_buttons}
      </div>
    </div>
    {daily_detail_div}
    {monthly_detail_div}
    <script>
    function showDetail(b_id, freq) {{
        document.getElementById("popup-container-" + b_id).style.display = "none";
        document.getElementById("detail-content-" + b_id + "-" + freq).style.display = "block";
        if(!window["chartInitialized_" + b_id + "_" + freq]) {{
            var ctx = document.getElementById("chart-" + b_id + "-" + freq).getContext('2d');
            var chart;
            if(freq === "daily") {{
                chart = new Chart(ctx, {{
                    type: 'line',
                    data: {{
                        labels: {daily_labels_js},
                        datasets: [
                            {{
                                label: 'Electricity Consumption',
                                data: {daily_elec_js},
                                fill: false,
                                borderColor: 'blue',
                                pointRadius: 0
                            }},
                            {{
                                label: 'Gas Consumption',
                                data: {daily_gas_js},
                                fill: false,
                                borderColor: 'red',
                                pointRadius: 0
                            }}
                        ]
                    }},
                    options: {{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {{
                            x: {{
                                ticks: {{
                                    autoSkip: true,
                                    maxTicksLimit: 10,
                                    callback: function(value, index, values) {{
                                        var parts = value.split('/');
                                        if(parts.length === 3 && parts[0] === "01") {{
                                            var monthMap = {{
                                                "01": "January", "02": "February", "03": "March", "04": "April",
                                                "05": "May", "06": "June", "07": "July", "08": "August",
                                                "09": "September", "10": "October", "11": "November", "12": "December"
                                            }};
                                            return monthMap[parts[1]];
                                        }} else {{
                                            return "";
                                        }}
                                    }}
                                }}
                            }},
                            y: {{
                                beginAtZero: true
                            }}
                        }},
                        plugins: {{
                            tooltip: {{
                                callbacks: {{
                                    label: function(context) {{
                                        var label = context.dataset.label || '';
                                        if(label) {{
                                            label += ': ';
                                        }}
                                        if(context.parsed.y !== null) {{
                                            label += context.parsed.y.toLocaleString();
                                        }}
                                        return label;
                                    }}
                                }}
                            }}
                        }}
                    }}
                }});
            }} else if(freq === "monthly") {{
                chart = new Chart(ctx, {{
                    type: 'line',
                    data: {{
                        labels: {monthly_labels_js},
                        datasets: [
                            {{
                                label: 'Electricity Consumption',
                                data: {monthly_elec_js},
                                fill: false,
                                borderColor: 'blue'
                            }},
                            {{
                                label: 'Gas Consumption',
                                data: {monthly_gas_js},
                                fill: false,
                                borderColor: 'red'
                            }}
                        ]
                    }},
                    options: {{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {{
                            x: {{
                                ticks: {{ autoSkip: false }}
                            }},
                            y: {{
                                beginAtZero: true
                            }}
                        }},
                        plugins: {{
                            tooltip: {{
                                callbacks: {{
                                    label: function(context) {{
                                        var label = context.dataset.label || '';
                                        if(label) {{
                                            label += ': ';
                                        }}
                                        if(context.parsed.y !== null) {{
                                            label += context.parsed.y.toLocaleString();
                                        }}
                                        return label;
                                    }}
                                }}
                            }}
                        }}
                    }}
                }});
            }}
            window["chartInitialized_" + b_id + "_" + freq] = true;
            window["chart_" + b_id + "_" + freq] = chart;
        }} else {{
            window["chart_" + b_id + "_" + freq].resize();
        }}
    }}
    function hideDetail(b_id, freq) {{
        document.getElementById("detail-content-" + b_id + "-" + freq).style.display = "none";
        document.getElementById("popup-container-" + b_id).style.display = "block";
    }}
    </script>
    """
    iframe = folium.IFrame(html=popup_html, width=450, height=450)
    popup = folium.Popup(iframe, max_width=450)
    marker = folium.Marker(
        location=[building["lat"], building["lon"]],
        popup=popup,
        tooltip=building["name"],
        icon=folium.Icon(color="red" if i == 0 else "blue", icon="info-sign")
    )
    marker_cluster.add_child(marker)
    if i != 0 and hq is not None:
        distance = haversine(hq["lon"], hq["lat"], building["lon"], building["lat"])
        folium.PolyLine(
            locations=[[hq["lat"], hq["lon"]], [building["lat"], building["lon"]]],
            tooltip=f"Distance: {distance:,.2f} km",
            color="green"
        ).add_to(m)

m.save("london_buildings_portfolio.html")
print("Map has been saved to london_buildings_portfolio.html")
webbrowser.open("london_buildings_portfolio.html")
