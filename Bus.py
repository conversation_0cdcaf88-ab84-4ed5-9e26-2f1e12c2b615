import folium
import openrouteservice
from openrouteservice import convert
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# ----------------------
# 1. SETUP AND SETTINGS
# ----------------------

# OpenRouteService API key
ORS_API_KEY = "5b3ce3597851110001cf6248537adba153aa4a22bdd67c4bf93b34bf"

# Start and end locations (example: London to Reading)
start_coords = (51.5074, -0.1278)  # London
end_coords = (51.4545, -0.9781)    # Reading

# Vehicle parameters
mass = 13000  # kg
drag_coefficient = 0.6
frontal_area = 8.0  # m²
air_density = 1.225  # kg/m³
rolling_resistance_coeff = 0.008
regen_efficiency = 0.6
engine_efficiency = 0.35

# Battery and fuel parameters
battery_capacity_kwh = 100
initial_battery_soc_kwh = 80
fuel_energy_density_mj_per_litre = 35.8
fuel_co2_kg_per_litre = 2.68

# ----------------------
# 2. GET ROUTE + ELEVATION
# ----------------------

client = openrouteservice.Client(key=ORS_API_KEY)

# Get base driving route
route = client.directions(
    coordinates=[(start_coords[1], start_coords[0]), (end_coords[1], end_coords[0])],
    profile='driving-car',
    format='geojson'
)

# Add elevation to route geometry
elevation_route = client.elevation_line(
    format_in='geojson',
    geometry=route['features'][0]['geometry']
)
coordinates = elevation_route['geometry']['coordinates']

# Get route summary data
distance_m = route['features'][0]['properties']['segments'][0]['distance']
duration_s = route['features'][0]['properties']['segments'][0]['duration']

# ----------------------
# 3. ENERGY MODEL CALC
# ----------------------

n_segments = 10
segment_distance = distance_m / n_segments
segment_time = duration_s / n_segments
segment_coords = np.linspace(0, len(coordinates) - 1, n_segments + 1, dtype=int)

battery_soc_kwh = initial_battery_soc_kwh
total_fuel_used_l = 0
total_co2_kg = 0
total_energy = 0
rows = []

for i in range(n_segments):
    start_idx = segment_coords[i]
    end_idx = segment_coords[i + 1]
    coord_start = coordinates[start_idx]
    coord_end = coordinates[end_idx]

    v_initial = (distance_m / duration_s) * (i / n_segments)
    v_final = (distance_m / duration_s) * ((i + 1) / n_segments)
    a = (v_final - v_initial) / segment_time

    delta_ke = 0.5 * mass * (v_final ** 2 - v_initial ** 2)
    avg_speed = (v_initial + v_final) / 2
    drag_force = 0.5 * air_density * drag_coefficient * frontal_area * avg_speed ** 2
    rolling_force = mass * 9.81 * rolling_resistance_coeff
    resistive_work = (drag_force + rolling_force) * segment_distance

    elevation_start = coord_start[2]
    elevation_end = coord_end[2]
    delta_elevation = elevation_end - elevation_start
    potential_energy = mass * 9.81 * delta_elevation

    if delta_ke < 0:
        regen_energy = -delta_ke * regen_efficiency
        delta_ke = 0
    else:
        regen_energy = 0

    segment_energy = (delta_ke + resistive_work + potential_energy - regen_energy) / engine_efficiency
    segment_energy_mj = segment_energy / 1e6

    # Battery contribution
    energy_from_battery_mj = min(battery_soc_kwh * 3.6, segment_energy_mj)
    battery_soc_kwh -= energy_from_battery_mj / 3.6

    # Fuel usage if battery insufficient
    fuel_energy_needed_mj = segment_energy_mj - energy_from_battery_mj
    fuel_used_l = fuel_energy_needed_mj / fuel_energy_density_mj_per_litre if fuel_energy_needed_mj > 0 else 0
    co2_emitted = fuel_used_l * fuel_co2_kg_per_litre

    # Regen charging
    if regen_energy > 0:
        regen_kwh = regen_energy / 3.6e6
        battery_soc_kwh = min(battery_soc_kwh + regen_kwh, battery_capacity_kwh)

    total_fuel_used_l += fuel_used_l
    total_co2_kg += co2_emitted
    total_energy += segment_energy

    rows.append({
        "Segment": i + 1,
        "Start Coord": coord_start,
        "End Coord": coord_end,
        "v_initial (m/s)": round(v_initial, 2),
        "v_final (m/s)": round(v_final, 2),
        "Acceleration (m/s²)": round(a, 2),
        "Kinetic Energy (J)": round(delta_ke, 2),
        "Drag Force (N)": round(drag_force, 2),
        "Rolling Resistance (N)": round(rolling_force, 2),
        "Resistive Work (J)": round(resistive_work, 2),
        "Elevation Change (m)": round(delta_elevation, 2),
        "Potential Energy (J)": round(potential_energy, 2),
        "Regenerated Energy (J)": round(regen_energy, 2),
        "Segment Energy (MJ)": round(segment_energy_mj, 2),
        "Energy From Battery (MJ)": round(energy_from_battery_mj, 2),
        "Battery SoC (kWh)": round(battery_soc_kwh, 2),
        "Fuel Used (L)": round(fuel_used_l, 2),
        "CO2 Emitted (kg)": round(co2_emitted, 2),
        "Formula Description": "E = (ΔKE + drag + roll + PE - Regen) / efficiency"
    })

# ----------------------
# 4. EXPORT CSV
# ----------------------

df = pd.DataFrame(rows)
df.to_csv("hybrid_bus_energy_segments.csv", index=False)

# ----------------------
# 5. CREATE INTERACTIVE MAP
# ----------------------

m = folium.Map(location=start_coords, zoom_start=10)
folium.Marker(start_coords[::-1], tooltip="Start").add_to(m)
folium.Marker(end_coords[::-1], tooltip="End").add_to(m)
folium.PolyLine(locations=[(lat, lon) for lon, lat, *_ in coordinates], color="blue").add_to(m)
m.save("hybrid_bus_route.html")

# ----------------------
# 6. STATIC MAP IMAGE
# ----------------------

lats = [lat for lon, lat, *_ in coordinates]
lons = [lon for lon, lat, *_ in coordinates]

plt.figure(figsize=(8, 6))
plt.plot(lons, lats, marker='o', linestyle='-', label='Route')
plt.title("Hybrid Bus Route")
plt.xlabel("Longitude")
plt.ylabel("Latitude")
plt.grid(True)
plt.legend()
plt.savefig("hybrid_bus_route.png")

# ----------------------
# 7. FINAL SUMMARY
# ----------------------

total_energy_mj = total_energy / 1e6
print(f"✅ Total distance: {distance_m/1000:.2f} km")
print(f"🔋 Final battery SoC: {battery_soc_kwh:.2f} kWh")
print(f"⛽ Fuel used: {total_fuel_used_l:.2f} L")
print(f"🌍 CO₂ emitted: {total_co2_kg:.2f} kg")
print("📄 Outputs saved:")
print("- hybrid_bus_energy_segments.csv")
print("- hybrid_bus_route.html")
print("- hybrid_bus_route.png")
