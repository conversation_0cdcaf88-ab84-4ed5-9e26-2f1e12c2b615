
import csv

def dict_to_csv(dictionary, filename):
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        write_dict_to_csv(writer, dictionary)

def write_dict_to_csv(writer, dictionary, parent_key=''):
    for key, value in dictionary.items():
        if isinstance(value, dict):
            new_parent_key = f"{parent_key}{key}_" if parent_key else key
            write_dict_to_csv(writer, value, parent_key=new_parent_key)
        elif isinstance(value, list):
            write_list_to_csv(writer, value, parent_key=key)
        else:
            writer.writerow([f"{parent_key}{key}", value])

def write_list_to_csv(writer, lst, parent_key=''):
    for index, item in enumerate(lst):
        if isinstance(item, dict):
            new_parent_key = f"{parent_key}_{index}_" if parent_key else f"{index}_"
            write_dict_to_csv(writer, item, parent_key=new_parent_key)
        elif isinstance(item, list):
            write_list_to_csv(writer, item, parent_key=f"{parent_key}{index}_")
        else:
            writer.writerow([f"{parent_key}{index}", item])

def insert_half_hourly(data):
    data_half_hourly = []
    for i in range(len(data)):
        # Include current data point only at the beginning or end
        # if i == 0 or i == len(data) - 1:
        #     data_half_hourly.append(data[i])
        # else:
            # Insert half-value before the current data point
            data_half_hourly.append(data[i - 1] / 2)
            data_half_hourly.append(data[i] / 2)

    return data_half_hourly