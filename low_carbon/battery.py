from calculations.variables import *
from calculations.calculations import *

class Events:
    def __init__(self, event_hold, event_full, event_charge, event_discharge, event_export):
        self.event_hold = event_hold
        self.event_full = event_full
        self.event_charge = event_charge
        self.event_discharge = event_discharge
        self.event_export = event_export

    def battery_event_hold(self):
        self.event_hold += 1

    def battery_event_full(self):
        self.event_full += 1

    def battery_event_charge(self):
        self.event_charge += 1

    def battery_event_discharge(self):
        self.event_discharge += 1

    def battery_event_export(self):
        self.event_export += 1

battery_events = Events(0, 0, 0, 0, 0)

def get_efficiency(Temperature):
    if Battery_efficiency_fixed:
        efficiency = 1.0
        return efficiency
    """Get the efficiency for a given temperature."""
    if Temperature <= -10 or Temperature >= 41:
        return "Temperature is outside the valid range."

    # Efficiency values for each increment temperature
    Temperature_values = [-10, 0, 5, 10, 15, 20, 25, 30, 35, 40, 50]
    efficiency_values = [0.85, 0.88, 0.90, 0.92, 0.94, 0.96, 0.96, 0.92, 0.90, 0.85, 0.80]

    # Find the nearest temperature values
    lower_temp = max(t for t in Temperature_values if t <= Temperature)
    upper_temp = min(t for t in Temperature_values if t >= Temperature)

    # Check if lower and upper temperature values are equal
    if lower_temp == upper_temp:
        efficiency = efficiency_values[Temperature_values.index(lower_temp)]
    else:
        # Find the corresponding efficiency values
        lower_efficiency = efficiency_values[Temperature_values.index(lower_temp)]
        upper_efficiency = efficiency_values[Temperature_values.index(upper_temp)]

        # Interpolate the efficiency for the given temperature
        efficiency = interpolate(Temperature, lower_temp, lower_efficiency, upper_temp, upper_efficiency)

    return efficiency
