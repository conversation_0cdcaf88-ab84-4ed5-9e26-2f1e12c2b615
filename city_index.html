<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Interactive 3D City Model</title>
  <script src="Cesium/Cesium.js"></script>
  <link href="Cesium/Widgets/widgets.css" rel="stylesheet" />
  <style>
    html, body, #cesiumContainer { width: 100%; height: 100%; margin: 0; padding: 0; }
    #infoBox {
      position: absolute;
      top: 10px; left: 10px;
      background: white; padding: 10px;
      border: 1px solid #aaa; display: none;
      z-index: 999;
    }
  </style>
</head>
<body>
<div id="cesiumContainer"></div>
<div id="infoBox"></div>

<script>
  // Initialize the Cesium viewer
  const viewer = new Cesium.Viewer('cesiumContainer', {
    terrainProvider: Cesium.createWorldTerrain(),
    selectionIndicator: true,
    infoBox: false
  });

  // London coordinates
  const londonCoords = { lon: -0.126, lat: 51.5074, height: 0 };
  const infoBox = document.getElementById('infoBox');

  // Energy data for buildings
  const energyData = {
    "City": {
      totalEnergy: "12,500 MWh",
      population: "8.9 million",
      buildings: "87,235",
      carbonEmissions: "5.6 million tons CO2e",
      renewablePercentage: "22%"
    },
    "Commercial District": {
      totalEnergy: "4,200 MWh",
      buildingType: "Office",
      occupancy: "85%",
      rating: "EPC B",
      peakDemand: "1.2 MW"
    },
    "Residential Area": {
      totalEnergy: "3,800 MWh",
      buildingType: "Residential",
      occupancy: "92%",
      rating: "EPC C",
      peakDemand: "0.9 MW"
    }
  };

  // Load the low poly city model
  const modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(
    Cesium.Cartesian3.fromDegrees(londonCoords.lon, londonCoords.lat, londonCoords.height)
  );

  const cityModel = viewer.scene.primitives.add(Cesium.Model.fromGltf({
    url: 'assets/low_poly_city/scene.gltf',
    modelMatrix: modelMatrix,
    scale: 100.0, // Adjust scale as needed
    minimumPixelSize: 128
  }));

  cityModel.readyPromise.then(() => {
    // Activate any animations
    cityModel.activeAnimations.addAll();
    
    // Set the model ID for picking
    cityModel.id = "City";
    
    // Fly to the model
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(londonCoords.lon, londonCoords.lat - 0.01, 500),
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-30),
        roll: 0.0
      }
    });
  });

  // Add click handler for building information
  viewer.screenSpaceEventHandler.setInputAction(function onClick(movement) {
    const picked = viewer.scene.pick(movement.position);
    if (Cesium.defined(picked) && picked.id) {
      const label = picked.id;
      const data = energyData[label];
      if (data) {
        let html = `<h4>${label}</h4>`;
        for (const [key, value] of Object.entries(data)) {
          html += `${key}: ${value}<br>`;
        }
        infoBox.innerHTML = html;
        infoBox.style.display = 'block';
      }
    } else {
      infoBox.style.display = 'none';
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // Add some additional entities to represent different districts
  const commercialDistrict = viewer.entities.add({
    name: 'Commercial District',
    position: Cesium.Cartesian3.fromDegrees(londonCoords.lon + 0.003, londonCoords.lat + 0.002, 50),
    ellipse: {
      semiMinorAxis: 100,
      semiMajorAxis: 100,
      material: Cesium.Color.BLUE.withAlpha(0.3),
      outline: true,
      outlineColor: Cesium.Color.BLUE
    },
    id: "Commercial District"
  });

  const residentialArea = viewer.entities.add({
    name: 'Residential Area',
    position: Cesium.Cartesian3.fromDegrees(londonCoords.lon - 0.003, londonCoords.lat - 0.002, 50),
    ellipse: {
      semiMinorAxis: 100,
      semiMajorAxis: 100,
      material: Cesium.Color.GREEN.withAlpha(0.3),
      outline: true,
      outlineColor: Cesium.Color.GREEN
    },
    id: "Residential Area"
  });
</script>
</body>
</html>
