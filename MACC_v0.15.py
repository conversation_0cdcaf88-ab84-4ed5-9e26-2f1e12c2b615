''' VERSION CONTROL
v0.15 - to add NPV as an option, and a dotted vertical line as the carbon reduction value



'''
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.offsetbox import OffsetImage, AnnotationBbox
import textwrap

''' IW colours
171/10/56 - brand red
23/156/154 - brand teal
139/217/0 - sustainability
200/0/0 - architecture
65/30/169 - building surveying
32/143/183 - building services
'''

# CARBON TARGET CONFIGURATION
# Set your carbon emissions reduction target here (tCO2 per year)
CARBON_TARGET = 20.0  # Change this value to your desired target

# Define mapping from CSV "Colour" names to RGB tuples
colour_mapping = {
    "Building Surveying": (65 / 255, 30 / 255, 169 / 255),
    "Sustainability": (139 / 255, 217 / 255, 0 / 255),
    "Building Services": (32 / 255, 143 / 255, 183 / 255),
    "Architecture": (200 / 255, 0 / 255, 0 / 255)
}

# Load input data from CSV file
# CSV headers: Measure, Capital Cost, Carbon Reduction, Annual Savings, Lifetime, Colour
df = pd.read_csv("MACC_Input.csv")

# Rename 'Capital Cost' to 'Cost' for consistency
df.rename(columns={'Capital Cost': 'Cost'}, inplace=True)

# Map the textual "Colour" column to an RGB tuple and store in a new "Color" column
df["Color"] = df["Colour"].map(colour_mapping)

# 2. Calculate payback period (years)
df["Payback Period"] = df["Cost"] / df["Annual Savings"]

# 3. Calculate Net Cost & Lifetime Cost per Tonne CO2 Saved (MAC)
# Net Cost = Capital Cost - (Annual Savings * Lifetime)
# Add NPV next
df["Net Cost"] = df["Cost"] - (df["Annual Savings"] * df["Lifetime"])
# Total carbon saved over lifetime for each measure

df["LifetimeCarbonSaved"] = df["Carbon Reduction"]# * df["Lifetime"]

# Lifetime cost per tonne CO2 saved
df["MAC"] = (df["Net Cost"] / df["Carbon Reduction"]) / df["Lifetime"]

# 4. Sort by MAC (ascending) for a left-to-right MACC
df = df.sort_values(by="MAC")

# 5. Calculate cumulative lifetime carbon savings for the x-axis
df["CumulativeCarbonSaved"] = df["LifetimeCarbonSaved"].cumsum()

# 6. Create a 'LeftEdge' so each bar starts where the previous one ended
df["LeftEdge"] = df["CumulativeCarbonSaved"] - df["LifetimeCarbonSaved"]

# 7. Generate a list of colors for the bars based on the mapped RGB values
colors = df["Color"].tolist()

# 8. Plot the MACC with variable bar width
plt.figure(figsize=(12, 6))
bars = plt.bar(
    x=df["LeftEdge"],
    height=df["MAC"],
    width=df["LifetimeCarbonSaved"],
    align='edge',
    color=colors,
    edgecolor='black'
)

# 8.1. Add carbon target line (vertical dotted line)
if CARBON_TARGET > 0:
    # Get the y-axis limits to draw the line across the full height
    y_min, y_max = plt.ylim()
    plt.axvline(x=CARBON_TARGET, color='red', linestyle='--', linewidth=2, alpha=0.8)
    # Add a text annotation for the target line
    plt.text(CARBON_TARGET, y_max * 0.9, f'Target\n{CARBON_TARGET:.1f} tCO2/year',
             ha='center', va='top', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8),
             fontsize=9, color='red')

# 9. Label each bar with the measure name (wrapping text if too long)
max_chars_per_line = 15  # adjust as needed for label wrapping
for bar, label in zip(bars, df["Measure"]):
    wrapped_label = textwrap.fill(label, width=max_chars_per_line)
    x_pos = bar.get_x() + bar.get_width() / 2
    y_pos = bar.get_height()
    plt.text(
        x=x_pos,
        y=y_pos,
        s=wrapped_label,
        ha='center',
        va='bottom' if y_pos >= 0 else 'top',
        rotation=0,
        fontsize=8
    )

# 10. Annotate each measure with its individual lifetime carbon savings value.
ax = plt.gca()
for idx, row in df.iterrows():
    x_center = row["LeftEdge"] + row["LifetimeCarbonSaved"] / 2
    # For ROI measures (Net Cost <= 0, bars below x-axis), place label above x-axis.
    # For non-ROI measures (Net Cost > 0, bars above x-axis), place label below x-axis.
    if row["Net Cost"] <= 0:
        offset = 10  # points upward
        va = 'bottom'
    else:
        offset = -10  # points downward
        va = 'top'
    ax.annotate(
        f"{row['LifetimeCarbonSaved']:.1f}",
        xy=(x_center, 0),
        xytext=(0, offset),
        textcoords="offset points",
        ha='center',
        va=va,
        fontsize=8,
        color='black'
    )

plt.xlabel("Abatement Potential (tCO2 per year)")
plt.ylabel("MAC (£/tCO2)")
plt.title("Marginal Abatement Cost Curve (MACC)")
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 11. Add the logo to the top left of the chart
logo = plt.imread("E:\\Python\\Energy Model\\BEMMS\\assets\\J&H Logo (circular).png")
#logo = plt.imread("C:\\Users\\<USER>\\OneDrive - Ingleton Wood LLP\\Documents\\Python\\IW_logo.png")
imagebox = OffsetImage(logo, zoom=0.02)  # adjust zoom factor as needed - IW was 0.5
ab = AnnotationBbox(imagebox, (0.05, 0.95), xycoords='axes fraction', frameon=False)
plt.gca().add_artist(ab)

plt.tight_layout()
plt.show()

# 12. Print measures with negative return on investment (non-ROI) and those with positive return on investment (ROI)
non_roi_measures = df[df["Net Cost"] > 0]   # non-ROI: cost exceeds lifetime savings
roi_measures = df[df["Net Cost"] <= 0]        # ROI: investment pays back or saves more than it costs

print("Measures with negative return on investment (non-ROI):")
print(non_roi_measures[["Measure", "Net Cost"]])

print("\nMeasures with positive return on investment (ROI):")
print(roi_measures[["Measure", "Net Cost"]])
